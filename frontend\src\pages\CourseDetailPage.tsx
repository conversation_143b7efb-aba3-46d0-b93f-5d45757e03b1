import React, { useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { 
  ArrowLeft, 
  Clock, 
  Users, 
  Star, 
  BookOpen, 
  Play, 
  Heart, 
  Share2,
  Award,
  CheckCircle,
  MessageSquare
} from 'lucide-react';
import { Button } from '@/components/ui/Button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Badge } from '@/components/ui/Badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/Avatar';
import { ProgressBar } from '@/components/ui/ProgressBar';
import { generateInitials } from '@/lib/utils';
import toast from 'react-hot-toast';

// Mock course data
const mockCourse = {
  id: 'devops-1',
  title: 'DevOps Fundamentals',
  description: 'Aprende los principios fundamentales de DevOps, incluyendo CI/CD, containerización con Docker, orquestación con Kubernetes, y automatización de infraestructura. Este curso te proporcionará las habilidades necesarias para implementar prácticas DevOps en tu organización.',
  category: 'Tecnología',
  level: 'intermediate',
  duration: 8,
  studentCount: 156,
  rating: 4.8,
  reviewCount: 42,
  creator: { 
    id: '1', 
    firstName: 'Prof. García', 
    lastName: 'García',
    avatar: '',
    bio: 'Ingeniero DevOps con 10+ años de experiencia en empresas Fortune 500'
  },
  status: 'published',
  price: 0,
  language: 'Español',
  lastUpdated: '2024-01-15',
  prerequisites: ['Conocimientos básicos de Linux', 'Experiencia con línea de comandos'],
  learningObjectives: [
    'Implementar pipelines de CI/CD',
    'Containerizar aplicaciones con Docker',
    'Orquestar servicios con Kubernetes',
    'Automatizar infraestructura como código',
    'Monitorear y optimizar sistemas'
  ],
  modules: [
    {
      id: '1',
      title: 'Introducción a DevOps',
      duration: 45,
      lessons: 6,
      completed: true
    },
    {
      id: '2',
      title: 'Control de Versiones con Git',
      duration: 60,
      lessons: 8,
      completed: true
    },
    {
      id: '3',
      title: 'CI/CD con Jenkins',
      duration: 90,
      lessons: 12,
      completed: false,
      current: true
    },
    {
      id: '4',
      title: 'Containerización con Docker',
      duration: 75,
      lessons: 10,
      completed: false
    },
    {
      id: '5',
      title: 'Orquestación con Kubernetes',
      duration: 120,
      lessons: 15,
      completed: false
    }
  ],
  expertReview: {
    expert: {
      name: 'Dr. Ana Martínez',
      title: 'Senior DevOps Architect',
      avatar: ''
    },
    rating: 'excellent',
    comment: 'Excelente curso que cubre todos los aspectos fundamentales de DevOps. La progresión es lógica y los ejemplos prácticos son muy valiosos.'
  },
  enrolled: true,
  progress: 65,
  saved: false
};

const ratingLabels = {
  'very-bad': 'Muy Malo',
  'bad': 'Malo',
  'neutral': 'Neutral',
  'good': 'Bueno',
  'excellent': 'Excelente'
};

const ratingColors = {
  'very-bad': 'text-red-600',
  'bad': 'text-orange-600',
  'neutral': 'text-yellow-600',
  'good': 'text-blue-600',
  'excellent': 'text-green-600'
};

export default function CourseDetailPage() {
  const { id } = useParams();
  const navigate = useNavigate();
  const [course] = useState(mockCourse);
  const [isEnrolling, setIsEnrolling] = useState(false);
  const [isSaved, setIsSaved] = useState(course.saved);

  const handleEnroll = async () => {
    setIsEnrolling(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));
      toast.success('¡Te has inscrito exitosamente al curso!');
      navigate('/my-courses');
    } catch (error) {
      toast.error('Error al inscribirse al curso');
    } finally {
      setIsEnrolling(false);
    }
  };

  const handleSave = () => {
    setIsSaved(!isSaved);
    toast.success(isSaved ? 'Curso removido de guardados' : 'Curso guardado exitosamente');
  };

  const handleShare = () => {
    navigator.clipboard.writeText(window.location.href);
    toast.success('Enlace copiado al portapapeles');
  };

  const completedModules = course.modules.filter(m => m.completed).length;
  const totalModules = course.modules.length;

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Header */}
      <div className="mb-8">
        <Button
          variant="ghost"
          onClick={() => navigate(-1)}
          className="mb-4"
        >
          <ArrowLeft className="w-4 h-4 mr-2" />
          Volver
        </Button>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2">
            {/* Course Header */}
            <div className="mb-6">
              <div className="flex items-center space-x-2 mb-3">
                <Badge variant="outline">{course.category}</Badge>
                <Badge variant={course.level === 'beginner' ? 'green' : course.level === 'intermediate' ? 'yellow' : 'red'}>
                  {course.level === 'beginner' ? 'Principiante' : course.level === 'intermediate' ? 'Intermedio' : 'Avanzado'}
                </Badge>
              </div>

              <h1 className="text-3xl font-bold text-gray-900 mb-4">
                {course.title}
              </h1>

              <p className="text-lg text-gray-600 mb-6">
                {course.description}
              </p>

              {/* Course Stats */}
              <div className="flex items-center space-x-6 text-sm text-gray-600">
                <div className="flex items-center">
                  <Clock className="w-4 h-4 mr-1" />
                  {course.duration} horas
                </div>
                <div className="flex items-center">
                  <Users className="w-4 h-4 mr-1" />
                  {course.studentCount} estudiantes
                </div>
                <div className="flex items-center">
                  <Star className="w-4 h-4 mr-1 text-yellow-500" />
                  {course.rating} ({course.reviewCount} reseñas)
                </div>
                <div className="flex items-center">
                  <BookOpen className="w-4 h-4 mr-1" />
                  {course.modules.length} módulos
                </div>
              </div>
            </div>

            {/* Expert Review */}
            {course.expertReview && (
              <Card className="mb-6 border-green-200 bg-green-50">
                <CardHeader>
                  <CardTitle className="flex items-center text-green-800">
                    <Award className="w-5 h-5 mr-2" />
                    Revisión de Experto
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex items-start space-x-4">
                    <Avatar className="w-12 h-12">
                      <AvatarImage src={course.expertReview.expert.avatar} />
                      <AvatarFallback className="bg-green-500 text-white">
                        {generateInitials(course.expertReview.expert.name)}
                      </AvatarFallback>
                    </Avatar>
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-2">
                        <h4 className="font-medium text-green-900">
                          {course.expertReview.expert.name}
                        </h4>
                        <Badge className={`${ratingColors[course.expertReview.rating]} bg-transparent border-current`}>
                          {ratingLabels[course.expertReview.rating]}
                        </Badge>
                      </div>
                      <p className="text-sm text-green-700 mb-1">
                        {course.expertReview.expert.title}
                      </p>
                      <p className="text-green-800">
                        "{course.expertReview.comment}"
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Learning Objectives */}
            <Card className="mb-6">
              <CardHeader>
                <CardTitle>¿Qué aprenderás?</CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2">
                  {course.learningObjectives.map((objective, index) => (
                    <li key={index} className="flex items-start">
                      <CheckCircle className="w-5 h-5 text-green-500 mr-3 mt-0.5 flex-shrink-0" />
                      <span>{objective}</span>
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>

            {/* Prerequisites */}
            {course.prerequisites.length > 0 && (
              <Card className="mb-6">
                <CardHeader>
                  <CardTitle>Prerrequisitos</CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2">
                    {course.prerequisites.map((prerequisite, index) => (
                      <li key={index} className="flex items-start">
                        <div className="w-2 h-2 bg-gray-400 rounded-full mr-3 mt-2 flex-shrink-0" />
                        <span>{prerequisite}</span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            )}

            {/* Course Modules */}
            <Card>
              <CardHeader>
                <CardTitle>Contenido del Curso</CardTitle>
                {course.enrolled && (
                  <div className="text-sm text-gray-600">
                    Progreso: {completedModules} de {totalModules} módulos completados
                  </div>
                )}
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {course.modules.map((module, index) => (
                    <div
                      key={module.id}
                      className={`p-4 border rounded-lg ${
                        module.current ? 'border-blue-500 bg-blue-50' : 'border-gray-200'
                      }`}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                            module.completed 
                              ? 'bg-green-500 text-white' 
                              : module.current 
                                ? 'bg-blue-500 text-white'
                                : 'bg-gray-200 text-gray-600'
                          }`}>
                            {module.completed ? (
                              <CheckCircle className="w-4 h-4" />
                            ) : (
                              index + 1
                            )}
                          </div>
                          <div>
                            <h4 className="font-medium text-gray-900">
                              {module.title}
                            </h4>
                            <div className="text-sm text-gray-500">
                              {module.lessons} lecciones • {module.duration} min
                            </div>
                          </div>
                        </div>
                        
                        {course.enrolled && (
                          <Button
                            variant={module.current ? "default" : "outline"}
                            size="sm"
                            disabled={!module.completed && !module.current}
                          >
                            {module.completed ? 'Revisar' : module.current ? 'Continuar' : 'Bloqueado'}
                          </Button>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="lg:col-span-1">
            <div className="sticky top-8 space-y-6">
              {/* Enrollment Card */}
              <Card>
                <CardContent className="p-6">
                  {course.enrolled ? (
                    <div className="space-y-4">
                      <div className="text-center">
                        <h3 className="text-lg font-semibold text-gray-900 mb-2">
                          Tu Progreso
                        </h3>
                        <div className="text-3xl font-bold text-blue-600 mb-2">
                          {course.progress}%
                        </div>
                        <ProgressBar value={course.progress} className="mb-4" />
                        <p className="text-sm text-gray-600">
                          {completedModules} de {totalModules} módulos completados
                        </p>
                      </div>
                      
                      <Button className="w-full" size="lg">
                        <Play className="w-4 h-4 mr-2" />
                        Continuar Aprendiendo
                      </Button>
                      
                      <Button 
                        variant="outline" 
                        className="w-full"
                        onClick={() => navigate(`/courses/${course.id}/forum`)}
                      >
                        <MessageSquare className="w-4 h-4 mr-2" />
                        Ir al Foro
                      </Button>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      <div className="text-center">
                        <div className="text-3xl font-bold text-green-600 mb-2">
                          Gratis
                        </div>
                        <p className="text-sm text-gray-600">
                          Acceso completo al curso
                        </p>
                      </div>
                      
                      <Button 
                        className="w-full" 
                        size="lg"
                        onClick={handleEnroll}
                        disabled={isEnrolling}
                      >
                        {isEnrolling ? 'Inscribiendo...' : 'Inscribirse Ahora'}
                      </Button>
                    </div>
                  )}

                  <div className="flex space-x-2 mt-4">
                    <Button
                      variant="outline"
                      size="sm"
                      className="flex-1"
                      onClick={handleSave}
                    >
                      <Heart className={`w-4 h-4 mr-2 ${isSaved ? 'fill-current text-red-500' : ''}`} />
                      {isSaved ? 'Guardado' : 'Guardar'}
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      className="flex-1"
                      onClick={handleShare}
                    >
                      <Share2 className="w-4 h-4 mr-2" />
                      Compartir
                    </Button>
                  </div>
                </CardContent>
              </Card>

              {/* Instructor Info */}
              <Card>
                <CardHeader>
                  <CardTitle>Instructor</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex items-start space-x-4">
                    <Avatar className="w-12 h-12">
                      <AvatarImage src={course.creator.avatar} />
                      <AvatarFallback className="bg-blue-500 text-white">
                        {generateInitials(course.creator.firstName, course.creator.lastName)}
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <h4 className="font-medium text-gray-900">
                        {course.creator.firstName} {course.creator.lastName}
                      </h4>
                      <p className="text-sm text-gray-600 mt-1">
                        {course.creator.bio}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Course Info */}
              <Card>
                <CardHeader>
                  <CardTitle>Información del Curso</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Idioma:</span>
                    <span className="font-medium">{course.language}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Última actualización:</span>
                    <span className="font-medium">
                      {new Date(course.lastUpdated).toLocaleDateString()}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Estudiantes:</span>
                    <span className="font-medium">{course.studentCount}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Calificación:</span>
                    <div className="flex items-center">
                      <Star className="w-4 h-4 text-yellow-500 mr-1" />
                      <span className="font-medium">{course.rating}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
