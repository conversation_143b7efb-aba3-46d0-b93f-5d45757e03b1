import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { ArrowLeft, Save, Sparkles } from 'lucide-react';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Switch } from '@/components/ui/Switch';
import { Badge } from '@/components/ui/Badge';
import toast from 'react-hot-toast';

const courseSchema = z.object({
  title: z.string().min(3, 'El título debe tener al menos 3 caracteres'),
  code: z.string().optional(),
  category: z.string().min(1, 'Selecciona una categoría'),
  description: z.string().optional(),
  passingScore: z.number().min(0).max(100),
  timeLimit: z.number().min(15).max(300),
  attempts: z.string(),
  feedbackMode: z.string(),
  tags: z.string().optional(),
});

type CourseForm = z.infer<typeof courseSchema>;

const categories = [
  'Tecnología',
  'Idiomas',
  'Matemáticas',
  'Ciencias',
  'Historia',
  'Arte y Diseño',
  'Negocios',
  'Medicina',
  'Otro',
];

const assessmentTypes = [
  {
    id: 'multiple-choice',
    name: 'Preguntas de opción múltiple',
    description: 'Preguntas con múltiples opciones de respuesta',
    enabled: true,
  },
  {
    id: 'essay',
    name: 'Preguntas de desarrollo',
    description: 'Respuestas escritas extensas',
    enabled: true,
  },
  {
    id: 'multimedia',
    name: 'Preguntas con multimedia',
    description: 'Incluye audio, video o imágenes',
    enabled: true,
  },
  {
    id: 'practical',
    name: 'Preguntas prácticas',
    description: 'Ejercicios hands-on o simulaciones',
    enabled: false,
  },
];

export default function CourseCreationPage() {
  const navigate = useNavigate();
  const [assessmentSettings, setAssessmentSettings] = useState(assessmentTypes);
  const [aiGeneration, setAiGeneration] = useState(true);
  const [isLoading, setIsLoading] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
  } = useForm<CourseForm>({
    resolver: zodResolver(courseSchema),
    defaultValues: {
      passingScore: 70,
      timeLimit: 90,
      attempts: '1',
      feedbackMode: 'after-submission',
    },
  });

  const toggleAssessmentType = (id: string) => {
    setAssessmentSettings(prev =>
      prev.map(type =>
        type.id === id ? { ...type, enabled: !type.enabled } : type
      )
    );
  };

  const onSubmit = async (data: CourseForm) => {
    setIsLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      const enabledAssessments = assessmentSettings
        .filter(type => type.enabled)
        .map(type => type.name);

      toast.success(
        `¡Curso "${data.title}" creado exitosamente!\n\n` +
        `Código: ${data.code || 'Auto-generado'}\n` +
        `Categoría: ${data.category}\n` +
        `Tipos de evaluación: ${enabledAssessments.join(', ')}\n\n` +
        `Ahora puedes comenzar a agregar preguntas y configurar exámenes.`
      );
      
      navigate('/admin/courses');
    } catch (error) {
      toast.error('Error al crear el curso');
    } finally {
      setIsLoading(false);
    }
  };

  const saveDraft = async () => {
    toast.success('Borrador guardado exitosamente');
  };

  return (
    <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center space-x-4 mb-4">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => navigate(-1)}
          >
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Crear Nuevo Curso</h1>
            <p className="text-gray-600">Configura un nuevo curso para tus estudiantes</p>
          </div>
        </div>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-8">
        {/* Basic Information */}
        <Card>
          <CardHeader>
            <CardTitle>Información Básica</CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="md:col-span-2">
                <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-2">
                  Nombre del Curso *
                </label>
                <Input
                  id="title"
                  placeholder="Introducción a la Programación"
                  {...register('title')}
                  className={errors.title ? 'border-red-500' : ''}
                />
                {errors.title && (
                  <p className="mt-1 text-sm text-red-600">{errors.title.message}</p>
                )}
              </div>

              <div>
                <label htmlFor="code" className="block text-sm font-medium text-gray-700 mb-2">
                  Código del Curso
                </label>
                <Input
                  id="code"
                  placeholder="PROG-101"
                  {...register('code')}
                />
              </div>

              <div>
                <label htmlFor="category" className="block text-sm font-medium text-gray-700 mb-2">
                  Categoría *
                </label>
                <select
                  id="category"
                  {...register('category')}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">Seleccionar categoría</option>
                  {categories.map(category => (
                    <option key={category} value={category}>
                      {category}
                    </option>
                  ))}
                </select>
                {errors.category && (
                  <p className="mt-1 text-sm text-red-600">{errors.category.message}</p>
                )}
              </div>

              <div className="md:col-span-2">
                <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-2">
                  Descripción
                </label>
                <textarea
                  id="description"
                  rows={3}
                  placeholder="Describe los objetivos del curso, metodología y qué aprenderán los estudiantes..."
                  {...register('description')}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Assessment Types */}
        <Card>
          <CardHeader>
            <CardTitle>Tipos de Evaluación</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {assessmentSettings.map((type) => (
                <div key={type.id} className="flex items-center justify-between">
                  <div>
                    <h4 className="text-sm font-medium text-gray-900">{type.name}</h4>
                    <p className="text-sm text-gray-500">{type.description}</p>
                  </div>
                  <Switch
                    checked={type.enabled}
                    onCheckedChange={() => toggleAssessmentType(type.id)}
                  />
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Assessment Configuration */}
        <Card>
          <CardHeader>
            <CardTitle>Configuración de Evaluación</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label htmlFor="passingScore" className="block text-sm font-medium text-gray-700 mb-2">
                  Puntuación mínima para aprobar (%)
                </label>
                <Input
                  id="passingScore"
                  type="number"
                  min="0"
                  max="100"
                  {...register('passingScore', { valueAsNumber: true })}
                />
              </div>

              <div>
                <label htmlFor="timeLimit" className="block text-sm font-medium text-gray-700 mb-2">
                  Tiempo límite por defecto (minutos)
                </label>
                <Input
                  id="timeLimit"
                  type="number"
                  min="15"
                  max="300"
                  {...register('timeLimit', { valueAsNumber: true })}
                />
              </div>

              <div>
                <label htmlFor="attempts" className="block text-sm font-medium text-gray-700 mb-2">
                  Intentos permitidos
                </label>
                <select
                  id="attempts"
                  {...register('attempts')}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="1">1 intento</option>
                  <option value="2">2 intentos</option>
                  <option value="3">3 intentos</option>
                  <option value="unlimited">Ilimitados</option>
                </select>
              </div>

              <div>
                <label htmlFor="feedbackMode" className="block text-sm font-medium text-gray-700 mb-2">
                  Modo de feedback
                </label>
                <select
                  id="feedbackMode"
                  {...register('feedbackMode')}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="immediate">Inmediato</option>
                  <option value="after-submission">Después del envío</option>
                  <option value="manual">Solo manual</option>
                </select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Tags */}
        <Card>
          <CardHeader>
            <CardTitle>Organización</CardTitle>
          </CardHeader>
          <CardContent>
            <div>
              <label htmlFor="tags" className="block text-sm font-medium text-gray-700 mb-2">
                Etiquetas
              </label>
              <Input
                id="tags"
                placeholder="programación, python, algoritmos"
                {...register('tags')}
              />
              <p className="mt-1 text-xs text-gray-500">Separar con comas</p>
            </div>
          </CardContent>
        </Card>

        {/* AI Integration */}
        <Card className="border-blue-200 bg-blue-50">
          <CardHeader>
            <CardTitle className="flex items-center text-blue-900">
              <Sparkles className="w-5 h-5 mr-2" />
              Integración con IA
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-blue-800 font-medium">Generar preguntas automáticamente</p>
                <p className="text-xs text-blue-600">Crear 20 preguntas iniciales usando IA</p>
                <p className="text-xs text-blue-600 mt-1">Costo estimado: ~200 tokens IA</p>
              </div>
              <Switch
                checked={aiGeneration}
                onCheckedChange={setAiGeneration}
              />
            </div>
          </CardContent>
        </Card>

        {/* Action Buttons */}
        <div className="flex justify-between pt-6 border-t">
          <Button
            type="button"
            variant="outline"
            onClick={() => navigate(-1)}
          >
            Cancelar
          </Button>
          <div className="flex space-x-3">
            <Button
              type="button"
              variant="outline"
              onClick={saveDraft}
              disabled={isLoading}
            >
              <Save className="w-4 h-4 mr-2" />
              Guardar Borrador
            </Button>
            <Button
              type="submit"
              disabled={isLoading}
            >
              {isLoading ? 'Creando...' : 'Crear Curso'}
            </Button>
          </div>
        </div>
      </form>
    </div>
  );
}
