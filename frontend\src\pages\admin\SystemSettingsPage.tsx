import React, { useState } from 'react';
import { Save, RefreshCw, Shield, Mail, Globe, Database, Bell, Palette } from 'lucide-react';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Switch } from '@/components/ui/Switch';
import { Badge } from '@/components/ui/Badge';
import toast from 'react-hot-toast';

interface SystemSettings {
  general: {
    siteName: string;
    siteDescription: string;
    defaultLanguage: string;
    timezone: string;
    maintenanceMode: boolean;
    registrationEnabled: boolean;
  };
  email: {
    smtpHost: string;
    smtpPort: number;
    smtpUsername: string;
    smtpPassword: string;
    fromEmail: string;
    fromName: string;
    emailVerificationRequired: boolean;
  };
  security: {
    passwordMinLength: number;
    passwordRequireUppercase: boolean;
    passwordRequireNumbers: boolean;
    passwordRequireSymbols: boolean;
    sessionTimeout: number;
    maxLoginAttempts: number;
    twoFactorEnabled: boolean;
  };
  notifications: {
    emailNotifications: boolean;
    pushNotifications: boolean;
    digestFrequency: 'daily' | 'weekly' | 'monthly';
    notifyOnNewUser: boolean;
    notifyOnCourseCompletion: boolean;
  };
  appearance: {
    primaryColor: string;
    secondaryColor: string;
    darkModeEnabled: boolean;
    customLogo: string;
    customFavicon: string;
  };
  features: {
    aiQuestionGeneration: boolean;
    forumEnabled: boolean;
    groupsEnabled: boolean;
    certificatesEnabled: boolean;
    analyticsEnabled: boolean;
  };
}

const defaultSettings: SystemSettings = {
  general: {
    siteName: 'Arroyo University',
    siteDescription: 'Plataforma de aprendizaje y evaluación',
    defaultLanguage: 'es',
    timezone: 'America/Mexico_City',
    maintenanceMode: false,
    registrationEnabled: false,
  },
  email: {
    smtpHost: 'smtp.gmail.com',
    smtpPort: 587,
    smtpUsername: '',
    smtpPassword: '',
    fromEmail: '<EMAIL>',
    fromName: 'Arroyo University',
    emailVerificationRequired: true,
  },
  security: {
    passwordMinLength: 8,
    passwordRequireUppercase: true,
    passwordRequireNumbers: true,
    passwordRequireSymbols: false,
    sessionTimeout: 24,
    maxLoginAttempts: 5,
    twoFactorEnabled: false,
  },
  notifications: {
    emailNotifications: true,
    pushNotifications: false,
    digestFrequency: 'weekly',
    notifyOnNewUser: true,
    notifyOnCourseCompletion: true,
  },
  appearance: {
    primaryColor: '#3B82F6',
    secondaryColor: '#10B981',
    darkModeEnabled: true,
    customLogo: '',
    customFavicon: '',
  },
  features: {
    aiQuestionGeneration: true,
    forumEnabled: true,
    groupsEnabled: true,
    certificatesEnabled: false,
    analyticsEnabled: true,
  },
};

export default function SystemSettingsPage() {
  const [settings, setSettings] = useState<SystemSettings>(defaultSettings);
  const [activeTab, setActiveTab] = useState<keyof SystemSettings>('general');
  const [isSaving, setIsSaving] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);

  const updateSetting = (section: keyof SystemSettings, key: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        [key]: value,
      },
    }));
    setHasChanges(true);
  };

  const handleSave = async () => {
    setIsSaving(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));
      setHasChanges(false);
      toast.success('Configuración guardada exitosamente');
    } catch (error) {
      toast.error('Error al guardar la configuración');
    } finally {
      setIsSaving(false);
    }
  };

  const handleReset = () => {
    setSettings(defaultSettings);
    setHasChanges(false);
    toast.info('Configuración restablecida a valores por defecto');
  };

  const tabs = [
    { id: 'general', label: 'General', icon: Globe },
    { id: 'email', label: 'Email', icon: Mail },
    { id: 'security', label: 'Seguridad', icon: Shield },
    { id: 'notifications', label: 'Notificaciones', icon: Bell },
    { id: 'appearance', label: 'Apariencia', icon: Palette },
    { id: 'features', label: 'Características', icon: Database },
  ];

  const renderTabContent = () => {
    switch (activeTab) {
      case 'general':
        return (
          <div className="space-y-6">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Nombre del Sitio
                </label>
                <Input
                  value={settings.general.siteName}
                  onChange={(e) => updateSetting('general', 'siteName', e.target.value)}
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Idioma por Defecto
                </label>
                <select
                  value={settings.general.defaultLanguage}
                  onChange={(e) => updateSetting('general', 'defaultLanguage', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="es">Español</option>
                  <option value="en">English</option>
                  <option value="fr">Français</option>
                </select>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Descripción del Sitio
              </label>
              <textarea
                rows={3}
                value={settings.general.siteDescription}
                onChange={(e) => updateSetting('general', 'siteDescription', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Zona Horaria
              </label>
              <select
                value={settings.general.timezone}
                onChange={(e) => updateSetting('general', 'timezone', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="America/Mexico_City">Ciudad de México (GMT-6)</option>
                <option value="America/New_York">Nueva York (GMT-5)</option>
                <option value="Europe/Madrid">Madrid (GMT+1)</option>
                <option value="UTC">UTC (GMT+0)</option>
              </select>
            </div>

            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="text-sm font-medium text-gray-900">Modo de Mantenimiento</h4>
                  <p className="text-sm text-gray-500">Desactiva el acceso al sitio para mantenimiento</p>
                </div>
                <Switch
                  checked={settings.general.maintenanceMode}
                  onCheckedChange={(checked) => updateSetting('general', 'maintenanceMode', checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <h4 className="text-sm font-medium text-gray-900">Registro Habilitado</h4>
                  <p className="text-sm text-gray-500">Permite el auto-registro de usuarios</p>
                </div>
                <Switch
                  checked={settings.general.registrationEnabled}
                  onCheckedChange={(checked) => updateSetting('general', 'registrationEnabled', checked)}
                />
              </div>
            </div>
          </div>
        );

      case 'email':
        return (
          <div className="space-y-6">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Servidor SMTP
                </label>
                <Input
                  value={settings.email.smtpHost}
                  onChange={(e) => updateSetting('email', 'smtpHost', e.target.value)}
                  placeholder="smtp.gmail.com"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Puerto SMTP
                </label>
                <Input
                  type="number"
                  value={settings.email.smtpPort}
                  onChange={(e) => updateSetting('email', 'smtpPort', parseInt(e.target.value))}
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Usuario SMTP
                </label>
                <Input
                  value={settings.email.smtpUsername}
                  onChange={(e) => updateSetting('email', 'smtpUsername', e.target.value)}
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Contraseña SMTP
                </label>
                <Input
                  type="password"
                  value={settings.email.smtpPassword}
                  onChange={(e) => updateSetting('email', 'smtpPassword', e.target.value)}
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Email Remitente
                </label>
                <Input
                  type="email"
                  value={settings.email.fromEmail}
                  onChange={(e) => updateSetting('email', 'fromEmail', e.target.value)}
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Nombre Remitente
                </label>
                <Input
                  value={settings.email.fromName}
                  onChange={(e) => updateSetting('email', 'fromName', e.target.value)}
                />
              </div>
            </div>

            <div className="flex items-center justify-between">
              <div>
                <h4 className="text-sm font-medium text-gray-900">Verificación de Email Requerida</h4>
                <p className="text-sm text-gray-500">Los usuarios deben verificar su email</p>
              </div>
              <Switch
                checked={settings.email.emailVerificationRequired}
                onCheckedChange={(checked) => updateSetting('email', 'emailVerificationRequired', checked)}
              />
            </div>
          </div>
        );

      case 'security':
        return (
          <div className="space-y-6">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Longitud Mínima de Contraseña
                </label>
                <Input
                  type="number"
                  min="6"
                  max="20"
                  value={settings.security.passwordMinLength}
                  onChange={(e) => updateSetting('security', 'passwordMinLength', parseInt(e.target.value))}
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Tiempo de Sesión (horas)
                </label>
                <Input
                  type="number"
                  min="1"
                  max="168"
                  value={settings.security.sessionTimeout}
                  onChange={(e) => updateSetting('security', 'sessionTimeout', parseInt(e.target.value))}
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Máximo Intentos de Login
              </label>
              <Input
                type="number"
                min="3"
                max="10"
                value={settings.security.maxLoginAttempts}
                onChange={(e) => updateSetting('security', 'maxLoginAttempts', parseInt(e.target.value))}
                className="w-32"
              />
            </div>

            <div className="space-y-4">
              <h4 className="text-sm font-medium text-gray-900">Requisitos de Contraseña</h4>
              
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-700">Requiere mayúsculas</span>
                <Switch
                  checked={settings.security.passwordRequireUppercase}
                  onCheckedChange={(checked) => updateSetting('security', 'passwordRequireUppercase', checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-700">Requiere números</span>
                <Switch
                  checked={settings.security.passwordRequireNumbers}
                  onCheckedChange={(checked) => updateSetting('security', 'passwordRequireNumbers', checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-700">Requiere símbolos</span>
                <Switch
                  checked={settings.security.passwordRequireSymbols}
                  onCheckedChange={(checked) => updateSetting('security', 'passwordRequireSymbols', checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <span className="text-sm text-gray-700">Autenticación de Dos Factores</span>
                  <p className="text-xs text-gray-500">Habilita 2FA para todos los usuarios</p>
                </div>
                <Switch
                  checked={settings.security.twoFactorEnabled}
                  onCheckedChange={(checked) => updateSetting('security', 'twoFactorEnabled', checked)}
                />
              </div>
            </div>
          </div>
        );

      case 'features':
        return (
          <div className="space-y-6">
            <h3 className="text-lg font-medium text-gray-900">Características de la Plataforma</h3>
            
            <div className="space-y-4">
              <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                <div>
                  <h4 className="text-sm font-medium text-gray-900">Generación de Preguntas con IA</h4>
                  <p className="text-sm text-gray-500">Permite generar preguntas automáticamente</p>
                </div>
                <Switch
                  checked={settings.features.aiQuestionGeneration}
                  onCheckedChange={(checked) => updateSetting('features', 'aiQuestionGeneration', checked)}
                />
              </div>

              <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                <div>
                  <h4 className="text-sm font-medium text-gray-900">Foros de Cursos</h4>
                  <p className="text-sm text-gray-500">Habilita discusiones en los cursos</p>
                </div>
                <Switch
                  checked={settings.features.forumEnabled}
                  onCheckedChange={(checked) => updateSetting('features', 'forumEnabled', checked)}
                />
              </div>

              <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                <div>
                  <h4 className="text-sm font-medium text-gray-900">Grupos de Estudio</h4>
                  <p className="text-sm text-gray-500">Permite crear y unirse a grupos</p>
                </div>
                <Switch
                  checked={settings.features.groupsEnabled}
                  onCheckedChange={(checked) => updateSetting('features', 'groupsEnabled', checked)}
                />
              </div>

              <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                <div>
                  <h4 className="text-sm font-medium text-gray-900">Certificados</h4>
                  <p className="text-sm text-gray-500">Genera certificados al completar cursos</p>
                </div>
                <Switch
                  checked={settings.features.certificatesEnabled}
                  onCheckedChange={(checked) => updateSetting('features', 'certificatesEnabled', checked)}
                />
              </div>

              <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                <div>
                  <h4 className="text-sm font-medium text-gray-900">Analíticas Avanzadas</h4>
                  <p className="text-sm text-gray-500">Recopila métricas detalladas de uso</p>
                </div>
                <Switch
                  checked={settings.features.analyticsEnabled}
                  onCheckedChange={(checked) => updateSetting('features', 'analyticsEnabled', checked)}
                />
              </div>
            </div>
          </div>
        );

      default:
        return <div>Contenido no disponible</div>;
    }
  };

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Configuración del Sistema</h1>
            <p className="text-gray-600 mt-1">
              Administra la configuración global de la plataforma
            </p>
          </div>
          <div className="flex items-center space-x-3">
            {hasChanges && (
              <Badge variant="yellow">Cambios sin guardar</Badge>
            )}
            <Button
              variant="outline"
              onClick={handleReset}
              disabled={isSaving}
            >
              <RefreshCw className="w-4 h-4 mr-2" />
              Restablecer
            </Button>
            <Button
              onClick={handleSave}
              disabled={isSaving || !hasChanges}
            >
              <Save className="w-4 h-4 mr-2" />
              {isSaving ? 'Guardando...' : 'Guardar Cambios'}
            </Button>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
        {/* Sidebar */}
        <div className="lg:col-span-1">
          <nav className="space-y-1">
            {tabs.map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id as keyof SystemSettings)}
                  className={`w-full flex items-center px-3 py-2 text-sm font-medium rounded-md ${
                    activeTab === tab.id
                      ? 'bg-blue-100 text-blue-700'
                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                  }`}
                >
                  <Icon className="w-5 h-5 mr-3" />
                  {tab.label}
                </button>
              );
            })}
          </nav>
        </div>

        {/* Content */}
        <div className="lg:col-span-3">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                {tabs.find(t => t.id === activeTab)?.icon && (
                  React.createElement(tabs.find(t => t.id === activeTab)!.icon, {
                    className: "w-5 h-5 mr-2"
                  })
                )}
                {tabs.find(t => t.id === activeTab)?.label}
              </CardTitle>
            </CardHeader>
            <CardContent>
              {renderTabContent()}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
