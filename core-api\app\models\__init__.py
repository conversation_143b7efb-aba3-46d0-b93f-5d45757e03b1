"""
SQLModel models for Arroyo University Core API
"""

from .base import BaseModel, TimestampMixin
from .tenant import Tenant, Tenant<PERSON>reate, TenantUpdate
from .user import User, UserCreate, UserUpdate, UserResponse
from .role import Role, RoleCreate, RoleUpdate, Permission
from .group import Group, GroupCreate, GroupUpdate, GroupMember
from .course import Course, CourseCreate, CourseUpdate, CourseModule, ContentItem
from .enrollment import CourseEnrollment, UserProgress
from .question import Question, QuestionCreate, QuestionUpdate, QuestionBank
from .exam import Exam, ExamCreate, ExamUpdate, ExamSubmission, SubmissionAnswer
from .forum import ForumCategory, ForumPost, ForumReply, ForumLike
from .notification import Notification, EmailTemplate, EmailQueue
from .analytics import UserScore, ScoreTransaction, SystemMetrics
from .settings import SystemSetting
from .ai import AITask, QuestionGenerationRequest, ScoringRequest, ModerationRequest

__all__ = [
    # Base
    "BaseModel",
    "TimestampMixin",
    
    # Tenant
    "Tenant",
    "TenantCreate", 
    "TenantUpdate",
    
    # User
    "User",
    "UserCreate",
    "UserUpdate", 
    "UserResponse",
    
    # Role
    "Role",
    "RoleCreate",
    "RoleUpdate",
    "Permission",
    
    # Group
    "Group",
    "GroupCreate",
    "GroupUpdate",
    "GroupMember",
    
    # Course
    "Course",
    "CourseCreate",
    "CourseUpdate",
    "CourseModule",
    "ContentItem",
    
    # Enrollment
    "CourseEnrollment",
    "UserProgress",
    
    # Question
    "Question",
    "QuestionCreate",
    "QuestionUpdate",
    "QuestionBank",
    
    # Exam
    "Exam",
    "ExamCreate",
    "ExamUpdate",
    "ExamSubmission",
    "SubmissionAnswer",
    
    # Forum
    "ForumCategory",
    "ForumPost",
    "ForumReply",
    "ForumLike",
    
    # Notification
    "Notification",
    "EmailTemplate",
    "EmailQueue",
    
    # Analytics
    "UserScore",
    "ScoreTransaction",
    "SystemMetrics",
    
    # Settings
    "SystemSetting",

    # AI
    "AITask",
    "QuestionGenerationRequest",
    "ScoringRequest",
    "ModerationRequest",
]
