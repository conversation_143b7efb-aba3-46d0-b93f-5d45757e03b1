# Arroyo University - Development Makefile

.PHONY: help build up down logs clean test lint format install-deps backup restore

# Default target
help:
	@echo "Arroyo University - Available Commands:"
	@echo ""
	@echo "Development:"
	@echo "  make up              - Start all services in development mode"
	@echo "  make down            - Stop all services"
	@echo "  make build           - Build all Docker images"
	@echo "  make logs            - Show logs from all services"
	@echo "  make logs-follow     - Follow logs from all services"
	@echo "  make restart         - Restart all services"
	@echo ""
	@echo "Production:"
	@echo "  make up-prod         - Start all services in production mode"
	@echo "  make down-prod       - Stop production services"
	@echo "  make build-prod      - Build production Docker images"
	@echo ""
	@echo "Database:"
	@echo "  make db-migrate      - Run database migrations"
	@echo "  make db-seed         - Seed database with sample data"
	@echo "  make db-backup       - Create database backup"
	@echo "  make db-restore      - Restore database from backup"
	@echo "  make db-reset        - Reset database (WARNING: destroys data)"
	@echo ""
	@echo "Testing:"
	@echo "  make test            - Run all tests"
	@echo "  make test-core       - Run core API tests"
	@echo "  make test-ai         - Run AI service tests"
	@echo "  make test-frontend   - Run frontend tests"
	@echo "  make test-coverage   - Run tests with coverage report"
	@echo ""
	@echo "Code Quality:"
	@echo "  make lint            - Run linting on all services"
	@echo "  make format          - Format code in all services"
	@echo "  make security-scan   - Run security scans"
	@echo ""
	@echo "Utilities:"
	@echo "  make clean           - Clean up Docker resources"
	@echo "  make install-deps    - Install development dependencies"
	@echo "  make status          - Show status of all services"
	@echo "  make shell-core      - Open shell in core API container"
	@echo "  make shell-ai        - Open shell in AI service container"
	@echo "  make shell-db        - Open PostgreSQL shell"

# Development commands
up:
	docker-compose up -d
	@echo "Services started. Access URLs:"
	@echo "  Frontend: http://localhost:3000"
	@echo "  API Gateway: http://localhost:80"
	@echo "  Core API: http://localhost:8000"
	@echo "  Grafana: http://localhost:3001"

down:
	docker-compose down

build:
	docker-compose build

logs:
	docker-compose logs

logs-follow:
	docker-compose logs -f

restart:
	docker-compose restart

# Production commands
up-prod:
	docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d

down-prod:
	docker-compose -f docker-compose.yml -f docker-compose.prod.yml down

build-prod:
	docker-compose -f docker-compose.yml -f docker-compose.prod.yml build

# Database commands
db-migrate:
	docker-compose exec core-api python -m alembic upgrade head

db-seed:
	docker-compose exec core-api python scripts/seed_data.py

db-backup:
	@mkdir -p ./database/backups
	docker-compose exec postgres pg_dump -U postgres arroyo_university > ./database/backups/backup_$(shell date +%Y%m%d_%H%M%S).sql
	@echo "Database backup created in ./database/backups/"

db-restore:
	@read -p "Enter backup file name (without path): " backup_file; \
	docker-compose exec -T postgres psql -U postgres -d arroyo_university < ./database/backups/$$backup_file

db-reset:
	@echo "WARNING: This will destroy all data!"
	@read -p "Are you sure? (y/N): " confirm; \
	if [ "$$confirm" = "y" ] || [ "$$confirm" = "Y" ]; then \
		docker-compose down; \
		docker volume rm arroyouniversity_postgres_data; \
		docker-compose up -d postgres; \
		sleep 10; \
		make db-migrate; \
	fi

# Testing commands
test:
	@echo "Running all tests..."
	make test-core
	make test-ai
	make test-frontend

test-core:
	docker-compose exec core-api python -m pytest tests/ -v

test-ai:
	docker-compose exec ai-service python -m pytest tests/ -v

test-frontend:
	docker-compose exec frontend npm test

test-coverage:
	docker-compose exec core-api python -m pytest tests/ --cov=app --cov-report=html
	docker-compose exec ai-service python -m pytest tests/ --cov=app --cov-report=html

# Code quality commands
lint:
	@echo "Running linting..."
	docker-compose exec core-api python -m flake8 app/
	docker-compose exec core-api python -m mypy app/
	docker-compose exec ai-service python -m flake8 app/
	docker-compose exec ai-service python -m mypy app/
	docker-compose exec notification-service python -m flake8 app/
	docker-compose exec frontend npm run lint

format:
	@echo "Formatting code..."
	docker-compose exec core-api python -m black app/
	docker-compose exec core-api python -m isort app/
	docker-compose exec ai-service python -m black app/
	docker-compose exec ai-service python -m isort app/
	docker-compose exec notification-service python -m black app/
	docker-compose exec notification-service python -m isort app/
	docker-compose exec frontend npm run format

security-scan:
	@echo "Running security scans..."
	docker-compose exec core-api python -m bandit -r app/
	docker-compose exec ai-service python -m bandit -r app/
	docker-compose exec notification-service python -m bandit -r app/

# Utility commands
clean:
	@echo "Cleaning up Docker resources..."
	docker-compose down -v
	docker system prune -f
	docker volume prune -f

install-deps:
	@echo "Installing development dependencies..."
	cd core-api && pip install -r requirements.txt
	cd ai-service && pip install -r requirements.txt
	cd notification-service && pip install -r requirements.txt
	cd frontend && npm install

status:
	docker-compose ps

shell-core:
	docker-compose exec core-api bash

shell-ai:
	docker-compose exec ai-service bash

shell-notification:
	docker-compose exec notification-service bash

shell-db:
	docker-compose exec postgres psql -U postgres -d arroyo_university

# Monitoring commands
monitoring-up:
	docker-compose up -d prometheus grafana loki

monitoring-down:
	docker-compose stop prometheus grafana loki

# Development helpers
dev-setup: build up db-migrate
	@echo "Development environment setup complete!"
	@echo "Run 'make logs-follow' to see service logs"

# Quick commands for common operations
quick-restart-api:
	docker-compose restart core-api

quick-restart-ai:
	docker-compose restart ai-service

quick-restart-frontend:
	docker-compose restart frontend

# Health checks
health-check:
	@echo "Checking service health..."
	@curl -f http://localhost:8000/health || echo "Core API: DOWN"
	@curl -f http://localhost:8001/health || echo "AI Service: DOWN"
	@curl -f http://localhost:8002/health || echo "Notification Service: DOWN"
	@curl -f http://localhost:3000 || echo "Frontend: DOWN"
