import React, { useState, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import { CourseGrid } from '@/components/courses/CourseGrid';
import { CourseFilters } from '@/components/courses/CourseFilters';
import { Button } from '@/components/ui/Button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { CourseFilters as CourseFiltersType, Course } from '@/types';
import { courseService } from '@/services/courseService';
import toast from 'react-hot-toast';

const categories = [
  'Tecnología',
  'Idiomas', 
  'Matemáticas',
  'Ciencias',
  'Negocios',
  'Arte',
  'Historia',
  'Metodologías'
];

const featuredCourses = [
  {
    id: 'devops-1',
    title: 'DevOps Fundamentals',
    description: 'Aprende los principios fundamentales de DevOps, CI/CD, containerización y automatización.',
    category: 'Tecnología',
    level: 'intermediate' as const,
    duration: 8,
    studentCount: 156,
    rating: 4.8,
    creator: { id: '1', firstName: '<PERSON><PERSON> <PERSON>', lastName: '<PERSON>' },
    status: 'published' as const,
    tenantId: '1',
    creatorId: '1',
    metadata: {},
    version: 1,
    createdAt: '2024-01-01',
    updatedAt: '2024-01-01',
  },
  {
    id: 'ai-basics-1',
    title: 'Introducción a la Inteligencia Artificial',
    description: 'Conceptos básicos de IA, machine learning y aplicaciones prácticas en el mundo real.',
    category: 'Tecnología',
    level: 'beginner' as const,
    duration: 6,
    studentCount: 243,
    rating: 4.6,
    creator: { id: '2', firstName: 'Dra. Martínez', lastName: 'Martínez' },
    status: 'published' as const,
    tenantId: '1',
    creatorId: '2',
    metadata: {},
    version: 1,
    createdAt: '2024-01-01',
    updatedAt: '2024-01-01',
  },
  {
    id: 'prompt-eng-1',
    title: 'Prompt Engineering Avanzado',
    description: 'Domina el arte de crear prompts efectivos para modelos de IA y maximiza su potencial.',
    category: 'Tecnología',
    level: 'advanced' as const,
    duration: 4,
    studentCount: 89,
    rating: 4.9,
    creator: { id: '3', firstName: 'Ing. López', lastName: 'López' },
    status: 'published' as const,
    tenantId: '1',
    creatorId: '3',
    metadata: {},
    version: 1,
    createdAt: '2024-01-01',
    updatedAt: '2024-01-01',
  },
];

const allCourses = [
  ...featuredCourses,
  {
    id: 'python-1',
    title: 'Python para Principiantes',
    description: 'Fundamentos de programación en Python',
    category: 'Tecnología',
    level: 'beginner' as const,
    duration: 10,
    studentCount: 312,
    rating: 4.8,
    creator: { id: '4', firstName: 'Prof. Silva', lastName: 'Silva' },
    status: 'published' as const,
    tenantId: '1',
    creatorId: '4',
    metadata: {},
    version: 1,
    createdAt: '2024-01-01',
    updatedAt: '2024-01-01',
  },
  {
    id: 'data-science-1',
    title: 'Ciencia de Datos',
    description: 'Análisis de datos con Python y R',
    category: 'Tecnología',
    level: 'intermediate' as const,
    duration: 12,
    studentCount: 187,
    rating: 4.6,
    creator: { id: '5', firstName: 'Dra. Chen', lastName: 'Chen' },
    status: 'published' as const,
    tenantId: '1',
    creatorId: '5',
    metadata: {},
    version: 1,
    createdAt: '2024-01-01',
    updatedAt: '2024-01-01',
  },
  {
    id: 'english-b2-1',
    title: 'English B2 Intermediate',
    description: 'Inglés intermedio alto',
    category: 'Idiomas',
    level: 'intermediate' as const,
    duration: 16,
    studentCount: 456,
    rating: 4.9,
    creator: { id: '6', firstName: 'Prof. Johnson', lastName: 'Johnson' },
    status: 'published' as const,
    tenantId: '1',
    creatorId: '6',
    metadata: {},
    version: 1,
    createdAt: '2024-01-01',
    updatedAt: '2024-01-01',
  },
];

export default function MarketplacePage() {
  const [filters, setFilters] = useState<CourseFiltersType>({});
  const [activeTab, setActiveTab] = useState<'courses' | 'groups'>('courses');

  // Filter courses based on current filters
  const filteredCourses = allCourses.filter((course) => {
    if (filters.category && course.category !== filters.category) return false;
    if (filters.level && course.level !== filters.level) return false;
    if (filters.search) {
      const searchLower = filters.search.toLowerCase();
      return (
        course.title.toLowerCase().includes(searchLower) ||
        course.description.toLowerCase().includes(searchLower)
      );
    }
    return true;
  });

  const handleEnrollCourse = async (courseId: string) => {
    try {
      // await courseService.enrollCourse(courseId);
      toast.success('¡Te has inscrito al curso exitosamente!');
    } catch (error) {
      toast.error('Error al inscribirse al curso');
    }
  };

  const handleSaveCourse = async (courseId: string) => {
    try {
      // await courseService.saveCourse(courseId);
      toast.success('Curso guardado en favoritos');
    } catch (error) {
      toast.error('Error al guardar el curso');
    }
  };

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900">Marketplace</h1>
        <p className="text-gray-600 mt-2">
          Descubre y únete a cursos disponibles en tu organización
        </p>
      </div>

      {/* Tabs */}
      <div className="mb-8">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8">
            <button
              onClick={() => setActiveTab('courses')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'courses'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Cursos
            </button>
            <button
              onClick={() => setActiveTab('groups')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'groups'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Grupos
            </button>
          </nav>
        </div>
      </div>

      {activeTab === 'courses' && (
        <>
          {/* Filters */}
          <CourseFilters
            filters={filters}
            onFiltersChange={setFilters}
            categories={categories}
          />

          {/* Categories */}
          <div className="mb-8">
            <div className="flex flex-wrap gap-2">
              <Button
                variant={!filters.category ? 'default' : 'outline'}
                size="sm"
                onClick={() => setFilters({ ...filters, category: undefined })}
              >
                Todos
              </Button>
              {categories.map((category) => (
                <Button
                  key={category}
                  variant={filters.category === category ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setFilters({ ...filters, category })}
                >
                  {category}
                </Button>
              ))}
            </div>
          </div>

          {/* Featured Courses */}
          {!filters.category && !filters.level && !filters.search && (
            <div className="mb-8">
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                Cursos Destacados
              </h3>
              <CourseGrid
                courses={featuredCourses}
                onEnroll={handleEnrollCourse}
                onSave={handleSaveCourse}
                columns={3}
              />
            </div>
          )}

          {/* All Courses */}
          <Card>
            <CardHeader>
              <CardTitle>
                {filters.category || filters.level || filters.search
                  ? 'Resultados de búsqueda'
                  : 'Todos los Cursos'}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <CourseGrid
                courses={filteredCourses}
                onEnroll={handleEnrollCourse}
                onSave={handleSaveCourse}
                variant="compact"
                columns={4}
              />
            </CardContent>
          </Card>
        </>
      )}

      {activeTab === 'groups' && (
        <div className="text-center py-12">
          <div className="w-24 h-24 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
            <svg className="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">Grupos</h3>
          <p className="text-gray-500">La funcionalidad de grupos estará disponible próximamente.</p>
        </div>
      )}
    </div>
  );
}
