# Grafana Dashboard Provisioning Configuration
# This file tells Grafana where to find dashboard definitions

apiVersion: 1

providers:
  # System Infrastructure Dashboards
  - name: 'infrastructure'
    orgId: 1
    folder: 'Infrastructure'
    type: file
    disableDeletion: false
    updateIntervalSeconds: 10
    allowUiUpdates: true
    options:
      path: /var/lib/grafana/dashboards/infrastructure

  # Application Performance Dashboards
  - name: 'application'
    orgId: 1
    folder: 'Application'
    type: file
    disableDeletion: false
    updateIntervalSeconds: 10
    allowUiUpdates: true
    options:
      path: /var/lib/grafana/dashboards/application

  # Business Metrics Dashboards
  - name: 'business'
    orgId: 1
    folder: 'Business'
    type: file
    disableDeletion: false
    updateIntervalSeconds: 10
    allowUiUpdates: true
    options:
      path: /var/lib/grafana/dashboards/business

  # AI Services Dashboards
  - name: 'ai-services'
    orgId: 1
    folder: 'AI Services'
    type: file
    disableDeletion: false
    updateIntervalSeconds: 10
    allowUiUpdates: true
    options:
      path: /var/lib/grafana/dashboards/ai-services

  # Security & Compliance Dashboards
  - name: 'security'
    orgId: 1
    folder: 'Security'
    type: file
    disableDeletion: false
    updateIntervalSeconds: 10
    allowUiUpdates: true
    options:
      path: /var/lib/grafana/dashboards/security
