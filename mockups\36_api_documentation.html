<!DOCTYPE html>
<html lang="es" class="h-full bg-gray-50">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Documentación API - Arroyo University</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400;500;600&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Inter', sans-serif; }
        .code { font-family: 'JetBrains Mono', monospace; }
    </style>
</head>
<body class="h-full">
    <div class="min-h-full">
        <!-- Navigation -->
        <nav class="bg-white shadow">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between h-16">
                    <div class="flex items-center">
                        <button onclick="window.location.href='04_admin_tenant_dashboard.html'" 
                                class="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center hover:bg-blue-700 transition-colors">
                            <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M10.394 2.08a1 1 0 00-.788 0l-7 3a1 1 0 000 1.84L5.25 8.051a.999.999 0 01.356-.257l4-1.714a1 1 0 11.788 1.84L7.667 9.088l1.94.831a1 1 0 00.787 0l7-3a1 1 0 000-1.838l-7-3z"/>
                            </svg>
                        </button>
                        <h1 class="ml-3 text-xl font-semibold text-gray-900">Universidad Ejemplo</h1>
                        <nav class="ml-8 flex space-x-4">
                            <a href="04_admin_tenant_dashboard.html" class="text-gray-500 hover:text-gray-700 px-3 py-2 text-sm font-medium">Dashboard</a>
                            <a href="26_user_management.html" class="text-gray-500 hover:text-gray-700 px-3 py-2 text-sm font-medium">Usuarios</a>
                            <a href="#" class="bg-blue-100 text-blue-700 px-3 py-2 rounded-md text-sm font-medium">API</a>
                        </nav>
                    </div>
                    
                    <div class="flex items-center space-x-4">
                        <div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                            <span class="text-sm font-medium text-gray-700">AM</span>
                        </div>
                    </div>
                </div>
            </div>
        </nav>

        <!-- Main Content -->
        <main class="max-w-7xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
                <!-- Sidebar -->
                <div class="lg:col-span-1">
                    <div class="bg-white shadow rounded-lg sticky top-8">
                        <div class="px-6 py-4 border-b border-gray-200">
                            <h3 class="text-lg font-medium text-gray-900">Documentación API</h3>
                        </div>
                        <nav class="p-6">
                            <ul class="space-y-2">
                                <li><a href="#getting-started" class="text-blue-600 hover:text-blue-500 text-sm font-medium">Primeros Pasos</a></li>
                                <li><a href="#authentication" class="text-gray-700 hover:text-gray-900 text-sm">Autenticación</a></li>
                                <li><a href="#students" class="text-gray-700 hover:text-gray-900 text-sm">Estudiantes</a></li>
                                <li><a href="#courses" class="text-gray-700 hover:text-gray-900 text-sm">Cursos</a></li>
                                <li><a href="#exams" class="text-gray-700 hover:text-gray-900 text-sm">Exámenes</a></li>
                                <li><a href="#results" class="text-gray-700 hover:text-gray-900 text-sm">Resultados</a></li>
                                <li><a href="#webhooks" class="text-gray-700 hover:text-gray-900 text-sm">Webhooks</a></li>
                                <li><a href="#errors" class="text-gray-700 hover:text-gray-900 text-sm">Códigos de Error</a></li>
                            </ul>
                        </nav>
                    </div>
                </div>

                <!-- Content -->
                <div class="lg:col-span-3 space-y-8">
                    <!-- Getting Started -->
                    <section id="getting-started" class="bg-white shadow rounded-lg">
                        <div class="px-6 py-4 border-b border-gray-200">
                            <h2 class="text-xl font-semibold text-gray-900">Primeros Pasos</h2>
                        </div>
                        <div class="p-6">
                            <p class="text-gray-600 mb-4">
                                La API de Arroyo University te permite integrar nuestras funcionalidades de evaluación de idiomas en tus aplicaciones.
                            </p>
                            
                            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                                <h4 class="text-sm font-medium text-blue-900 mb-2">URL Base</h4>
                                <code class="code text-blue-800">https://api.arroyo.app/v1</code>
                            </div>

                            <h4 class="text-lg font-medium text-gray-900 mb-3">Características Principales</h4>
                            <ul class="list-disc list-inside text-gray-600 space-y-1 mb-6">
                                <li>API RESTful con respuestas JSON</li>
                                <li>Autenticación mediante API Keys</li>
                                <li>Rate limiting: 1000 requests/hora</li>
                                <li>Webhooks para eventos en tiempo real</li>
                                <li>Documentación interactiva con Swagger</li>
                            </ul>

                            <div class="bg-gray-50 rounded-lg p-4">
                                <h4 class="text-sm font-medium text-gray-900 mb-2">Ejemplo de Request</h4>
                                <pre class="code text-sm text-gray-800 overflow-x-auto"><code>curl -X GET "https://api.arroyo.app/v1/students" \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -H "Content-Type: application/json"</code></pre>
                            </div>
                        </div>
                    </section>

                    <!-- Authentication -->
                    <section id="authentication" class="bg-white shadow rounded-lg">
                        <div class="px-6 py-4 border-b border-gray-200">
                            <h2 class="text-xl font-semibold text-gray-900">Autenticación</h2>
                        </div>
                        <div class="p-6">
                            <p class="text-gray-600 mb-4">
                                Todas las requests a la API requieren autenticación mediante Bearer Token.
                            </p>

                            <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
                                <h4 class="text-sm font-medium text-yellow-900 mb-2">Tu API Key</h4>
                                <div class="flex items-center space-x-2">
                                    <code class="code text-yellow-800 bg-yellow-100 px-2 py-1 rounded">ak_live_1234567890abcdef</code>
                                    <button onclick="copyApiKey()" class="text-yellow-600 hover:text-yellow-500">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"/>
                                        </svg>
                                    </button>
                                </div>
                            </div>

                            <h4 class="text-lg font-medium text-gray-900 mb-3">Headers Requeridos</h4>
                            <div class="bg-gray-50 rounded-lg p-4 mb-6">
                                <pre class="code text-sm text-gray-800"><code>Authorization: Bearer YOUR_API_KEY
Content-Type: application/json
X-Tenant-ID: universidad-ejemplo</code></pre>
                            </div>

                            <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                                <h4 class="text-sm font-medium text-red-900 mb-2">⚠️ Importante</h4>
                                <p class="text-red-700 text-sm">Nunca expongas tu API key en código del lado del cliente. Úsala solo en tu backend.</p>
                            </div>
                        </div>
                    </section>

                    <!-- Students Endpoint -->
                    <section id="students" class="bg-white shadow rounded-lg">
                        <div class="px-6 py-4 border-b border-gray-200">
                            <h2 class="text-xl font-semibold text-gray-900">Estudiantes</h2>
                        </div>
                        <div class="p-6">
                            <!-- GET Students -->
                            <div class="mb-8">
                                <div class="flex items-center space-x-3 mb-3">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">GET</span>
                                    <code class="code text-gray-800">/students</code>
                                </div>
                                <p class="text-gray-600 mb-4">Obtiene la lista de estudiantes del tenant.</p>
                                
                                <h5 class="text-sm font-medium text-gray-900 mb-2">Parámetros de Query</h5>
                                <div class="overflow-x-auto mb-4">
                                    <table class="min-w-full divide-y divide-gray-200">
                                        <thead class="bg-gray-50">
                                            <tr>
                                                <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Parámetro</th>
                                                <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Tipo</th>
                                                <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Descripción</th>
                                            </tr>
                                        </thead>
                                        <tbody class="bg-white divide-y divide-gray-200">
                                            <tr>
                                                <td class="px-4 py-2 text-sm code text-gray-900">limit</td>
                                                <td class="px-4 py-2 text-sm text-gray-500">integer</td>
                                                <td class="px-4 py-2 text-sm text-gray-500">Número de resultados (máx: 100)</td>
                                            </tr>
                                            <tr>
                                                <td class="px-4 py-2 text-sm code text-gray-900">offset</td>
                                                <td class="px-4 py-2 text-sm text-gray-500">integer</td>
                                                <td class="px-4 py-2 text-sm text-gray-500">Número de resultados a omitir</td>
                                            </tr>
                                            <tr>
                                                <td class="px-4 py-2 text-sm code text-gray-900">course_id</td>
                                                <td class="px-4 py-2 text-sm text-gray-500">string</td>
                                                <td class="px-4 py-2 text-sm text-gray-500">Filtrar por curso específico</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>

                                <h5 class="text-sm font-medium text-gray-900 mb-2">Respuesta de Ejemplo</h5>
                                <div class="bg-gray-50 rounded-lg p-4">
                                    <pre class="code text-sm text-gray-800 overflow-x-auto"><code>{
  "data": [
    {
      "id": "std_1234567890",
      "email": "<EMAIL>",
      "name": "Carlos López",
      "created_at": "2023-11-15T10:30:00Z",
      "courses": ["course_english_b2"],
      "status": "active"
    }
  ],
  "pagination": {
    "total": 1247,
    "limit": 20,
    "offset": 0,
    "has_more": true
  }
}</code></pre>
                                </div>
                            </div>

                            <!-- POST Students -->
                            <div class="mb-8">
                                <div class="flex items-center space-x-3 mb-3">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">POST</span>
                                    <code class="code text-gray-800">/students</code>
                                </div>
                                <p class="text-gray-600 mb-4">Crea un nuevo estudiante.</p>
                                
                                <h5 class="text-sm font-medium text-gray-900 mb-2">Body de Request</h5>
                                <div class="bg-gray-50 rounded-lg p-4">
                                    <pre class="code text-sm text-gray-800 overflow-x-auto"><code>{
  "email": "<EMAIL>",
  "name": "Nuevo Estudiante",
  "course_ids": ["course_english_b2"],
  "send_invitation": true
}</code></pre>
                                </div>
                            </div>
                        </div>
                    </section>

                    <!-- Exams Endpoint -->
                    <section id="exams" class="bg-white shadow rounded-lg">
                        <div class="px-6 py-4 border-b border-gray-200">
                            <h2 class="text-xl font-semibold text-gray-900">Exámenes</h2>
                        </div>
                        <div class="p-6">
                            <div class="mb-8">
                                <div class="flex items-center space-x-3 mb-3">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">GET</span>
                                    <code class="code text-gray-800">/exams/{exam_id}/results</code>
                                </div>
                                <p class="text-gray-600 mb-4">Obtiene los resultados de un examen específico.</p>
                                
                                <h5 class="text-sm font-medium text-gray-900 mb-2">Respuesta de Ejemplo</h5>
                                <div class="bg-gray-50 rounded-lg p-4">
                                    <pre class="code text-sm text-gray-800 overflow-x-auto"><code>{
  "exam_id": "exam_1234567890",
  "title": "Evaluación de Competencias Técnicas",
  "results": [
    {
      "student_id": "std_1234567890",
      "student_name": "Carlos López",
      "score": 85,
      "difficulty_level": "Intermedio",
      "completed_at": "2023-11-15T14:30:00Z",
      "sections": {
        "writing": 84,
        "listening": 76,
        "speaking": 80,
        "technical_knowledge": 78
      }
    }
  ]
}</code></pre>
                                </div>
                            </div>
                        </div>
                    </section>

                    <!-- Webhooks -->
                    <section id="webhooks" class="bg-white shadow rounded-lg">
                        <div class="px-6 py-4 border-b border-gray-200">
                            <h2 class="text-xl font-semibold text-gray-900">Webhooks</h2>
                        </div>
                        <div class="p-6">
                            <p class="text-gray-600 mb-4">
                                Los webhooks te permiten recibir notificaciones en tiempo real cuando ocurren eventos importantes.
                            </p>

                            <h4 class="text-lg font-medium text-gray-900 mb-3">Eventos Disponibles</h4>
                            <div class="space-y-3 mb-6">
                                <div class="flex items-center space-x-3">
                                    <code class="code text-sm bg-gray-100 px-2 py-1 rounded">exam.completed</code>
                                    <span class="text-gray-600 text-sm">Un estudiante completa un examen</span>
                                </div>
                                <div class="flex items-center space-x-3">
                                    <code class="code text-sm bg-gray-100 px-2 py-1 rounded">student.created</code>
                                    <span class="text-gray-600 text-sm">Se crea un nuevo estudiante</span>
                                </div>
                                <div class="flex items-center space-x-3">
                                    <code class="code text-sm bg-gray-100 px-2 py-1 rounded">certificate.generated</code>
                                    <span class="text-gray-600 text-sm">Se genera un certificado</span>
                                </div>
                            </div>

                            <h4 class="text-lg font-medium text-gray-900 mb-3">Configuración</h4>
                            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
                                <h5 class="text-sm font-medium text-blue-900 mb-2">URL de Webhook Configurada</h5>
                                <code class="code text-blue-800">https://tu-app.com/webhooks/arroyo</code>
                            </div>

                            <h5 class="text-sm font-medium text-gray-900 mb-2">Ejemplo de Payload</h5>
                            <div class="bg-gray-50 rounded-lg p-4">
                                <pre class="code text-sm text-gray-800 overflow-x-auto"><code>{
  "event": "exam.completed",
  "data": {
    "exam_id": "exam_1234567890",
    "student_id": "std_1234567890",
    "score": 85,
    "cefr_level": "B2.1",
    "completed_at": "2023-11-15T14:30:00Z"
  },
  "timestamp": "2023-11-15T14:30:05Z"
}</code></pre>
                            </div>
                        </div>
                    </section>

                    <!-- Error Codes -->
                    <section id="errors" class="bg-white shadow rounded-lg">
                        <div class="px-6 py-4 border-b border-gray-200">
                            <h2 class="text-xl font-semibold text-gray-900">Códigos de Error</h2>
                        </div>
                        <div class="p-6">
                            <div class="overflow-x-auto">
                                <table class="min-w-full divide-y divide-gray-200">
                                    <thead class="bg-gray-50">
                                        <tr>
                                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Código</th>
                                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Descripción</th>
                                        </tr>
                                    </thead>
                                    <tbody class="bg-white divide-y divide-gray-200">
                                        <tr>
                                            <td class="px-4 py-2 text-sm code text-red-600">400</td>
                                            <td class="px-4 py-2 text-sm text-gray-500">Bad Request - Parámetros inválidos</td>
                                        </tr>
                                        <tr>
                                            <td class="px-4 py-2 text-sm code text-red-600">401</td>
                                            <td class="px-4 py-2 text-sm text-gray-500">Unauthorized - API key inválida</td>
                                        </tr>
                                        <tr>
                                            <td class="px-4 py-2 text-sm code text-red-600">403</td>
                                            <td class="px-4 py-2 text-sm text-gray-500">Forbidden - Sin permisos para este recurso</td>
                                        </tr>
                                        <tr>
                                            <td class="px-4 py-2 text-sm code text-red-600">404</td>
                                            <td class="px-4 py-2 text-sm text-gray-500">Not Found - Recurso no encontrado</td>
                                        </tr>
                                        <tr>
                                            <td class="px-4 py-2 text-sm code text-red-600">429</td>
                                            <td class="px-4 py-2 text-sm text-gray-500">Too Many Requests - Rate limit excedido</td>
                                        </tr>
                                        <tr>
                                            <td class="px-4 py-2 text-sm code text-red-600">500</td>
                                            <td class="px-4 py-2 text-sm text-gray-500">Internal Server Error - Error del servidor</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </section>
                </div>
            </div>
        </main>
    </div>

    <script>
        function copyApiKey() {
            navigator.clipboard.writeText('ak_live_1234567890abcdef');
            alert('API Key copiada al portapapeles');
        }

        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    </script>
</body>
</html>
