"""
User management service
"""

import secrets
from datetime import datetime, timed<PERSON><PERSON>
from typing import Optional, List
from uuid import UUI<PERSON>

from sqlmodel import Session, select, func
from sqlalchemy.orm import selectinload

from ..models.user import (
    User, UserCreate, UserUpdate, UserResponse, UserProfile,
    UserVerificationToken, UserChangePassword
)
from ..models.role import Role, UserRole
from ..models.group import GroupMember
from ..models.enrollment import CourseEnrollment
from ..models.analytics import UserScore
from ..services.auth_service import AuthService
from ..services.notification_service import NotificationService


class UserService:
    """User management service"""
    
    def __init__(self, db: Session):
        self.db = db
        self.auth_service = AuthService(db)
        self.notification_service = NotificationService(db)
    
    async def create_user(self, user_data: UserCreate, tenant_id: UUID, created_by: UUID = None) -> User:
        """Create a new user"""
        # Check if email already exists in tenant
        existing_user = self.db.exec(
            select(User).where(
                User.email == user_data.email,
                User.tenant_id == tenant_id,
                User.is_deleted == False
            )
        ).first()
        
        if existing_user:
            raise ValueError("User with this email already exists")
        
        # Hash password
        password_hash = self.auth_service._hash_password(user_data.password)
        
        # Create user
        user = User(
            tenant_id=tenant_id,
            email=user_data.email,
            first_name=user_data.first_name,
            last_name=user_data.last_name,
            password_hash=password_hash,
            is_active=user_data.is_active,
            avatar_url=user_data.avatar_url,
            bio=user_data.bio,
            timezone=user_data.timezone,
            language=user_data.language
        )
        
        self.db.add(user)
        self.db.commit()
        self.db.refresh(user)
        
        # Assign roles if provided
        if user_data.role_ids:
            await self.assign_roles(user.user_id, user_data.role_ids, created_by)
        
        # Create user score record
        user_score = UserScore(
            user_id=user.user_id,
            tenant_id=tenant_id
        )
        self.db.add(user_score)
        self.db.commit()
        
        # Send verification email if required
        if user_data.send_welcome_email:
            await self._send_verification_email(user)
        
        return user
    
    async def get_user(self, user_id: UUID, tenant_id: UUID) -> Optional[User]:
        """Get user by ID"""
        return self.db.exec(
            select(User).where(
                User.user_id == user_id,
                User.tenant_id == tenant_id,
                User.is_deleted == False
            )
        ).first()
    
    async def get_user_by_email(self, email: str, tenant_id: UUID) -> Optional[User]:
        """Get user by email"""
        return self.db.exec(
            select(User).where(
                User.email == email,
                User.tenant_id == tenant_id,
                User.is_deleted == False
            )
        ).first()
    
    async def update_user(self, user_id: UUID, user_data: UserUpdate, tenant_id: UUID) -> Optional[User]:
        """Update user"""
        user = await self.get_user(user_id, tenant_id)
        if not user:
            return None
        
        # Update fields
        update_data = user_data.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(user, field, value)
        
        user.updated_at = datetime.utcnow()
        
        self.db.add(user)
        self.db.commit()
        self.db.refresh(user)
        
        return user
    
    async def delete_user(self, user_id: UUID, tenant_id: UUID) -> bool:
        """Soft delete user"""
        user = await self.get_user(user_id, tenant_id)
        if not user:
            return False
        
        user.is_deleted = True
        user.deleted_at = datetime.utcnow()
        user.is_active = False
        
        self.db.add(user)
        self.db.commit()
        
        return True
    
    async def list_users(
        self, 
        tenant_id: UUID,
        skip: int = 0,
        limit: int = 20,
        search: str = None,
        role_id: UUID = None,
        is_active: bool = None
    ) -> List[UserResponse]:
        """List users with filtering"""
        stmt = select(User).where(
            User.tenant_id == tenant_id,
            User.is_deleted == False
        )
        
        # Apply filters
        if search:
            search_term = f"%{search}%"
            stmt = stmt.where(
                (User.first_name.ilike(search_term)) |
                (User.last_name.ilike(search_term)) |
                (User.email.ilike(search_term))
            )
        
        if is_active is not None:
            stmt = stmt.where(User.is_active == is_active)
        
        if role_id:
            stmt = stmt.join(UserRole).where(UserRole.role_id == role_id)
        
        stmt = stmt.offset(skip).limit(limit)
        users = self.db.exec(stmt).all()
        
        # Convert to response models with roles
        user_responses = []
        for user in users:
            roles = await self.get_user_roles(user.user_id)
            groups = await self.get_user_groups(user.user_id)
            
            user_response = UserResponse(
                user_id=user.user_id,
                email=user.email,
                first_name=user.first_name,
                last_name=user.last_name,
                is_active=user.is_active,
                is_verified=user.is_verified,
                avatar_url=user.avatar_url,
                bio=user.bio,
                timezone=user.timezone,
                language=user.language,
                created_at=user.created_at,
                updated_at=user.updated_at,
                email_verified_at=user.email_verified_at,
                last_login_at=user.last_login_at,
                login_count=user.login_count,
                roles=[role.name for role in roles],
                groups=[group.name for group in groups]
            )
            user_responses.append(user_response)
        
        return user_responses
    
    async def get_user_profile(self, user_id: UUID, tenant_id: UUID) -> Optional[UserProfile]:
        """Get detailed user profile"""
        user = await self.get_user(user_id, tenant_id)
        if not user:
            return None
        
        # Get user statistics
        enrollments = self.db.exec(
            select(CourseEnrollment).where(CourseEnrollment.user_id == user_id)
        ).all()
        
        completed_courses = len([e for e in enrollments if e.status == "completed"])
        
        # Get user score
        user_score = self.db.exec(
            select(UserScore).where(UserScore.user_id == user_id)
        ).first()
        
        # Get roles and groups
        roles = await self.get_user_roles(user_id)
        groups = await self.get_user_groups(user_id)
        
        return UserProfile(
            user_id=user.user_id,
            email=user.email,
            first_name=user.first_name,
            last_name=user.last_name,
            is_active=user.is_active,
            is_verified=user.is_verified,
            avatar_url=user.avatar_url,
            bio=user.bio,
            timezone=user.timezone,
            language=user.language,
            created_at=user.created_at,
            updated_at=user.updated_at,
            email_verified_at=user.email_verified_at,
            last_login_at=user.last_login_at,
            login_count=user.login_count,
            roles=[role.name for role in roles],
            groups=[group.name for group in groups],
            courses_enrolled=len(enrollments),
            courses_completed=completed_courses,
            total_points=user_score.total_points if user_score else 0,
            current_streak=user_score.current_streak if user_score else 0,
            achievements=user_score.achievements if user_score else [],
            learning_stats=user_score.statistics if user_score else {}
        )
    
    async def assign_roles(self, user_id: UUID, role_ids: List[UUID], assigned_by: UUID = None) -> bool:
        """Assign roles to user"""
        # Remove existing roles
        existing_roles = self.db.exec(
            select(UserRole).where(UserRole.user_id == user_id)
        ).all()
        
        for role in existing_roles:
            self.db.delete(role)
        
        # Add new roles
        user = self.db.get(User, user_id)
        if not user:
            return False
        
        for role_id in role_ids:
            user_role = UserRole(
                user_id=user_id,
                role_id=role_id,
                tenant_id=user.tenant_id,
                assigned_by=assigned_by or user_id
            )
            self.db.add(user_role)
        
        self.db.commit()
        return True
    
    async def get_user_roles(self, user_id: UUID) -> List[Role]:
        """Get user roles"""
        stmt = select(Role).join(UserRole).where(
            UserRole.user_id == user_id,
            UserRole.is_active == True
        )
        return self.db.exec(stmt).all()
    
    async def get_user_groups(self, user_id: UUID) -> List:
        """Get user groups"""
        from ..models.group import Group
        stmt = select(Group).join(GroupMember).where(
            GroupMember.user_id == user_id,
            GroupMember.is_active == True
        )
        return self.db.exec(stmt).all()
    
    async def change_password(self, user_id: UUID, password_data: UserChangePassword, tenant_id: UUID) -> bool:
        """Change user password"""
        user = await self.get_user(user_id, tenant_id)
        if not user:
            return False
        
        # Verify current password
        if not self.auth_service._verify_password(password_data.current_password, user.password_hash):
            raise ValueError("Current password is incorrect")
        
        # Update password
        user.password_hash = self.auth_service._hash_password(password_data.new_password)
        user.updated_at = datetime.utcnow()
        
        self.db.add(user)
        self.db.commit()
        
        return True
    
    async def bulk_create_users(self, users_data: List[dict], tenant_id: UUID, created_by: UUID = None) -> List[User]:
        """Bulk create users from CSV or similar data"""
        created_users = []
        
        for user_data in users_data:
            try:
                # Generate random password if not provided
                if 'password' not in user_data:
                    user_data['password'] = secrets.token_urlsafe(12)
                
                user_create = UserCreate(**user_data)
                user = await self.create_user(user_create, tenant_id, created_by)
                created_users.append(user)
                
            except Exception as e:
                # Log error but continue with other users
                print(f"Failed to create user {user_data.get('email', 'unknown')}: {e}")
                continue
        
        return created_users
    
    async def _send_verification_email(self, user: User):
        """Send email verification"""
        # Generate verification token
        token = secrets.token_urlsafe(32)
        token_hash = self.auth_service._hash_token(token)
        
        verification_token = UserVerificationToken(
            user_id=user.user_id,
            tenant_id=user.tenant_id,
            token_hash=token_hash,
            token_type="email_verification",
            expires_at=datetime.utcnow() + timedelta(hours=24)
        )
        
        self.db.add(verification_token)
        self.db.commit()
        
        # Send email
        await self.notification_service.send_verification_email(user, token)
