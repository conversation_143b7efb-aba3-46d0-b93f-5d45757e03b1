import React from 'react';
import { Course } from '@/types';
import { CourseCard } from './CourseCard';

interface CourseGridProps {
  courses: Course[];
  onEnroll?: (courseId: string) => void;
  onSave?: (courseId: string) => void;
  savedCourses?: string[];
  enrolledCourses?: string[];
  variant?: 'default' | 'compact';
  columns?: 1 | 2 | 3 | 4;
}

export function CourseGrid({ 
  courses, 
  onEnroll, 
  onSave, 
  savedCourses = [], 
  enrolledCourses = [],
  variant = 'default',
  columns = 3
}: CourseGridProps) {
  const getGridClasses = () => {
    const baseClasses = 'grid gap-6';
    switch (columns) {
      case 1:
        return `${baseClasses} grid-cols-1`;
      case 2:
        return `${baseClasses} grid-cols-1 md:grid-cols-2`;
      case 3:
        return `${baseClasses} grid-cols-1 md:grid-cols-2 lg:grid-cols-3`;
      case 4:
        return `${baseClasses} grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4`;
      default:
        return `${baseClasses} grid-cols-1 md:grid-cols-2 lg:grid-cols-3`;
    }
  };

  if (courses.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="w-24 h-24 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
          <svg className="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
          </svg>
        </div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">No hay cursos disponibles</h3>
        <p className="text-gray-500">No se encontraron cursos que coincidan con los criterios de búsqueda.</p>
      </div>
    );
  }

  return (
    <div className={getGridClasses()}>
      {courses.map((course) => (
        <CourseCard
          key={course.id}
          course={course}
          onEnroll={onEnroll}
          onSave={onSave}
          isSaved={savedCourses.includes(course.id)}
          isEnrolled={enrolledCourses.includes(course.id)}
          variant={variant}
        />
      ))}
    </div>
  );
}
