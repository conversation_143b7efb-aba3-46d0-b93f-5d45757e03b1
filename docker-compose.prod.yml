# Production Docker Compose Override
# Use with: docker-compose -f docker-compose.yml -f docker-compose.prod.yml up

version: '3.8'

services:
  # Production PostgreSQL with optimized settings
  postgres:
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-arroyo_university}
      POSTGRES_USER: ${POSTGRES_USER:-postgres}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
    command: >
      postgres
      -c shared_preload_libraries=pg_stat_statements
      -c max_connections=200
      -c shared_buffers=256MB
      -c effective_cache_size=1GB
      -c maintenance_work_mem=64MB
      -c checkpoint_completion_target=0.9
      -c wal_buffers=16MB
      -c default_statistics_target=100
      -c random_page_cost=1.1
      -c effective_io_concurrency=200
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init:/docker-entrypoint-initdb.d
      - ./database/backups:/backups

  # Production Redis with persistence
  redis:
    command: >
      redis-server
      --appendonly yes
      --requirepass ${REDIS_PASSWORD}
      --maxmemory 512mb
      --maxmemory-policy allkeys-lru
      --save 900 1
      --save 300 10
      --save 60 10000

  # API Gateway with SSL
  api-gateway:
    environment:
      - SSL_CERTIFICATE_PATH=/etc/nginx/ssl/cert.pem
      - SSL_PRIVATE_KEY_PATH=/etc/nginx/ssl/key.pem
    volumes:
      - ./api-gateway/nginx.prod.conf:/etc/nginx/nginx.conf
      - ./api-gateway/ssl:/etc/nginx/ssl
      - nginx_logs:/var/log/nginx

  # Core API with production settings
  core-api:
    build:
      context: ./core-api
      dockerfile: Dockerfile.prod
    environment:
      - DEBUG=false
      - LOG_LEVEL=INFO
      - WORKERS=4
    command: >
      gunicorn app.main:app
      -w 4
      -k uvicorn.workers.UvicornWorker
      --bind 0.0.0.0:8000
      --access-logfile -
      --error-logfile -
      --log-level info
    volumes:
      - core_api_logs:/app/logs
    deploy:
      replicas: 2
      resources:
        limits:
          memory: 1G
          cpus: '0.5'

  # AI Service with production settings
  ai-service:
    build:
      context: ./ai-service
      dockerfile: Dockerfile.prod
    environment:
      - DEBUG=false
      - LOG_LEVEL=INFO
      - WORKERS=2
    command: >
      gunicorn app.main:app
      -w 2
      -k uvicorn.workers.UvicornWorker
      --bind 0.0.0.0:8000
      --timeout 120
      --access-logfile -
      --error-logfile -
      --log-level info
    volumes:
      - ai_service_logs:/app/logs
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'

  # AI Worker with production settings
  ai-worker:
    build:
      context: ./ai-service
      dockerfile: Dockerfile.prod
    environment:
      - DEBUG=false
      - LOG_LEVEL=INFO
    command: >
      celery -A app.celery_app worker
      --loglevel=info
      --concurrency=4
      --max-tasks-per-child=1000
      --time-limit=300
      --soft-time-limit=240
    volumes:
      - ai_service_logs:/app/logs
    deploy:
      replicas: 2
      resources:
        limits:
          memory: 2G
          cpus: '1.0'

  # Notification Service with production settings
  notification-service:
    build:
      context: ./notification-service
      dockerfile: Dockerfile.prod
    environment:
      - DEBUG=false
      - LOG_LEVEL=INFO
      - WORKERS=2
    command: >
      gunicorn app.main:app
      -w 2
      -k uvicorn.workers.UvicornWorker
      --bind 0.0.0.0:8000
      --access-logfile -
      --error-logfile -
      --log-level info
    volumes:
      - notification_service_logs:/app/logs
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.25'

  # Frontend with production build
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      target: production
    environment:
      - NODE_ENV=production
    volumes:
      - nginx_logs:/var/log/nginx

  # Production monitoring with persistent storage
  prometheus:
    volumes:
      - ./monitoring/prometheus:/etc/prometheus
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=30d'
      - '--web.enable-lifecycle'
      - '--web.enable-admin-api'

  grafana:
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD}
      - GF_INSTALL_PLUGINS=grafana-piechart-panel
      - GF_SECURITY_SECRET_KEY=${GRAFANA_SECRET_KEY}
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/provisioning:/etc/grafana/provisioning
      - ./monitoring/grafana/dashboards:/var/lib/grafana/dashboards

  loki:
    volumes:
      - ./monitoring/loki:/etc/loki
      - loki_data:/loki
    command: -config.file=/etc/loki/local-config.yaml

  # Add exporters for better monitoring
  postgres-exporter:
    image: prometheuscommunity/postgres-exporter:latest
    container_name: arroyo-postgres-exporter
    environment:
      DATA_SOURCE_NAME: "postgresql://${POSTGRES_USER}:${POSTGRES_PASSWORD}@postgres:5432/${POSTGRES_DB}?sslmode=disable"
    ports:
      - "9187:9187"
    networks:
      - arroyo-network
    depends_on:
      - postgres

  redis-exporter:
    image: oliver006/redis_exporter:latest
    container_name: arroyo-redis-exporter
    environment:
      REDIS_ADDR: "redis://redis:6379"
      REDIS_PASSWORD: ${REDIS_PASSWORD}
    ports:
      - "9121:9121"
    networks:
      - arroyo-network
    depends_on:
      - redis

volumes:
  postgres_data:
  redis_data:
  minio_data:
  prometheus_data:
  grafana_data:
  loki_data:
  nginx_logs:
  core_api_logs:
  ai_service_logs:
  notification_service_logs:
