import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Clock, AlertTriangle, CheckCircle, ArrowLeft, ArrowRight, Flag } from 'lucide-react';
import { Button } from '@/components/ui/Button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Badge } from '@/components/ui/Badge';
import { Mo<PERSON>, ModalHeader, ModalFooter } from '@/components/ui/Modal';
import { ProgressBar } from '@/components/ui/ProgressBar';
import { examService, ExamAttempt, Question } from '@/services/examService';
import toast from 'react-hot-toast';

interface ExamQuestion extends Question {
  userAnswer?: string | string[];
  timeSpent: number;
  flagged: boolean;
}

const mockExam = {
  id: 'exam-1',
  title: 'DevOps Fundamentals - Final Exam',
  description: 'Evaluación final del curso de DevOps',
  timeLimit: 90,
  totalQuestions: 20,
  passingScore: 70,
  instructions: 'Lee cuidadosamente cada pregunta antes de responder. Puedes marcar preguntas para revisar más tarde.',
};

const mockQuestions: ExamQuestion[] = [
  {
    id: '1',
    type: 'multiple-choice',
    title: 'Principios de DevOps',
    content: '¿Cuál de los siguientes es un principio fundamental de DevOps?',
    options: ['Automatización', 'Colaboración', 'Monitoreo continuo', 'Todas las anteriores'],
    correctAnswer: 'Todas las anteriores',
    points: 5,
    difficulty: 'medium',
    category: 'DevOps',
    tags: ['principios'],
    createdBy: 'instructor',
    createdAt: '2024-01-01',
    updatedAt: '2024-01-01',
    timeSpent: 0,
    flagged: false,
  },
  {
    id: '2',
    type: 'essay',
    title: 'CI/CD Pipeline',
    content: 'Explica los beneficios de implementar un pipeline de CI/CD en un proyecto de software. (Mínimo 200 palabras)',
    points: 15,
    timeLimit: 900,
    difficulty: 'hard',
    category: 'DevOps',
    tags: ['ci-cd'],
    createdBy: 'instructor',
    createdAt: '2024-01-01',
    updatedAt: '2024-01-01',
    timeSpent: 0,
    flagged: false,
  },
  {
    id: '3',
    type: 'true-false',
    title: 'Docker Containers',
    content: 'Los contenedores Docker comparten el kernel del sistema operativo host.',
    correctAnswer: 'true',
    points: 3,
    difficulty: 'easy',
    category: 'Containerización',
    tags: ['docker'],
    createdBy: 'instructor',
    createdAt: '2024-01-01',
    updatedAt: '2024-01-01',
    timeSpent: 0,
    flagged: false,
  },
];

export default function ExamTakingPage() {
  const { examId } = useParams();
  const navigate = useNavigate();
  
  const [exam] = useState(mockExam);
  const [questions, setQuestions] = useState<ExamQuestion[]>(mockQuestions);
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [timeRemaining, setTimeRemaining] = useState(exam.timeLimit * 60); // in seconds
  const [isSubmitModalOpen, setIsSubmitModalOpen] = useState(false);
  const [isAutoSaving, setIsAutoSaving] = useState(false);
  const [examStarted, setExamStarted] = useState(false);

  const currentQuestion = questions[currentQuestionIndex];
  const answeredQuestions = questions.filter(q => q.userAnswer !== undefined).length;
  const flaggedQuestions = questions.filter(q => q.flagged).length;

  // Timer effect
  useEffect(() => {
    if (!examStarted || timeRemaining <= 0) return;

    const timer = setInterval(() => {
      setTimeRemaining(prev => {
        if (prev <= 1) {
          handleAutoSubmit();
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, [examStarted, timeRemaining]);

  // Auto-save effect
  useEffect(() => {
    if (!examStarted) return;

    const autoSaveTimer = setInterval(() => {
      handleAutoSave();
    }, 30000); // Auto-save every 30 seconds

    return () => clearInterval(autoSaveTimer);
  }, [examStarted, questions]);

  const formatTime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    
    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
    return `${minutes}:${secs.toString().padStart(2, '0')}`;
  };

  const handleStartExam = () => {
    setExamStarted(true);
    toast.success('Examen iniciado. ¡Buena suerte!');
  };

  const handleAnswerChange = (answer: string | string[]) => {
    setQuestions(prev => prev.map((q, index) => 
      index === currentQuestionIndex 
        ? { ...q, userAnswer: answer }
        : q
    ));
  };

  const handleFlagQuestion = () => {
    setQuestions(prev => prev.map((q, index) => 
      index === currentQuestionIndex 
        ? { ...q, flagged: !q.flagged }
        : q
    ));
  };

  const handleAutoSave = async () => {
    setIsAutoSaving(true);
    try {
      // Simulate auto-save
      await new Promise(resolve => setTimeout(resolve, 500));
    } catch (error) {
      console.error('Auto-save failed:', error);
    } finally {
      setIsAutoSaving(false);
    }
  };

  const handleAutoSubmit = () => {
    toast.error('Tiempo agotado. El examen se ha enviado automáticamente.');
    handleSubmitExam();
  };

  const handleSubmitExam = async () => {
    try {
      // Simulate exam submission
      await new Promise(resolve => setTimeout(resolve, 1500));
      toast.success('Examen enviado exitosamente');
      navigate(`/exams/${examId}/results`);
    } catch (error) {
      toast.error('Error al enviar el examen');
    }
  };

  const goToQuestion = (index: number) => {
    setCurrentQuestionIndex(index);
  };

  const goToPrevious = () => {
    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex(currentQuestionIndex - 1);
    }
  };

  const goToNext = () => {
    if (currentQuestionIndex < questions.length - 1) {
      setCurrentQuestionIndex(currentQuestionIndex + 1);
    }
  };

  if (!examStarted) {
    return (
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <Card>
          <CardHeader className="text-center">
            <CardTitle className="text-2xl">{exam.title}</CardTitle>
            <p className="text-gray-600 mt-2">{exam.description}</p>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-center">
              <div>
                <div className="text-2xl font-bold text-blue-600">{exam.totalQuestions}</div>
                <div className="text-sm text-gray-600">Preguntas</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-green-600">{exam.timeLimit}</div>
                <div className="text-sm text-gray-600">Minutos</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-purple-600">{exam.passingScore}%</div>
                <div className="text-sm text-gray-600">Para Aprobar</div>
              </div>
            </div>

            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h3 className="font-medium text-blue-900 mb-2">Instrucciones:</h3>
              <p className="text-blue-800 text-sm">{exam.instructions}</p>
            </div>

            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <div className="flex items-start">
                <AlertTriangle className="w-5 h-5 text-yellow-600 mr-2 mt-0.5" />
                <div>
                  <h4 className="font-medium text-yellow-900">Importante:</h4>
                  <ul className="text-yellow-800 text-sm mt-1 space-y-1">
                    <li>• Una vez iniciado, el examen no se puede pausar</li>
                    <li>• Tus respuestas se guardan automáticamente cada 30 segundos</li>
                    <li>• Puedes marcar preguntas para revisar más tarde</li>
                    <li>• El examen se enviará automáticamente cuando se agote el tiempo</li>
                  </ul>
                </div>
              </div>
            </div>

            <div className="flex justify-center">
              <Button size="lg" onClick={handleStartExam}>
                Iniciar Examen
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Header */}
      <div className="mb-6">
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-bold text-gray-900">{exam.title}</h1>
          <div className="flex items-center space-x-4">
            {isAutoSaving && (
              <span className="text-sm text-gray-500">Guardando...</span>
            )}
            <div className={`flex items-center space-x-2 ${timeRemaining < 300 ? 'text-red-600' : 'text-gray-700'}`}>
              <Clock className="w-5 h-5" />
              <span className="font-mono text-lg">{formatTime(timeRemaining)}</span>
            </div>
          </div>
        </div>
        
        <div className="mt-4">
          <ProgressBar 
            value={answeredQuestions} 
            max={questions.length} 
            showLabel 
            label={`Progreso: ${answeredQuestions}/${questions.length} preguntas respondidas`}
          />
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Question Navigation Sidebar */}
        <div className="lg:col-span-1">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Navegación</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-5 lg:grid-cols-4 gap-2 mb-4">
                {questions.map((question, index) => (
                  <button
                    key={question.id}
                    onClick={() => goToQuestion(index)}
                    className={`
                      w-10 h-10 rounded text-sm font-medium border-2 transition-colors
                      ${index === currentQuestionIndex 
                        ? 'border-blue-500 bg-blue-500 text-white' 
                        : question.userAnswer !== undefined
                          ? 'border-green-500 bg-green-500 text-white'
                          : question.flagged
                            ? 'border-yellow-500 bg-yellow-500 text-white'
                            : 'border-gray-300 hover:border-gray-400'
                      }
                    `}
                  >
                    {index + 1}
                  </button>
                ))}
              </div>
              
              <div className="space-y-2 text-sm">
                <div className="flex items-center">
                  <div className="w-4 h-4 bg-green-500 rounded mr-2"></div>
                  <span>Respondidas ({answeredQuestions})</span>
                </div>
                <div className="flex items-center">
                  <div className="w-4 h-4 bg-yellow-500 rounded mr-2"></div>
                  <span>Marcadas ({flaggedQuestions})</span>
                </div>
                <div className="flex items-center">
                  <div className="w-4 h-4 bg-blue-500 rounded mr-2"></div>
                  <span>Actual</span>
                </div>
                <div className="flex items-center">
                  <div className="w-4 h-4 border-2 border-gray-300 rounded mr-2"></div>
                  <span>Sin responder</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Main Question Area */}
        <div className="lg:col-span-3">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>
                    Pregunta {currentQuestionIndex + 1} de {questions.length}
                  </CardTitle>
                  <div className="flex items-center space-x-2 mt-2">
                    <Badge variant="outline">{currentQuestion.type}</Badge>
                    <Badge variant={currentQuestion.difficulty === 'easy' ? 'green' : currentQuestion.difficulty === 'medium' ? 'yellow' : 'red'}>
                      {currentQuestion.difficulty}
                    </Badge>
                    <span className="text-sm text-gray-600">{currentQuestion.points} puntos</span>
                  </div>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleFlagQuestion}
                  className={currentQuestion.flagged ? 'bg-yellow-100 border-yellow-500' : ''}
                >
                  <Flag className="w-4 h-4 mr-2" />
                  {currentQuestion.flagged ? 'Desmarcada' : 'Marcar'}
                </Button>
              </div>
            </CardHeader>
            <CardContent className="space-y-6">
              <div>
                <h3 className="text-lg font-medium mb-4">{currentQuestion.title}</h3>
                <p className="text-gray-700 whitespace-pre-wrap">{currentQuestion.content}</p>
              </div>

              {/* Question Type Specific Rendering */}
              {currentQuestion.type === 'multiple-choice' && (
                <div className="space-y-3">
                  {currentQuestion.options?.map((option, index) => (
                    <label key={index} className="flex items-center space-x-3 cursor-pointer">
                      <input
                        type="radio"
                        name={`question-${currentQuestion.id}`}
                        value={option}
                        checked={currentQuestion.userAnswer === option}
                        onChange={(e) => handleAnswerChange(e.target.value)}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                      />
                      <span className="text-gray-900">{option}</span>
                    </label>
                  ))}
                </div>
              )}

              {currentQuestion.type === 'true-false' && (
                <div className="space-y-3">
                  <label className="flex items-center space-x-3 cursor-pointer">
                    <input
                      type="radio"
                      name={`question-${currentQuestion.id}`}
                      value="true"
                      checked={currentQuestion.userAnswer === 'true'}
                      onChange={(e) => handleAnswerChange(e.target.value)}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                    />
                    <span className="text-gray-900">Verdadero</span>
                  </label>
                  <label className="flex items-center space-x-3 cursor-pointer">
                    <input
                      type="radio"
                      name={`question-${currentQuestion.id}`}
                      value="false"
                      checked={currentQuestion.userAnswer === 'false'}
                      onChange={(e) => handleAnswerChange(e.target.value)}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                    />
                    <span className="text-gray-900">Falso</span>
                  </label>
                </div>
              )}

              {currentQuestion.type === 'essay' && (
                <div>
                  <textarea
                    rows={8}
                    placeholder="Escribe tu respuesta aquí..."
                    value={currentQuestion.userAnswer as string || ''}
                    onChange={(e) => handleAnswerChange(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                  <div className="mt-2 text-sm text-gray-500">
                    Palabras: {((currentQuestion.userAnswer as string) || '').split(/\s+/).filter(w => w.length > 0).length}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Navigation Buttons */}
          <div className="flex justify-between mt-6">
            <Button
              variant="outline"
              onClick={goToPrevious}
              disabled={currentQuestionIndex === 0}
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Anterior
            </Button>

            <div className="flex space-x-3">
              {currentQuestionIndex === questions.length - 1 ? (
                <Button onClick={() => setIsSubmitModalOpen(true)}>
                  Finalizar Examen
                </Button>
              ) : (
                <Button onClick={goToNext}>
                  Siguiente
                  <ArrowRight className="w-4 h-4 ml-2" />
                </Button>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Submit Confirmation Modal */}
      <Modal
        isOpen={isSubmitModalOpen}
        onClose={() => setIsSubmitModalOpen(false)}
        title="Finalizar Examen"
        size="md"
      >
        <div className="space-y-4">
          <p className="text-gray-700">
            ¿Estás seguro de que quieres finalizar el examen? Una vez enviado, no podrás hacer cambios.
          </p>
          
          <div className="bg-gray-50 rounded-lg p-4">
            <h4 className="font-medium mb-2">Resumen:</h4>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-gray-600">Preguntas respondidas:</span>
                <span className="font-medium ml-2">{answeredQuestions}/{questions.length}</span>
              </div>
              <div>
                <span className="text-gray-600">Preguntas marcadas:</span>
                <span className="font-medium ml-2">{flaggedQuestions}</span>
              </div>
              <div>
                <span className="text-gray-600">Tiempo restante:</span>
                <span className="font-medium ml-2">{formatTime(timeRemaining)}</span>
              </div>
            </div>
          </div>

          {answeredQuestions < questions.length && (
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
              <div className="flex items-start">
                <AlertTriangle className="w-5 h-5 text-yellow-600 mr-2 mt-0.5" />
                <div>
                  <p className="text-yellow-800 text-sm">
                    Tienes {questions.length - answeredQuestions} pregunta(s) sin responder.
                  </p>
                </div>
              </div>
            </div>
          )}
        </div>

        <ModalFooter>
          <Button
            variant="outline"
            onClick={() => setIsSubmitModalOpen(false)}
          >
            Continuar Examen
          </Button>
          <Button onClick={handleSubmitExam}>
            Finalizar y Enviar
          </Button>
        </ModalFooter>
      </Modal>
    </div>
  );
}
