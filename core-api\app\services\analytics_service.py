"""
Analytics service for platform metrics and insights
"""

from datetime import datetime, timedelta
from typing import Optional, List, Dict, Any
from uuid import UUID

from sqlmodel import Session, select, func, text

from ..models.analytics import (
    UserScore, UserScoreUpdate, UserScoreResponse,
    PlatformMetrics, PlatformMetricsResponse,
    CourseAnalytics, CourseAnalyticsResponse,
    UserAnalytics, UserAnalyticsResponse,
    LeaderboardEntry, LeaderboardResponse,
    AnalyticsReport, AnalyticsReportCreate, AnalyticsReportResponse,
    MetricType, TimeRange, ReportType
)
from ..models.user import User
from ..models.course import Course
from ..models.enrollment import CourseEnrollment
from ..models.exam import ExamSubmission
from ..models.forum import ForumPost, ForumReply


class AnalyticsService:
    """Analytics service for platform metrics and insights"""
    
    def __init__(self, db: Session):
        self.db = db
    
    async def get_user_score(self, user_id: UUID, tenant_id: UUID) -> Optional[UserScoreResponse]:
        """Get user score and statistics"""
        user_score = self.db.exec(
            select(UserScore).where(
                UserScore.user_id == user_id,
                UserScore.tenant_id == tenant_id
            )
        ).first()
        
        if not user_score:
            # Create default user score
            user_score = UserScore(
                user_id=user_id,
                tenant_id=tenant_id
            )
            self.db.add(user_score)
            self.db.commit()
            self.db.refresh(user_score)
        
        # Get user info
        user = self.db.get(User, user_id)
        user_name = f"{user.first_name} {user.last_name}" if user else None
        
        # Calculate rank
        rank = self.db.exec(
            select(func.count(UserScore.user_id)).where(
                UserScore.tenant_id == tenant_id,
                UserScore.total_points > user_score.total_points
            )
        ).first() or 0
        rank += 1  # Convert to 1-based ranking
        
        return UserScoreResponse(
            user_id=user_score.user_id,
            total_points=user_score.total_points,
            course_points=user_score.course_points,
            exam_points=user_score.exam_points,
            forum_points=user_score.forum_points,
            bonus_points=user_score.bonus_points,
            current_streak=user_score.current_streak,
            longest_streak=user_score.longest_streak,
            achievements=user_score.achievements,
            statistics=user_score.statistics,
            last_activity=user_score.last_activity,
            created_at=user_score.created_at,
            updated_at=user_score.updated_at,
            user_name=user_name,
            rank=rank
        )
    
    async def update_user_score(self, user_id: UUID, score_data: UserScoreUpdate, tenant_id: UUID) -> UserScore:
        """Update user score"""
        user_score = self.db.exec(
            select(UserScore).where(
                UserScore.user_id == user_id,
                UserScore.tenant_id == tenant_id
            )
        ).first()
        
        if not user_score:
            user_score = UserScore(
                user_id=user_id,
                tenant_id=tenant_id
            )
        
        # Update fields
        if score_data.course_points is not None:
            user_score.course_points += score_data.course_points
        
        if score_data.exam_points is not None:
            user_score.exam_points += score_data.exam_points
        
        if score_data.forum_points is not None:
            user_score.forum_points += score_data.forum_points
        
        if score_data.bonus_points is not None:
            user_score.bonus_points += score_data.bonus_points
        
        # Recalculate total points
        user_score.total_points = (
            user_score.course_points + 
            user_score.exam_points + 
            user_score.forum_points + 
            user_score.bonus_points
        )
        
        # Update streak
        if score_data.activity_date:
            await self._update_streak(user_score, score_data.activity_date)
        
        # Update achievements
        if score_data.achievements:
            current_achievements = set(user_score.achievements or [])
            new_achievements = set(score_data.achievements)
            user_score.achievements = list(current_achievements.union(new_achievements))
        
        # Update statistics
        if score_data.statistics:
            current_stats = user_score.statistics or {}
            current_stats.update(score_data.statistics)
            user_score.statistics = current_stats
        
        user_score.last_activity = datetime.utcnow()
        user_score.updated_at = datetime.utcnow()
        
        self.db.add(user_score)
        self.db.commit()
        self.db.refresh(user_score)
        
        return user_score
    
    async def get_leaderboard(
        self, 
        tenant_id: UUID,
        time_range: TimeRange = TimeRange.ALL_TIME,
        metric_type: MetricType = MetricType.TOTAL_POINTS,
        limit: int = 50
    ) -> LeaderboardResponse:
        """Get leaderboard for tenant"""
        # Base query
        stmt = select(UserScore, User).join(User).where(
            UserScore.tenant_id == tenant_id
        )
        
        # Apply time range filter
        if time_range != TimeRange.ALL_TIME:
            cutoff_date = self._get_cutoff_date(time_range)
            stmt = stmt.where(UserScore.last_activity >= cutoff_date)
        
        # Order by metric type
        if metric_type == MetricType.TOTAL_POINTS:
            stmt = stmt.order_by(UserScore.total_points.desc())
        elif metric_type == MetricType.COURSE_POINTS:
            stmt = stmt.order_by(UserScore.course_points.desc())
        elif metric_type == MetricType.EXAM_POINTS:
            stmt = stmt.order_by(UserScore.exam_points.desc())
        elif metric_type == MetricType.FORUM_POINTS:
            stmt = stmt.order_by(UserScore.forum_points.desc())
        elif metric_type == MetricType.CURRENT_STREAK:
            stmt = stmt.order_by(UserScore.current_streak.desc())
        
        stmt = stmt.limit(limit)
        results = self.db.exec(stmt).all()
        
        # Build leaderboard entries
        entries = []
        for rank, (user_score, user) in enumerate(results, 1):
            entry = LeaderboardEntry(
                rank=rank,
                user_id=user.user_id,
                user_name=f"{user.first_name} {user.last_name}",
                user_avatar=user.avatar_url,
                total_points=user_score.total_points,
                course_points=user_score.course_points,
                exam_points=user_score.exam_points,
                forum_points=user_score.forum_points,
                current_streak=user_score.current_streak,
                achievements=user_score.achievements or [],
                last_activity=user_score.last_activity
            )
            entries.append(entry)
        
        return LeaderboardResponse(
            time_range=time_range,
            metric_type=metric_type,
            entries=entries,
            total_users=len(entries),
            generated_at=datetime.utcnow()
        )
    
    async def get_platform_metrics(self, tenant_id: UUID, time_range: TimeRange = TimeRange.LAST_30_DAYS) -> PlatformMetricsResponse:
        """Get platform-wide metrics"""
        cutoff_date = self._get_cutoff_date(time_range)
        
        # User metrics
        total_users = self.db.exec(
            select(func.count(User.user_id)).where(
                User.tenant_id == tenant_id,
                User.is_active == True
            )
        ).first() or 0
        
        active_users = self.db.exec(
            select(func.count(User.user_id)).where(
                User.tenant_id == tenant_id,
                User.is_active == True,
                User.last_login_at >= cutoff_date
            )
        ).first() or 0
        
        new_users = self.db.exec(
            select(func.count(User.user_id)).where(
                User.tenant_id == tenant_id,
                User.created_at >= cutoff_date
            )
        ).first() or 0
        
        # Course metrics
        total_courses = self.db.exec(
            select(func.count(Course.course_id)).where(
                Course.tenant_id == tenant_id,
                Course.status == "published"
            )
        ).first() or 0
        
        total_enrollments = self.db.exec(
            select(func.count(CourseEnrollment.enrollment_id)).where(
                CourseEnrollment.tenant_id == tenant_id
            )
        ).first() or 0
        
        completed_courses = self.db.exec(
            select(func.count(CourseEnrollment.enrollment_id)).where(
                CourseEnrollment.tenant_id == tenant_id,
                CourseEnrollment.status == "completed"
            )
        ).first() or 0
        
        # Exam metrics
        total_exams = self.db.exec(
            select(func.count(ExamSubmission.submission_id)).where(
                ExamSubmission.tenant_id == tenant_id,
                ExamSubmission.submitted_at >= cutoff_date
            )
        ).first() or 0
        
        passed_exams = self.db.exec(
            select(func.count(ExamSubmission.submission_id)).where(
                ExamSubmission.tenant_id == tenant_id,
                ExamSubmission.submitted_at >= cutoff_date,
                ExamSubmission.passed == True
            )
        ).first() or 0
        
        # Forum metrics
        total_posts = self.db.exec(
            select(func.count(ForumPost.post_id)).where(
                ForumPost.tenant_id == tenant_id,
                ForumPost.created_at >= cutoff_date
            )
        ).first() or 0
        
        total_replies = self.db.exec(
            select(func.count(ForumReply.reply_id)).where(
                ForumReply.tenant_id == tenant_id,
                ForumReply.created_at >= cutoff_date
            )
        ).first() or 0
        
        # Calculate rates
        completion_rate = (completed_courses / total_enrollments * 100) if total_enrollments > 0 else 0
        pass_rate = (passed_exams / total_exams * 100) if total_exams > 0 else 0
        engagement_rate = (active_users / total_users * 100) if total_users > 0 else 0
        
        metrics = PlatformMetrics(
            total_users=total_users,
            active_users=active_users,
            new_users=new_users,
            total_courses=total_courses,
            total_enrollments=total_enrollments,
            completed_courses=completed_courses,
            total_exams=total_exams,
            passed_exams=passed_exams,
            total_posts=total_posts,
            total_replies=total_replies,
            completion_rate=completion_rate,
            pass_rate=pass_rate,
            engagement_rate=engagement_rate
        )
        
        return PlatformMetricsResponse(
            time_range=time_range,
            metrics=metrics,
            generated_at=datetime.utcnow()
        )
    
    async def get_course_analytics(self, course_id: UUID, tenant_id: UUID) -> Optional[CourseAnalyticsResponse]:
        """Get analytics for a specific course"""
        course = self.db.get(Course, course_id)
        if not course or course.tenant_id != tenant_id:
            return None
        
        # Enrollment metrics
        total_enrollments = self.db.exec(
            select(func.count(CourseEnrollment.enrollment_id)).where(
                CourseEnrollment.course_id == course_id
            )
        ).first() or 0
        
        active_enrollments = self.db.exec(
            select(func.count(CourseEnrollment.enrollment_id)).where(
                CourseEnrollment.course_id == course_id,
                CourseEnrollment.status == "active"
            )
        ).first() or 0
        
        completed_enrollments = self.db.exec(
            select(func.count(CourseEnrollment.enrollment_id)).where(
                CourseEnrollment.course_id == course_id,
                CourseEnrollment.status == "completed"
            )
        ).first() or 0
        
        # Progress metrics
        avg_progress = self.db.exec(
            select(func.avg(CourseEnrollment.progress_percentage)).where(
                CourseEnrollment.course_id == course_id,
                CourseEnrollment.status == "active"
            )
        ).first() or 0
        
        # Time metrics
        avg_completion_time = self.db.exec(
            select(func.avg(
                func.extract('epoch', CourseEnrollment.completed_at - CourseEnrollment.enrolled_at) / 86400
            )).where(
                CourseEnrollment.course_id == course_id,
                CourseEnrollment.status == "completed"
            )
        ).first() or 0
        
        # Rating metrics
        avg_rating = self.db.exec(
            select(func.avg(CourseEnrollment.rating)).where(
                CourseEnrollment.course_id == course_id,
                CourseEnrollment.rating.isnot(None)
            )
        ).first() or 0
        
        analytics = CourseAnalytics(
            course_id=course_id,
            total_enrollments=total_enrollments,
            active_enrollments=active_enrollments,
            completed_enrollments=completed_enrollments,
            completion_rate=(completed_enrollments / total_enrollments * 100) if total_enrollments > 0 else 0,
            average_progress=float(avg_progress) if avg_progress else 0,
            average_completion_time_days=float(avg_completion_time) if avg_completion_time else 0,
            average_rating=float(avg_rating) if avg_rating else 0,
            dropout_rate=((total_enrollments - active_enrollments - completed_enrollments) / total_enrollments * 100) if total_enrollments > 0 else 0
        )
        
        return CourseAnalyticsResponse(
            course_title=course.title,
            analytics=analytics,
            generated_at=datetime.utcnow()
        )
    
    async def get_user_analytics(self, user_id: UUID, tenant_id: UUID) -> Optional[UserAnalyticsResponse]:
        """Get analytics for a specific user"""
        user = self.db.get(User, user_id)
        if not user or user.tenant_id != tenant_id:
            return None
        
        # Course metrics
        total_enrollments = self.db.exec(
            select(func.count(CourseEnrollment.enrollment_id)).where(
                CourseEnrollment.user_id == user_id
            )
        ).first() or 0
        
        completed_courses = self.db.exec(
            select(func.count(CourseEnrollment.enrollment_id)).where(
                CourseEnrollment.user_id == user_id,
                CourseEnrollment.status == "completed"
            )
        ).first() or 0
        
        # Exam metrics
        total_exams = self.db.exec(
            select(func.count(ExamSubmission.submission_id)).where(
                ExamSubmission.user_id == user_id
            )
        ).first() or 0
        
        passed_exams = self.db.exec(
            select(func.count(ExamSubmission.submission_id)).where(
                ExamSubmission.user_id == user_id,
                ExamSubmission.passed == True
            )
        ).first() or 0
        
        avg_exam_score = self.db.exec(
            select(func.avg(ExamSubmission.score)).where(
                ExamSubmission.user_id == user_id,
                ExamSubmission.score.isnot(None)
            )
        ).first() or 0
        
        # Forum metrics
        total_posts = self.db.exec(
            select(func.count(ForumPost.post_id)).where(
                ForumPost.author_id == user_id
            )
        ).first() or 0
        
        total_replies = self.db.exec(
            select(func.count(ForumReply.reply_id)).where(
                ForumReply.author_id == user_id
            )
        ).first() or 0
        
        # Time metrics
        total_study_time = 0  # This would be calculated from activity logs
        
        analytics = UserAnalytics(
            user_id=user_id,
            total_enrollments=total_enrollments,
            completed_courses=completed_courses,
            completion_rate=(completed_courses / total_enrollments * 100) if total_enrollments > 0 else 0,
            total_exams=total_exams,
            passed_exams=passed_exams,
            exam_pass_rate=(passed_exams / total_exams * 100) if total_exams > 0 else 0,
            average_exam_score=float(avg_exam_score) if avg_exam_score else 0,
            total_posts=total_posts,
            total_replies=total_replies,
            total_study_time_hours=total_study_time,
            login_count=user.login_count,
            last_login=user.last_login_at
        )
        
        return UserAnalyticsResponse(
            user_name=f"{user.first_name} {user.last_name}",
            analytics=analytics,
            generated_at=datetime.utcnow()
        )
    
    def _get_cutoff_date(self, time_range: TimeRange) -> datetime:
        """Get cutoff date for time range"""
        now = datetime.utcnow()
        
        if time_range == TimeRange.LAST_7_DAYS:
            return now - timedelta(days=7)
        elif time_range == TimeRange.LAST_30_DAYS:
            return now - timedelta(days=30)
        elif time_range == TimeRange.LAST_90_DAYS:
            return now - timedelta(days=90)
        elif time_range == TimeRange.LAST_YEAR:
            return now - timedelta(days=365)
        else:
            return datetime.min  # All time
    
    async def _update_streak(self, user_score: UserScore, activity_date: datetime):
        """Update user streak based on activity"""
        if not user_score.last_activity:
            user_score.current_streak = 1
        else:
            days_diff = (activity_date.date() - user_score.last_activity.date()).days
            
            if days_diff == 1:
                # Consecutive day
                user_score.current_streak += 1
            elif days_diff == 0:
                # Same day, no change
                pass
            else:
                # Streak broken
                user_score.current_streak = 1
        
        # Update longest streak
        if user_score.current_streak > user_score.longest_streak:
            user_score.longest_streak = user_score.current_streak
