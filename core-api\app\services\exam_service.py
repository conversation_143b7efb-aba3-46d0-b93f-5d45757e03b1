"""
Exam management service
"""

import random
from datetime import datetime, timedelta
from typing import Optional, List, Dict, Any
from uuid import UUID

from sqlmodel import Session, select, func

from ..models.exam import (
    Exam, ExamCreate, ExamUpdate, ExamResponse, ExamStatus,
    ExamSubmission, ExamSubmissionCreate, ExamSubmissionUpdate, ExamSubmissionResponse,
    SubmissionAnswer, SubmissionAnswerCreate, SubmissionAnswerUpdate,
    ExamResult, ExamStatistics, ExamPreview, ExamGrading, SubmissionStatus
)
from ..models.question import Question
from ..models.course import Course
from ..models.user import User
from ..services.notification_service import NotificationService


class ExamService:
    """Exam management service"""
    
    def __init__(self, db: Session):
        self.db = db
        self.notification_service = NotificationService(db)
    
    async def create_exam(self, exam_data: ExamCreate, created_by: UUID, tenant_id: UUID) -> Exam:
        """Create a new exam"""
        # Calculate total points
        total_points = 0
        if exam_data.question_ids:
            questions = self.db.exec(
                select(Question).where(Question.question_id.in_(exam_data.question_ids))
            ).all()
            total_points = sum(q.points for q in questions)
        
        exam = Exam(
            tenant_id=tenant_id,
            course_id=exam_data.course_id,
            created_by=created_by,
            title=exam_data.title,
            description=exam_data.description,
            instructions=exam_data.instructions,
            time_limit_minutes=exam_data.time_limit_minutes,
            passing_score=exam_data.passing_score,
            max_attempts=exam_data.max_attempts,
            shuffle_questions=exam_data.shuffle_questions,
            shuffle_options=exam_data.shuffle_options,
            show_results=exam_data.show_results,
            allow_review=exam_data.allow_review,
            is_proctored=exam_data.is_proctored,
            question_ids=exam_data.question_ids,
            scheduled_start=exam_data.scheduled_start,
            scheduled_end=exam_data.scheduled_end,
            total_points=total_points
        )
        
        self.db.add(exam)
        self.db.commit()
        self.db.refresh(exam)
        
        return exam
    
    async def get_exam(self, exam_id: UUID, tenant_id: UUID) -> Optional[ExamResponse]:
        """Get exam by ID with additional data"""
        exam = self.db.exec(
            select(Exam).where(
                Exam.exam_id == exam_id,
                Exam.tenant_id == tenant_id
            )
        ).first()
        
        if not exam:
            return None
        
        # Get course title
        course = self.db.get(Course, exam.course_id)
        course_title = course.title if course else None
        
        # Get creator name
        creator = self.db.get(User, exam.created_by)
        creator_name = f"{creator.first_name} {creator.last_name}" if creator else None
        
        # Get submission count
        submission_count = self.db.exec(
            select(func.count(ExamSubmission.submission_id)).where(
                ExamSubmission.exam_id == exam_id
            )
        ).first() or 0
        
        # Get average score
        avg_score = self.db.exec(
            select(func.avg(ExamSubmission.score)).where(
                ExamSubmission.exam_id == exam_id,
                ExamSubmission.status == SubmissionStatus.GRADED
            )
        ).first()
        
        return ExamResponse(
            exam_id=exam.exam_id,
            course_id=exam.course_id,
            created_by=exam.created_by,
            title=exam.title,
            description=exam.description,
            instructions=exam.instructions,
            time_limit_minutes=exam.time_limit_minutes,
            passing_score=exam.passing_score,
            max_attempts=exam.max_attempts,
            shuffle_questions=exam.shuffle_questions,
            shuffle_options=exam.shuffle_options,
            show_results=exam.show_results,
            allow_review=exam.allow_review,
            is_proctored=exam.is_proctored,
            status=exam.status,
            question_ids=exam.question_ids,
            scheduled_start=exam.scheduled_start,
            scheduled_end=exam.scheduled_end,
            published_at=exam.published_at,
            total_points=exam.total_points,
            created_at=exam.created_at,
            updated_at=exam.updated_at,
            course_title=course_title,
            creator_name=creator_name,
            question_count=len(exam.question_ids),
            submission_count=submission_count,
            average_score=float(avg_score) if avg_score else None
        )
    
    async def update_exam(self, exam_id: UUID, exam_data: ExamUpdate, tenant_id: UUID) -> Optional[Exam]:
        """Update exam"""
        exam = self.db.exec(
            select(Exam).where(
                Exam.exam_id == exam_id,
                Exam.tenant_id == tenant_id
            )
        ).first()
        
        if not exam:
            return None
        
        # Update fields
        update_data = exam_data.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(exam, field, value)
        
        # Recalculate total points if questions changed
        if 'question_ids' in update_data and exam.question_ids:
            questions = self.db.exec(
                select(Question).where(Question.question_id.in_(exam.question_ids))
            ).all()
            exam.total_points = sum(q.points for q in questions)
        
        exam.updated_at = datetime.utcnow()
        
        self.db.add(exam)
        self.db.commit()
        self.db.refresh(exam)
        
        return exam
    
    async def publish_exam(self, exam_id: UUID, tenant_id: UUID) -> bool:
        """Publish exam"""
        exam = self.db.exec(
            select(Exam).where(
                Exam.exam_id == exam_id,
                Exam.tenant_id == tenant_id
            )
        ).first()
        
        if not exam:
            return False
        
        exam.status = ExamStatus.PUBLISHED
        exam.published_at = datetime.utcnow()
        exam.updated_at = datetime.utcnow()
        
        self.db.add(exam)
        self.db.commit()
        
        return True
    
    async def start_exam(self, exam_id: UUID, user_id: UUID, tenant_id: UUID) -> Optional[ExamSubmission]:
        """Start exam attempt"""
        exam = self.db.get(Exam, exam_id)
        if not exam:
            return None
        
        # Check if exam is available
        now = datetime.utcnow()
        if exam.scheduled_start and now < exam.scheduled_start:
            raise ValueError("Exam has not started yet")
        
        if exam.scheduled_end and now > exam.scheduled_end:
            raise ValueError("Exam has ended")
        
        # Check attempt limit
        existing_attempts = self.db.exec(
            select(func.count(ExamSubmission.submission_id)).where(
                ExamSubmission.exam_id == exam_id,
                ExamSubmission.user_id == user_id
            )
        ).first() or 0
        
        if existing_attempts >= exam.max_attempts:
            raise ValueError("Maximum attempts exceeded")
        
        # Create submission
        submission = ExamSubmission(
            tenant_id=tenant_id,
            exam_id=exam_id,
            user_id=user_id,
            attempt_number=existing_attempts + 1
        )
        
        self.db.add(submission)
        self.db.commit()
        self.db.refresh(submission)
        
        return submission
    
    async def get_exam_questions(self, exam_id: UUID, user_id: UUID, shuffle: bool = True) -> List[Question]:
        """Get exam questions for user"""
        exam = self.db.get(Exam, exam_id)
        if not exam:
            return []
        
        # Get questions
        questions = self.db.exec(
            select(Question).where(Question.question_id.in_(exam.question_ids))
        ).all()
        
        # Sort by original order first
        question_dict = {q.question_id: q for q in questions}
        ordered_questions = [question_dict[qid] for qid in exam.question_ids if qid in question_dict]
        
        # Shuffle if enabled
        if shuffle and exam.shuffle_questions:
            random.shuffle(ordered_questions)
        
        return ordered_questions
    
    async def submit_answer(
        self, 
        submission_id: UUID, 
        answer_data: SubmissionAnswerCreate, 
        tenant_id: UUID
    ) -> SubmissionAnswer:
        """Submit answer for a question"""
        # Check if answer already exists
        existing_answer = self.db.exec(
            select(SubmissionAnswer).where(
                SubmissionAnswer.submission_id == submission_id,
                SubmissionAnswer.question_id == answer_data.question_id
            )
        ).first()
        
        if existing_answer:
            # Update existing answer
            existing_answer.answer_data = answer_data.answer_data
            existing_answer.updated_at = datetime.utcnow()
            
            self.db.add(existing_answer)
            self.db.commit()
            self.db.refresh(existing_answer)
            
            return existing_answer
        else:
            # Create new answer
            answer = SubmissionAnswer(
                tenant_id=tenant_id,
                submission_id=submission_id,
                question_id=answer_data.question_id,
                answer_data=answer_data.answer_data
            )
            
            self.db.add(answer)
            self.db.commit()
            self.db.refresh(answer)
            
            return answer
    
    async def submit_exam(self, submission_id: UUID, user_id: UUID) -> ExamResult:
        """Submit exam for grading"""
        submission = self.db.exec(
            select(ExamSubmission).where(
                ExamSubmission.submission_id == submission_id,
                ExamSubmission.user_id == user_id
            )
        ).first()
        
        if not submission:
            raise ValueError("Submission not found")
        
        if submission.status != SubmissionStatus.IN_PROGRESS:
            raise ValueError("Submission already completed")
        
        # Update submission
        submission.submitted_at = datetime.utcnow()
        submission.status = SubmissionStatus.SUBMITTED
        submission.time_spent_seconds = int((submission.submitted_at - submission.started_at).total_seconds())
        
        # Auto-grade objective questions
        await self._auto_grade_submission(submission)
        
        self.db.add(submission)
        self.db.commit()
        
        # Generate result
        return await self.get_exam_result(submission_id, user_id)
    
    async def get_exam_result(self, submission_id: UUID, user_id: UUID) -> ExamResult:
        """Get exam result"""
        submission = self.db.exec(
            select(ExamSubmission).where(
                ExamSubmission.submission_id == submission_id,
                ExamSubmission.user_id == user_id
            )
        ).first()
        
        if not submission:
            raise ValueError("Submission not found")
        
        exam = self.db.get(Exam, submission.exam_id)
        if not exam:
            raise ValueError("Exam not found")
        
        # Get answers
        answers = self.db.exec(
            select(SubmissionAnswer).where(
                SubmissionAnswer.submission_id == submission_id
            )
        ).all()
        
        # Calculate results
        total_questions = len(exam.question_ids)
        correct_answers = len([a for a in answers if a.is_correct])
        score = submission.score or 0
        percentage = (score / exam.total_points * 100) if exam.total_points > 0 else 0
        passed = percentage >= exam.passing_score
        
        return ExamResult(
            submission=ExamSubmissionResponse(
                submission_id=submission.submission_id,
                exam_id=submission.exam_id,
                user_id=submission.user_id,
                started_at=submission.started_at,
                submitted_at=submission.submitted_at,
                time_spent_seconds=submission.time_spent_seconds,
                status=submission.status,
                score=submission.score,
                passed=submission.passed,
                attempt_number=submission.attempt_number,
                graded_by=submission.graded_by,
                graded_at=submission.graded_at,
                feedback=submission.feedback,
                created_at=submission.created_at,
                updated_at=submission.updated_at
            ),
            exam=await self.get_exam(exam.exam_id, exam.tenant_id),
            answers=answers,
            total_questions=total_questions,
            correct_answers=correct_answers,
            score=score,
            percentage=percentage,
            passed=passed,
            time_spent_seconds=submission.time_spent_seconds,
            feedback=submission.feedback
        )
    
    async def _auto_grade_submission(self, submission: ExamSubmission):
        """Auto-grade objective questions in submission"""
        answers = self.db.exec(
            select(SubmissionAnswer).where(
                SubmissionAnswer.submission_id == submission.submission_id
            )
        ).all()
        
        total_score = 0
        
        for answer in answers:
            question = self.db.get(Question, answer.question_id)
            if not question:
                continue
            
            # Auto-grade based on question type
            if question.question_type in ['multiple_choice', 'true_false']:
                correct_answer = question.question_data.get('correct_answer')
                user_answer = answer.answer_data.get('answer')
                
                if user_answer == correct_answer:
                    answer.is_correct = True
                    answer.points_earned = question.points
                    total_score += question.points
                else:
                    answer.is_correct = False
                    answer.points_earned = 0
                
                answer.auto_graded = True
                self.db.add(answer)
        
        # Update submission score
        submission.score = total_score
        submission.passed = (total_score / submission.exam.total_points * 100) >= submission.exam.passing_score if submission.exam.total_points > 0 else False
        
        # If all questions are auto-gradable, mark as graded
        exam = self.db.get(Exam, submission.exam_id)
        questions = self.db.exec(
            select(Question).where(Question.question_id.in_(exam.question_ids))
        ).all()
        
        auto_gradable_types = ['multiple_choice', 'true_false']
        if all(q.question_type in auto_gradable_types for q in questions):
            submission.status = SubmissionStatus.GRADED
            submission.graded_at = datetime.utcnow()
        
        self.db.commit()
