"""
Forum service for course discussions
"""

from datetime import datetime
from typing import Optional, List
from uuid import UUID

from sqlmodel import Session, select, func

from ..models.forum import (
    ForumCategory, ForumCategoryCreate, ForumCategoryUpdate, ForumCategoryResponse,
    ForumPost, ForumPostCreate, ForumPostUpdate, ForumPostResponse,
    ForumReply, ForumReplyCreate, ForumReplyUpdate, ForumReplyResponse,
    ForumLike, ForumLikeCreate, ForumModeration, ForumModerationCreate,
    ForumReport, ForumReportCreate, ForumSearch, ForumStatistics,
    PostStatus, ModerationAction
)
from ..models.user import User
from ..services.notification_service import NotificationService


class ForumService:
    """Forum service for course discussions"""
    
    def __init__(self, db: Session):
        self.db = db
        self.notification_service = NotificationService(db)
    
    async def create_category(self, category_data: ForumCategoryCreate, created_by: UUID, tenant_id: UUID) -> ForumCategory:
        """Create a new forum category"""
        category = ForumCategory(
            tenant_id=tenant_id,
            course_id=category_data.course_id,
            created_by=created_by,
            name=category_data.name,
            description=category_data.description,
            order_index=category_data.order_index,
            is_active=category_data.is_active,
            color=category_data.color
        )
        
        self.db.add(category)
        self.db.commit()
        self.db.refresh(category)
        
        return category
    
    async def get_course_categories(self, course_id: UUID, tenant_id: UUID) -> List[ForumCategoryResponse]:
        """Get all categories for a course"""
        stmt = select(ForumCategory).where(
            ForumCategory.course_id == course_id,
            ForumCategory.tenant_id == tenant_id,
            ForumCategory.is_active == True
        ).order_by(ForumCategory.order_index)
        
        categories = self.db.exec(stmt).all()
        
        # Convert to response models with statistics
        category_responses = []
        for category in categories:
            # Get post count
            post_count = self.db.exec(
                select(func.count(ForumPost.post_id)).where(
                    ForumPost.category_id == category.category_id,
                    ForumPost.status == PostStatus.ACTIVE
                )
            ).first() or 0
            
            # Get reply count
            reply_count = self.db.exec(
                select(func.count(ForumReply.reply_id)).join(ForumPost).where(
                    ForumPost.category_id == category.category_id,
                    ForumPost.status == PostStatus.ACTIVE,
                    ForumReply.status == PostStatus.ACTIVE
                )
            ).first() or 0
            
            # Get last activity
            last_activity = self.db.exec(
                select(func.max(ForumPost.updated_at)).where(
                    ForumPost.category_id == category.category_id,
                    ForumPost.status == PostStatus.ACTIVE
                )
            ).first()
            
            category_response = ForumCategoryResponse(
                category_id=category.category_id,
                course_id=category.course_id,
                created_by=category.created_by,
                name=category.name,
                description=category.description,
                order_index=category.order_index,
                is_active=category.is_active,
                color=category.color,
                created_at=category.created_at,
                updated_at=category.updated_at,
                post_count=post_count,
                reply_count=reply_count,
                last_activity=last_activity
            )
            category_responses.append(category_response)
        
        return category_responses
    
    async def create_post(self, post_data: ForumPostCreate, author_id: UUID, tenant_id: UUID) -> ForumPost:
        """Create a new forum post"""
        # Get category to get course_id
        category = self.db.get(ForumCategory, post_data.category_id)
        if not category:
            raise ValueError("Category not found")
        
        post = ForumPost(
            tenant_id=tenant_id,
            category_id=post_data.category_id,
            course_id=category.course_id,
            author_id=author_id,
            title=post_data.title,
            content=post_data.content,
            is_pinned=post_data.is_pinned,
            is_locked=post_data.is_locked,
            is_highlighted=post_data.is_highlighted,
            status=post_data.status,
            tags=post_data.tags or []
        )
        
        self.db.add(post)
        self.db.commit()
        self.db.refresh(post)
        
        return post
    
    async def get_post(self, post_id: UUID, tenant_id: UUID, user_id: UUID = None) -> Optional[ForumPostResponse]:
        """Get post by ID with additional data"""
        post = self.db.exec(
            select(ForumPost).where(
                ForumPost.post_id == post_id,
                ForumPost.tenant_id == tenant_id
            )
        ).first()
        
        if not post:
            return None
        
        # Increment view count
        post.view_count += 1
        self.db.add(post)
        self.db.commit()
        
        # Get author info
        author = self.db.get(User, post.author_id)
        author_name = f"{author.first_name} {author.last_name}" if author else None
        author_avatar = author.avatar_url if author else None
        
        # Check if author is expert (has expert role)
        author_is_expert = False  # This would check user roles
        
        # Get category name
        category = self.db.get(ForumCategory, post.category_id)
        category_name = category.name if category else None
        
        # Get last reply author
        last_reply_author = None
        if post.last_reply_by:
            last_reply_user = self.db.get(User, post.last_reply_by)
            last_reply_author = f"{last_reply_user.first_name} {last_reply_user.last_name}" if last_reply_user else None
        
        # Check if current user has liked the post
        user_has_liked = False
        if user_id:
            like = self.db.exec(
                select(ForumLike).where(
                    ForumLike.post_id == post_id,
                    ForumLike.user_id == user_id
                )
            ).first()
            user_has_liked = like is not None
        
        return ForumPostResponse(
            post_id=post.post_id,
            category_id=post.category_id,
            course_id=post.course_id,
            author_id=post.author_id,
            title=post.title,
            content=post.content,
            is_pinned=post.is_pinned,
            is_locked=post.is_locked,
            is_highlighted=post.is_highlighted,
            status=post.status,
            tags=post.tags,
            view_count=post.view_count,
            like_count=post.like_count,
            reply_count=post.reply_count,
            last_reply_at=post.last_reply_at,
            last_reply_by=post.last_reply_by,
            created_at=post.created_at,
            updated_at=post.updated_at,
            author_name=author_name,
            author_avatar=author_avatar,
            author_is_expert=author_is_expert,
            category_name=category_name,
            last_reply_author=last_reply_author,
            user_has_liked=user_has_liked
        )
    
    async def list_posts(
        self,
        category_id: UUID = None,
        course_id: UUID = None,
        tenant_id: UUID = None,
        skip: int = 0,
        limit: int = 20,
        search: ForumSearch = None,
        user_id: UUID = None
    ) -> List[ForumPostResponse]:
        """List posts with filtering"""
        stmt = select(ForumPost).where(ForumPost.status == PostStatus.ACTIVE)
        
        if tenant_id:
            stmt = stmt.where(ForumPost.tenant_id == tenant_id)
        
        if category_id:
            stmt = stmt.where(ForumPost.category_id == category_id)
        
        if course_id:
            stmt = stmt.where(ForumPost.course_id == course_id)
        
        # Apply search filters
        if search:
            if search.query:
                search_term = f"%{search.query}%"
                stmt = stmt.where(
                    (ForumPost.title.ilike(search_term)) |
                    (ForumPost.content.ilike(search_term))
                )
            
            if search.author_id:
                stmt = stmt.where(ForumPost.author_id == search.author_id)
            
            if search.tags:
                for tag in search.tags:
                    stmt = stmt.where(ForumPost.tags.contains([tag]))
            
            if search.is_pinned is not None:
                stmt = stmt.where(ForumPost.is_pinned == search.is_pinned)
            
            if search.is_highlighted is not None:
                stmt = stmt.where(ForumPost.is_highlighted == search.is_highlighted)
            
            if search.date_from:
                stmt = stmt.where(ForumPost.created_at >= search.date_from)
            
            if search.date_to:
                stmt = stmt.where(ForumPost.created_at <= search.date_to)
        
        # Order by pinned first, then by last activity
        stmt = stmt.order_by(
            ForumPost.is_pinned.desc(),
            ForumPost.last_reply_at.desc().nullslast(),
            ForumPost.created_at.desc()
        ).offset(skip).limit(limit)
        
        posts = self.db.exec(stmt).all()
        
        # Convert to response models
        post_responses = []
        for post in posts:
            post_response = await self.get_post(post.post_id, post.tenant_id, user_id)
            if post_response:
                # Don't increment view count for list view
                post_response.view_count = post.view_count
                post_responses.append(post_response)
        
        return post_responses
    
    async def create_reply(self, reply_data: ForumReplyCreate, author_id: UUID, tenant_id: UUID) -> ForumReply:
        """Create a new reply to a post"""
        reply = ForumReply(
            tenant_id=tenant_id,
            post_id=reply_data.post_id,
            author_id=author_id,
            parent_reply_id=reply_data.parent_reply_id,
            content=reply_data.content,
            is_solution=reply_data.is_solution,
            status=reply_data.status
        )
        
        self.db.add(reply)
        self.db.commit()
        self.db.refresh(reply)
        
        # Update post reply count and last reply info
        post = self.db.get(ForumPost, reply_data.post_id)
        if post:
            post.reply_count += 1
            post.last_reply_at = datetime.utcnow()
            post.last_reply_by = author_id
            self.db.add(post)
            self.db.commit()
        
        # Send notification to post author
        if post and post.author_id != author_id:
            author = self.db.get(User, author_id)
            if author:
                await self.notification_service.create_notification({
                    "user_id": post.author_id,
                    "title": "New Reply",
                    "message": f"{author.first_name} replied to your post: {post.title}",
                    "notification_type": "forum_reply",
                    "related_id": post.post_id,
                    "related_type": "forum_post"
                })
        
        return reply
    
    async def get_post_replies(self, post_id: UUID, tenant_id: UUID, user_id: UUID = None) -> List[ForumReplyResponse]:
        """Get all replies for a post"""
        stmt = select(ForumReply).where(
            ForumReply.post_id == post_id,
            ForumReply.tenant_id == tenant_id,
            ForumReply.status == PostStatus.ACTIVE
        ).order_by(ForumReply.created_at)
        
        replies = self.db.exec(stmt).all()
        
        # Convert to response models with nested structure
        reply_responses = []
        reply_dict = {}
        
        for reply in replies:
            # Get author info
            author = self.db.get(User, reply.author_id)
            author_name = f"{author.first_name} {author.last_name}" if author else None
            author_avatar = author.avatar_url if author else None
            author_is_expert = False  # This would check user roles
            
            # Check if current user has liked the reply
            user_has_liked = False
            if user_id:
                like = self.db.exec(
                    select(ForumLike).where(
                        ForumLike.reply_id == reply.reply_id,
                        ForumLike.user_id == user_id
                    )
                ).first()
                user_has_liked = like is not None
            
            reply_response = ForumReplyResponse(
                reply_id=reply.reply_id,
                post_id=reply.post_id,
                author_id=reply.author_id,
                parent_reply_id=reply.parent_reply_id,
                content=reply.content,
                is_solution=reply.is_solution,
                status=reply.status,
                like_count=reply.like_count,
                created_at=reply.created_at,
                updated_at=reply.updated_at,
                author_name=author_name,
                author_avatar=author_avatar,
                author_is_expert=author_is_expert,
                user_has_liked=user_has_liked,
                replies=[]
            )
            
            reply_dict[reply.reply_id] = reply_response
            
            # Build nested structure
            if reply.parent_reply_id and reply.parent_reply_id in reply_dict:
                reply_dict[reply.parent_reply_id].replies.append(reply_response)
            else:
                reply_responses.append(reply_response)
        
        return reply_responses
    
    async def toggle_like(self, like_data: ForumLikeCreate, user_id: UUID, tenant_id: UUID) -> bool:
        """Toggle like on post or reply"""
        # Check if like already exists
        stmt = select(ForumLike).where(
            ForumLike.user_id == user_id,
            ForumLike.tenant_id == tenant_id
        )
        
        if like_data.post_id:
            stmt = stmt.where(ForumLike.post_id == like_data.post_id)
        elif like_data.reply_id:
            stmt = stmt.where(ForumLike.reply_id == like_data.reply_id)
        else:
            raise ValueError("Either post_id or reply_id must be provided")
        
        existing_like = self.db.exec(stmt).first()
        
        if existing_like:
            # Remove like
            self.db.delete(existing_like)
            
            # Update like count
            if like_data.post_id:
                post = self.db.get(ForumPost, like_data.post_id)
                if post:
                    post.like_count = max(0, post.like_count - 1)
                    self.db.add(post)
            elif like_data.reply_id:
                reply = self.db.get(ForumReply, like_data.reply_id)
                if reply:
                    reply.like_count = max(0, reply.like_count - 1)
                    self.db.add(reply)
            
            self.db.commit()
            return False
        else:
            # Add like
            like = ForumLike(
                user_id=user_id,
                tenant_id=tenant_id,
                post_id=like_data.post_id,
                reply_id=like_data.reply_id
            )
            
            self.db.add(like)
            
            # Update like count
            if like_data.post_id:
                post = self.db.get(ForumPost, like_data.post_id)
                if post:
                    post.like_count += 1
                    self.db.add(post)
            elif like_data.reply_id:
                reply = self.db.get(ForumReply, like_data.reply_id)
                if reply:
                    reply.like_count += 1
                    self.db.add(reply)
            
            self.db.commit()
            return True
    
    async def moderate_content(self, moderation_data: ForumModerationCreate, moderator_id: UUID, tenant_id: UUID) -> bool:
        """Moderate forum content"""
        moderation = ForumModeration(
            tenant_id=tenant_id,
            post_id=moderation_data.post_id,
            reply_id=moderation_data.reply_id,
            moderator_id=moderator_id,
            action=moderation_data.action,
            reason=moderation_data.reason,
            notes=moderation_data.notes
        )
        
        self.db.add(moderation)
        
        # Apply moderation action
        if moderation_data.post_id:
            post = self.db.get(ForumPost, moderation_data.post_id)
            if post:
                if moderation_data.action == ModerationAction.LOCK:
                    post.is_locked = True
                elif moderation_data.action == ModerationAction.UNLOCK:
                    post.is_locked = False
                elif moderation_data.action == ModerationAction.PIN:
                    post.is_pinned = True
                elif moderation_data.action == ModerationAction.UNPIN:
                    post.is_pinned = False
                elif moderation_data.action == ModerationAction.HIGHLIGHT:
                    post.is_highlighted = True
                elif moderation_data.action == ModerationAction.UNHIGHLIGHT:
                    post.is_highlighted = False
                
                self.db.add(post)
        
        self.db.commit()
        return True
