import { apiClient } from './api';
import { Course, CourseForm, CourseFilters, PaginatedResponse, ApiResponse } from '@/types';

export const courseService = {
  // Get all courses with filters and pagination
  getCourses: async (
    filters?: CourseFilters,
    page = 1,
    limit = 20
  ): Promise<PaginatedResponse<Course>> => {
    const params = new URLSearchParams({
      page: page.toString(),
      limit: limit.toString(),
      ...(filters?.category && { category: filters.category }),
      ...(filters?.level && { level: filters.level }),
      ...(filters?.search && { search: filters.search }),
      ...(filters?.sortBy && { sortBy: filters.sortBy }),
      ...(filters?.sortOrder && { sortOrder: filters.sortOrder }),
    });

    return apiClient.get<PaginatedResponse<Course>>(`/courses?${params}`);
  },

  // Get featured courses
  getFeaturedCourses: async (): Promise<Course[]> => {
    return apiClient.get<ApiResponse<Course[]>>('/courses/featured')
      .then(response => response.data);
  },

  // Get course by ID
  getCourse: async (id: string): Promise<Course> => {
    return apiClient.get<ApiResponse<Course>>(`/courses/${id}`)
      .then(response => response.data);
  },

  // Create new course
  createCourse: async (courseData: CourseForm): Promise<Course> => {
    return apiClient.post<ApiResponse<Course>>('/courses', courseData)
      .then(response => response.data);
  },

  // Update course
  updateCourse: async (id: string, courseData: Partial<CourseForm>): Promise<Course> => {
    return apiClient.patch<ApiResponse<Course>>(`/courses/${id}`, courseData)
      .then(response => response.data);
  },

  // Delete course
  deleteCourse: async (id: string): Promise<void> => {
    return apiClient.delete(`/courses/${id}`);
  },

  // Enroll in course
  enrollCourse: async (id: string): Promise<void> => {
    return apiClient.post(`/courses/${id}/enroll`);
  },

  // Unenroll from course
  unenrollCourse: async (id: string): Promise<void> => {
    return apiClient.delete(`/courses/${id}/enroll`);
  },

  // Get user's enrolled courses
  getEnrolledCourses: async (): Promise<Course[]> => {
    return apiClient.get<ApiResponse<Course[]>>('/courses/enrolled')
      .then(response => response.data);
  },

  // Get user's created courses
  getCreatedCourses: async (): Promise<Course[]> => {
    return apiClient.get<ApiResponse<Course[]>>('/courses/created')
      .then(response => response.data);
  },

  // Rate course (like/dislike)
  rateCourse: async (id: string, rating: 'like' | 'dislike'): Promise<void> => {
    return apiClient.post(`/courses/${id}/rate`, { rating });
  },

  // Remove course rating
  removeRating: async (id: string): Promise<void> => {
    return apiClient.delete(`/courses/${id}/rate`);
  },

  // Save/bookmark course
  saveCourse: async (id: string): Promise<void> => {
    return apiClient.post(`/courses/${id}/save`);
  },

  // Unsave/unbookmark course
  unsaveCourse: async (id: string): Promise<void> => {
    return apiClient.delete(`/courses/${id}/save`);
  },

  // Get saved courses
  getSavedCourses: async (): Promise<Course[]> => {
    return apiClient.get<ApiResponse<Course[]>>('/courses/saved')
      .then(response => response.data);
  },

  // Report course
  reportCourse: async (id: string, reason: string, comment?: string): Promise<void> => {
    return apiClient.post(`/courses/${id}/report`, { reason, comment });
  },

  // Get course categories
  getCategories: async (): Promise<string[]> => {
    return apiClient.get<ApiResponse<string[]>>('/courses/categories')
      .then(response => response.data);
  },

  // Publish course
  publishCourse: async (id: string): Promise<Course> => {
    return apiClient.post<ApiResponse<Course>>(`/courses/${id}/publish`)
      .then(response => response.data);
  },

  // Archive course
  archiveCourse: async (id: string): Promise<Course> => {
    return apiClient.post<ApiResponse<Course>>(`/courses/${id}/archive`)
      .then(response => response.data);
  },
};
