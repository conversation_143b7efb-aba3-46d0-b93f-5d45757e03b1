"""
AI router for AI-powered features
"""

from fastapi import APIRouter, Depends, HTTPException, status, UploadFile, File
from sqlmodel import Session
from typing import Optional, List
from uuid import UUID

from ..core.database import get_session
from ..core.security import get_current_user, Permissions, require_permissions
from ..services.ai_service import AIService
from ..models.ai import (
    QuestionGenerationRequest, QuestionGenerationResponse,
    ScoringRequest, ScoringResponse,
    ModerationRequest, ModerationResponse,
    SpeechSynthesisRequest, SpeechSynthesisResponse,
    SpeechRecognitionRequest, SpeechRecognitionResponse,
    PlagiarismCheckRequest, PlagiarismResponse,
    AITaskResponse, AITaskStatus
)
from ..models.base import SuccessResponse

router = APIRouter()


def get_ai_service(db: Session = Depends(get_session)) -> AIService:
    """Get AI service"""
    return AIService(db)


@router.post("/generate/questions", response_model=QuestionGenerationResponse)
@require_permissions([Permissions.QUESTION_CREATE])
async def generate_questions(
    request: QuestionGenerationRequest,
    current_user = Depends(get_current_user),
    ai_service: AIService = Depends(get_ai_service)
):
    """Generate questions using AI"""
    try:
        response = await ai_service.generate_questions(
            request=request,
            user_id=current_user.user_id,
            tenant_id=current_user.tenant_id
        )
        
        return response
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to generate questions"
        )


@router.post("/score/content", response_model=ScoringResponse)
@require_permissions([Permissions.EXAM_GRADE])
async def score_content(
    request: ScoringRequest,
    current_user = Depends(get_current_user),
    ai_service: AIService = Depends(get_ai_service)
):
    """Score content using AI"""
    try:
        response = await ai_service.score_content(
            request=request,
            user_id=current_user.user_id,
            tenant_id=current_user.tenant_id
        )
        
        return response
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to score content"
        )


@router.post("/moderate/content", response_model=ModerationResponse)
async def moderate_content(
    request: ModerationRequest,
    current_user = Depends(get_current_user),
    ai_service: AIService = Depends(get_ai_service)
):
    """Moderate content using AI"""
    try:
        response = await ai_service.moderate_content(
            request=request,
            user_id=current_user.user_id,
            tenant_id=current_user.tenant_id
        )
        
        return response
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to moderate content"
        )


@router.post("/speech/synthesize", response_model=SpeechSynthesisResponse)
@require_permissions([Permissions.COURSE_CREATE])
async def synthesize_speech(
    request: SpeechSynthesisRequest,
    current_user = Depends(get_current_user),
    ai_service: AIService = Depends(get_ai_service)
):
    """Convert text to speech"""
    try:
        response = await ai_service.synthesize_speech(
            request=request,
            user_id=current_user.user_id,
            tenant_id=current_user.tenant_id
        )

        return response
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to synthesize speech"
        )


@router.post("/speech/recognize", response_model=SpeechRecognitionResponse)
async def recognize_speech(
    request: SpeechRecognitionRequest,
    current_user = Depends(get_current_user),
    ai_service: AIService = Depends(get_ai_service)
):
    """Convert speech to text"""
    try:
        response = await ai_service.recognize_speech(
            request=request,
            user_id=current_user.user_id,
            tenant_id=current_user.tenant_id
        )

        return response
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to recognize speech"
        )


@router.post("/plagiarism/check", response_model=PlagiarismResponse)
@require_permissions([Permissions.EXAM_GRADE])
async def check_plagiarism(
    request: PlagiarismCheckRequest,
    current_user = Depends(get_current_user),
    ai_service: AIService = Depends(get_ai_service)
):
    """Check content for plagiarism"""
    try:
        response = await ai_service.check_plagiarism(
            request=request,
            user_id=current_user.user_id,
            tenant_id=current_user.tenant_id
        )

        return response
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to check plagiarism"
        )


@router.get("/tasks/{task_id}", response_model=AITaskResponse)
async def get_ai_task(
    task_id: UUID,
    current_user = Depends(get_current_user),
    ai_service: AIService = Depends(get_ai_service)
):
    """Get AI task status and results"""
    try:
        # This would be implemented to get task from database
        return {
            "task_id": task_id,
            "task_type": "question_generation",
            "status": "completed",
            "input_data": {},
            "output_data": {},
            "error_message": None,
            "processing_time_seconds": 5.2,
            "cost_usd": 0.15,
            "model_used": "gpt-4-turbo-preview",
            "tokens_used": 1500,
            "user_id": current_user.user_id,
            "started_at": "2024-01-01T00:00:00Z",
            "completed_at": "2024-01-01T00:00:05Z",
            "created_at": "2024-01-01T00:00:00Z",
            "updated_at": "2024-01-01T00:00:05Z"
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve task"
        )


@router.get("/tasks")
async def list_ai_tasks(
    skip: int = 0,
    limit: int = 20,
    task_type: Optional[str] = None,
    status: Optional[AITaskStatus] = None,
    current_user = Depends(get_current_user),
    ai_service: AIService = Depends(get_ai_service)
):
    """List AI tasks for current user"""
    try:
        # This would be implemented to list tasks from database
        return {
            "success": True,
            "data": {
                "tasks": [],
                "total": 0,
                "page": skip // limit + 1,
                "size": limit
            }
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve tasks"
        )


@router.get("/quota")
async def get_ai_quota(
    current_user = Depends(get_current_user),
    ai_service: AIService = Depends(get_ai_service)
):
    """Get AI quota usage for current tenant"""
    try:
        # This would be implemented to get quota from database
        return {
            "success": True,
            "data": {
                "daily_requests_used": 45,
                "daily_requests_limit": 1000,
                "monthly_requests_used": 1250,
                "monthly_requests_limit": 10000,
                "daily_cost_used_usd": 12.50,
                "daily_cost_limit_usd": 100.0,
                "monthly_cost_used_usd": 285.75,
                "monthly_cost_limit_usd": 1000.0,
                "percentage_used": {
                    "daily_requests": 4.5,
                    "monthly_requests": 12.5,
                    "daily_cost": 12.5,
                    "monthly_cost": 28.6
                }
            }
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve quota information"
        )


@router.get("/capabilities")
async def get_ai_capabilities():
    """Get available AI capabilities"""
    return {
        "success": True,
        "data": {
            "capabilities": [
                {
                    "name": "question_generation",
                    "description": "Generate educational questions using AI",
                    "enabled": True,
                    "supported_types": ["multiple_choice", "true_false", "essay", "fill_in_blank"],
                    "supported_languages": ["en", "es", "fr", "de", "pt"]
                },
                {
                    "name": "content_scoring",
                    "description": "Automatically score student responses",
                    "enabled": True,
                    "supported_types": ["essay", "short_answer", "writing"],
                    "rubric_types": ["holistic", "analytic"]
                },
                {
                    "name": "content_moderation",
                    "description": "Moderate content for inappropriate material",
                    "enabled": True,
                    "categories": ["hate", "harassment", "self-harm", "sexual", "violence"]
                },
                {
                    "name": "speech_synthesis",
                    "description": "Convert text to speech",
                    "enabled": True,
                    "supported_voices": ["en-US-AriaNeural", "en-US-JennyNeural"],
                    "supported_formats": ["mp3", "wav", "ogg"]
                },
                {
                    "name": "speech_recognition",
                    "description": "Convert speech to text",
                    "enabled": True,
                    "supported_languages": ["en-US", "es-ES", "fr-FR"],
                    "supported_formats": ["mp3", "wav", "m4a"]
                },
                {
                    "name": "plagiarism_detection",
                    "description": "Check content for plagiarism",
                    "enabled": False,
                    "reason": "Requires Turnitin API configuration"
                }
            ],
            "models": {
                "question_generation": "gpt-4-turbo-preview",
                "content_scoring": "gpt-4-turbo-preview",
                "content_moderation": "text-moderation-latest"
            },
            "limits": {
                "max_questions_per_request": 10,
                "max_content_length": 10000,
                "max_audio_duration_seconds": 300
            }
        }
    }
