import React, { useState } from 'react';
import { 
  Users, 
  BookOpen, 
  TrendingUp, 
  Award, 
  Calendar,
  Download,
  Filter,
  RefreshCw
} from 'lucide-react';
import { Button } from '@/components/ui/Button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Badge } from '@/components/ui/Badge';
import { ProgressBar } from '@/components/ui/ProgressBar';
import toast from 'react-hot-toast';

interface AnalyticsData {
  overview: {
    totalUsers: number;
    activeUsers: number;
    totalCourses: number;
    completedCourses: number;
    totalExams: number;
    averageScore: number;
  };
  userGrowth: Array<{
    month: string;
    users: number;
    active: number;
  }>;
  courseStats: Array<{
    id: string;
    title: string;
    enrollments: number;
    completions: number;
    averageRating: number;
    completionRate: number;
  }>;
  examStats: Array<{
    id: string;
    title: string;
    attempts: number;
    averageScore: number;
    passRate: number;
  }>;
  topPerformers: Array<{
    id: string;
    name: string;
    coursesCompleted: number;
    averageScore: number;
    totalPoints: number;
  }>;
}

const mockAnalytics: AnalyticsData = {
  overview: {
    totalUsers: 1247,
    activeUsers: 892,
    totalCourses: 45,
    completedCourses: 1834,
    totalExams: 156,
    averageScore: 78.5,
  },
  userGrowth: [
    { month: 'Ene', users: 850, active: 620 },
    { month: 'Feb', users: 920, active: 680 },
    { month: 'Mar', users: 1050, active: 750 },
    { month: 'Abr', users: 1150, active: 820 },
    { month: 'May', users: 1200, active: 860 },
    { month: 'Jun', users: 1247, active: 892 },
  ],
  courseStats: [
    {
      id: '1',
      title: 'DevOps Fundamentals',
      enrollments: 245,
      completions: 189,
      averageRating: 4.8,
      completionRate: 77.1,
    },
    {
      id: '2',
      title: 'Introducción a la IA',
      enrollments: 312,
      completions: 201,
      averageRating: 4.6,
      completionRate: 64.4,
    },
    {
      id: '3',
      title: 'Python para Principiantes',
      enrollments: 189,
      completions: 156,
      averageRating: 4.7,
      completionRate: 82.5,
    },
  ],
  examStats: [
    {
      id: '1',
      title: 'DevOps Final Exam',
      attempts: 234,
      averageScore: 82.3,
      passRate: 78.6,
    },
    {
      id: '2',
      title: 'IA Midterm Assessment',
      attempts: 198,
      averageScore: 75.8,
      passRate: 71.2,
    },
    {
      id: '3',
      title: 'Python Basics Quiz',
      attempts: 167,
      averageScore: 88.1,
      passRate: 89.2,
    },
  ],
  topPerformers: [
    {
      id: '1',
      name: 'María García',
      coursesCompleted: 12,
      averageScore: 94.5,
      totalPoints: 2840,
    },
    {
      id: '2',
      name: 'Carlos López',
      coursesCompleted: 10,
      averageScore: 91.2,
      totalPoints: 2650,
    },
    {
      id: '3',
      name: 'Ana Martínez',
      coursesCompleted: 11,
      averageScore: 89.8,
      totalPoints: 2580,
    },
  ],
};

export default function SystemAnalyticsPage() {
  const [analytics] = useState<AnalyticsData>(mockAnalytics);
  const [timeRange, setTimeRange] = useState('6m');
  const [isRefreshing, setIsRefreshing] = useState(false);

  const handleRefresh = async () => {
    setIsRefreshing(true);
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1500));
    setIsRefreshing(false);
    toast.success('Datos actualizados');
  };

  const handleExport = () => {
    toast.success('Exportando reporte analítico...');
  };

  const getGrowthPercentage = (current: number, previous: number) => {
    return ((current - previous) / previous * 100).toFixed(1);
  };

  const currentMonth = analytics.userGrowth[analytics.userGrowth.length - 1];
  const previousMonth = analytics.userGrowth[analytics.userGrowth.length - 2];

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Analíticas del Sistema</h1>
            <p className="text-gray-600 mt-1">
              Métricas y estadísticas de la plataforma
            </p>
          </div>
          <div className="flex items-center space-x-3">
            <select
              value={timeRange}
              onChange={(e) => setTimeRange(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="1m">Último mes</option>
              <option value="3m">Últimos 3 meses</option>
              <option value="6m">Últimos 6 meses</option>
              <option value="1y">Último año</option>
            </select>
            <Button
              variant="outline"
              onClick={handleRefresh}
              disabled={isRefreshing}
            >
              <RefreshCw className={`w-4 h-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
              Actualizar
            </Button>
            <Button onClick={handleExport}>
              <Download className="w-4 h-4 mr-2" />
              Exportar
            </Button>
          </div>
        </div>
      </div>

      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0 p-3 rounded-lg bg-blue-100">
                <Users className="h-6 w-6 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Usuarios Totales</p>
                <p className="text-2xl font-semibold text-gray-900">{analytics.overview.totalUsers.toLocaleString()}</p>
                <p className="text-sm text-green-600">
                  +{getGrowthPercentage(currentMonth.users, previousMonth.users)}% este mes
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0 p-3 rounded-lg bg-green-100">
                <TrendingUp className="h-6 w-6 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Usuarios Activos</p>
                <p className="text-2xl font-semibold text-gray-900">{analytics.overview.activeUsers.toLocaleString()}</p>
                <p className="text-sm text-green-600">
                  {((analytics.overview.activeUsers / analytics.overview.totalUsers) * 100).toFixed(1)}% del total
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0 p-3 rounded-lg bg-purple-100">
                <BookOpen className="h-6 w-6 text-purple-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Cursos Completados</p>
                <p className="text-2xl font-semibold text-gray-900">{analytics.overview.completedCourses.toLocaleString()}</p>
                <p className="text-sm text-gray-600">
                  {analytics.overview.totalCourses} cursos disponibles
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0 p-3 rounded-lg bg-yellow-100">
                <Award className="h-6 w-6 text-yellow-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Puntuación Promedio</p>
                <p className="text-2xl font-semibold text-gray-900">{analytics.overview.averageScore}%</p>
                <p className="text-sm text-gray-600">
                  {analytics.overview.totalExams} exámenes realizados
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        {/* User Growth Chart */}
        <Card>
          <CardHeader>
            <CardTitle>Crecimiento de Usuarios</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {analytics.userGrowth.map((data, index) => (
                <div key={data.month} className="flex items-center justify-between">
                  <span className="text-sm font-medium text-gray-700">{data.month}</span>
                  <div className="flex items-center space-x-4">
                    <div className="text-right">
                      <div className="text-sm font-medium text-gray-900">{data.users}</div>
                      <div className="text-xs text-gray-500">Total</div>
                    </div>
                    <div className="text-right">
                      <div className="text-sm font-medium text-green-600">{data.active}</div>
                      <div className="text-xs text-gray-500">Activos</div>
                    </div>
                    <div className="w-32">
                      <ProgressBar 
                        value={data.active} 
                        max={data.users} 
                        variant="success"
                        size="sm"
                      />
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Top Performers */}
        <Card>
          <CardHeader>
            <CardTitle>Mejores Estudiantes</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {analytics.topPerformers.map((performer, index) => (
                <div key={performer.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center space-x-3">
                    <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold ${
                      index === 0 ? 'bg-yellow-500 text-white' :
                      index === 1 ? 'bg-gray-400 text-white' :
                      index === 2 ? 'bg-orange-500 text-white' :
                      'bg-gray-200 text-gray-700'
                    }`}>
                      {index + 1}
                    </div>
                    <div>
                      <div className="font-medium text-gray-900">{performer.name}</div>
                      <div className="text-sm text-gray-600">
                        {performer.coursesCompleted} cursos • {performer.averageScore}% promedio
                      </div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="font-medium text-gray-900">{performer.totalPoints}</div>
                    <div className="text-xs text-gray-500">puntos</div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Course Performance */}
        <Card>
          <CardHeader>
            <CardTitle>Rendimiento de Cursos</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {analytics.courseStats.map((course) => (
                <div key={course.id} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="font-medium text-gray-900">{course.title}</h4>
                    <div className="flex items-center space-x-1">
                      <span className="text-yellow-500">★</span>
                      <span className="text-sm font-medium">{course.averageRating}</span>
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-3 gap-4 text-sm">
                    <div>
                      <div className="text-gray-500">Inscripciones</div>
                      <div className="font-medium">{course.enrollments}</div>
                    </div>
                    <div>
                      <div className="text-gray-500">Completados</div>
                      <div className="font-medium">{course.completions}</div>
                    </div>
                    <div>
                      <div className="text-gray-500">Tasa de Finalización</div>
                      <div className="font-medium">{course.completionRate}%</div>
                    </div>
                  </div>
                  
                  <div className="mt-3">
                    <ProgressBar 
                      value={course.completionRate} 
                      max={100} 
                      variant={course.completionRate > 75 ? 'success' : course.completionRate > 50 ? 'warning' : 'danger'}
                      size="sm"
                    />
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Exam Performance */}
        <Card>
          <CardHeader>
            <CardTitle>Rendimiento de Exámenes</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {analytics.examStats.map((exam) => (
                <div key={exam.id} className="border border-gray-200 rounded-lg p-4">
                  <h4 className="font-medium text-gray-900 mb-3">{exam.title}</h4>
                  
                  <div className="grid grid-cols-3 gap-4 text-sm mb-3">
                    <div>
                      <div className="text-gray-500">Intentos</div>
                      <div className="font-medium">{exam.attempts}</div>
                    </div>
                    <div>
                      <div className="text-gray-500">Puntuación Promedio</div>
                      <div className="font-medium">{exam.averageScore}%</div>
                    </div>
                    <div>
                      <div className="text-gray-500">Tasa de Aprobación</div>
                      <div className="font-medium">{exam.passRate}%</div>
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <div className="flex justify-between text-xs">
                      <span>Puntuación Promedio</span>
                      <span>{exam.averageScore}%</span>
                    </div>
                    <ProgressBar 
                      value={exam.averageScore} 
                      max={100} 
                      variant={exam.averageScore > 80 ? 'success' : exam.averageScore > 60 ? 'warning' : 'danger'}
                      size="sm"
                    />
                    
                    <div className="flex justify-between text-xs">
                      <span>Tasa de Aprobación</span>
                      <span>{exam.passRate}%</span>
                    </div>
                    <ProgressBar 
                      value={exam.passRate} 
                      max={100} 
                      variant={exam.passRate > 75 ? 'success' : exam.passRate > 50 ? 'warning' : 'danger'}
                      size="sm"
                    />
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
