"""
Group management service
"""

import secrets
from datetime import datetime, timed<PERSON>ta
from typing import Optional, List
from uuid import UUID

from sqlmodel import Session, select, func

from ..models.group import (
    Group, GroupCreate, GroupUpdate, GroupResponse,
    GroupMember, GroupMemberCreate, GroupMemberUpdate, GroupMemberResponse,
    GroupInvitation, GroupInvitationCreate, GroupInvitationResponse,
    GroupActivity, GroupActivityCreate, GroupActivityResponse,
    GroupStatistics, GroupSearch, GroupType, MemberRole, InvitationStatus
)
from ..models.user import User
from ..services.notification_service import NotificationService


class GroupService:
    """Group management service"""
    
    def __init__(self, db: Session):
        self.db = db
        self.notification_service = NotificationService(db)
    
    async def create_group(self, group_data: GroupCreate, created_by: UUID, tenant_id: UUID) -> Group:
        """Create a new group"""
        group = Group(
            tenant_id=tenant_id,
            created_by=created_by,
            name=group_data.name,
            description=group_data.description,
            category=group_data.category,
            group_type=group_data.group_type,
            max_members=group_data.max_members,
            is_active=group_data.is_active,
            tags=group_data.tags or []
        )
        
        self.db.add(group)
        self.db.commit()
        self.db.refresh(group)
        
        # Add creator as leader
        leader_membership = GroupMember(
            group_id=group.group_id,
            user_id=created_by,
            tenant_id=tenant_id,
            role=MemberRole.LEADER
        )
        
        self.db.add(leader_membership)
        self.db.commit()
        
        # Log activity
        await self.log_activity(
            group.group_id,
            created_by,
            tenant_id,
            "group_created",
            f"Group '{group.name}' was created"
        )
        
        return group
    
    async def get_group(self, group_id: UUID, tenant_id: UUID, user_id: UUID = None) -> Optional[GroupResponse]:
        """Get group by ID with additional data"""
        group = self.db.exec(
            select(Group).where(
                Group.group_id == group_id,
                Group.tenant_id == tenant_id,
                Group.is_active == True
            )
        ).first()
        
        if not group:
            return None
        
        # Get member count
        member_count = self.db.exec(
            select(func.count(GroupMember.user_id)).where(
                GroupMember.group_id == group_id,
                GroupMember.is_active == True
            )
        ).first() or 0
        
        # Get leader name
        leader = self.db.exec(
            select(User).join(GroupMember).where(
                GroupMember.group_id == group_id,
                GroupMember.role == MemberRole.LEADER,
                GroupMember.is_active == True
            )
        ).first()
        
        leader_name = f"{leader.first_name} {leader.last_name}" if leader else None
        
        # Check if current user is member/leader
        is_member = False
        is_leader = False
        
        if user_id:
            membership = self.db.exec(
                select(GroupMember).where(
                    GroupMember.group_id == group_id,
                    GroupMember.user_id == user_id,
                    GroupMember.is_active == True
                )
            ).first()
            
            if membership:
                is_member = True
                is_leader = membership.role == MemberRole.LEADER
        
        return GroupResponse(
            group_id=group.group_id,
            created_by=group.created_by,
            name=group.name,
            description=group.description,
            category=group.category,
            group_type=group.group_type,
            max_members=group.max_members,
            is_active=group.is_active,
            avatar_url=group.avatar_url,
            tags=group.tags,
            created_at=group.created_at,
            updated_at=group.updated_at,
            member_count=member_count,
            leader_name=leader_name,
            is_member=is_member,
            is_leader=is_leader
        )
    
    async def update_group(self, group_id: UUID, group_data: GroupUpdate, tenant_id: UUID) -> Optional[Group]:
        """Update group"""
        group = self.db.exec(
            select(Group).where(
                Group.group_id == group_id,
                Group.tenant_id == tenant_id
            )
        ).first()
        
        if not group:
            return None
        
        # Update fields
        update_data = group_data.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(group, field, value)
        
        group.updated_at = datetime.utcnow()
        
        self.db.add(group)
        self.db.commit()
        self.db.refresh(group)
        
        return group
    
    async def list_groups(
        self,
        tenant_id: UUID,
        skip: int = 0,
        limit: int = 20,
        search: GroupSearch = None,
        user_id: UUID = None
    ) -> List[GroupResponse]:
        """List groups with filtering"""
        stmt = select(Group).where(
            Group.tenant_id == tenant_id,
            Group.is_active == True
        )
        
        # Apply search filters
        if search:
            if search.query:
                search_term = f"%{search.query}%"
                stmt = stmt.where(
                    (Group.name.ilike(search_term)) |
                    (Group.description.ilike(search_term))
                )
            
            if search.category:
                stmt = stmt.where(Group.category == search.category)
            
            if search.group_type:
                stmt = stmt.where(Group.group_type == search.group_type)
            
            if search.tags:
                for tag in search.tags:
                    stmt = stmt.where(Group.tags.contains([tag]))
            
            if search.min_members:
                # This would require a subquery to count members
                pass
            
            if search.max_members:
                stmt = stmt.where(Group.max_members <= search.max_members)
        
        # For public groups or user's groups
        if user_id:
            # Show public groups and groups user is member of
            user_groups_stmt = select(GroupMember.group_id).where(
                GroupMember.user_id == user_id,
                GroupMember.is_active == True
            )
            user_group_ids = self.db.exec(user_groups_stmt).all()
            
            stmt = stmt.where(
                (Group.group_type == GroupType.PUBLIC) |
                (Group.group_id.in_(user_group_ids))
            )
        else:
            # Only show public groups
            stmt = stmt.where(Group.group_type == GroupType.PUBLIC)
        
        stmt = stmt.offset(skip).limit(limit).order_by(Group.created_at.desc())
        groups = self.db.exec(stmt).all()
        
        # Convert to response models
        group_responses = []
        for group in groups:
            group_response = await self.get_group(group.group_id, tenant_id, user_id)
            if group_response:
                group_responses.append(group_response)
        
        return group_responses
    
    async def join_group(self, group_id: UUID, user_id: UUID, tenant_id: UUID) -> bool:
        """Join a public group"""
        group = self.db.get(Group, group_id)
        if not group or not group.is_active:
            return False
        
        if group.group_type != GroupType.PUBLIC:
            raise ValueError("Cannot join private group without invitation")
        
        # Check if already a member
        existing_membership = self.db.exec(
            select(GroupMember).where(
                GroupMember.group_id == group_id,
                GroupMember.user_id == user_id
            )
        ).first()
        
        if existing_membership:
            if existing_membership.is_active:
                return False  # Already a member
            else:
                # Reactivate membership
                existing_membership.is_active = True
                existing_membership.joined_at = datetime.utcnow()
                self.db.add(existing_membership)
        else:
            # Check member limit
            if group.max_members:
                current_members = self.db.exec(
                    select(func.count(GroupMember.user_id)).where(
                        GroupMember.group_id == group_id,
                        GroupMember.is_active == True
                    )
                ).first() or 0
                
                if current_members >= group.max_members:
                    raise ValueError("Group is full")
            
            # Create new membership
            membership = GroupMember(
                group_id=group_id,
                user_id=user_id,
                tenant_id=tenant_id,
                role=MemberRole.MEMBER
            )
            
            self.db.add(membership)
        
        self.db.commit()
        
        # Log activity
        user = self.db.get(User, user_id)
        user_name = f"{user.first_name} {user.last_name}" if user else "Unknown"
        
        await self.log_activity(
            group_id,
            user_id,
            tenant_id,
            "member_joined",
            f"{user_name} joined the group"
        )
        
        return True
    
    async def leave_group(self, group_id: UUID, user_id: UUID, tenant_id: UUID) -> bool:
        """Leave a group"""
        membership = self.db.exec(
            select(GroupMember).where(
                GroupMember.group_id == group_id,
                GroupMember.user_id == user_id,
                GroupMember.is_active == True
            )
        ).first()
        
        if not membership:
            return False
        
        # Check if user is the only leader
        if membership.role == MemberRole.LEADER:
            leader_count = self.db.exec(
                select(func.count(GroupMember.user_id)).where(
                    GroupMember.group_id == group_id,
                    GroupMember.role == MemberRole.LEADER,
                    GroupMember.is_active == True
                )
            ).first() or 0
            
            if leader_count <= 1:
                raise ValueError("Cannot leave group as the only leader. Transfer leadership first.")
        
        # Deactivate membership
        membership.is_active = False
        membership.left_at = datetime.utcnow()
        
        self.db.add(membership)
        self.db.commit()
        
        # Log activity
        user = self.db.get(User, user_id)
        user_name = f"{user.first_name} {user.last_name}" if user else "Unknown"
        
        await self.log_activity(
            group_id,
            user_id,
            tenant_id,
            "member_left",
            f"{user_name} left the group"
        )
        
        return True
    
    async def invite_users(self, group_id: UUID, invitation_data: GroupInvitationCreate, invited_by: UUID, tenant_id: UUID) -> List[GroupInvitation]:
        """Invite users to group"""
        group = self.db.get(Group, group_id)
        if not group:
            raise ValueError("Group not found")
        
        invitations = []
        
        for email in invitation_data.emails:
            # Check if user exists
            user = self.db.exec(
                select(User).where(
                    User.email == email,
                    User.tenant_id == tenant_id
                )
            ).first()
            
            # Generate invitation token
            token = secrets.token_urlsafe(32)
            
            invitation = GroupInvitation(
                group_id=group_id,
                invited_by=invited_by,
                tenant_id=tenant_id,
                email=email,
                user_id=user.user_id if user else None,
                message=invitation_data.message,
                token=token,
                expires_at=datetime.utcnow() + timedelta(days=7)
            )
            
            self.db.add(invitation)
            invitations.append(invitation)
        
        self.db.commit()
        
        # Send invitation emails
        for invitation in invitations:
            await self._send_invitation_email(invitation, group)
        
        return invitations
    
    async def accept_invitation(self, token: str, user_id: UUID = None) -> bool:
        """Accept group invitation"""
        invitation = self.db.exec(
            select(GroupInvitation).where(
                GroupInvitation.token == token,
                GroupInvitation.status == InvitationStatus.PENDING,
                GroupInvitation.expires_at > datetime.utcnow()
            )
        ).first()
        
        if not invitation:
            raise ValueError("Invalid or expired invitation")
        
        # If user_id not provided, try to find user by email
        if not user_id and invitation.user_id:
            user_id = invitation.user_id
        
        if not user_id:
            raise ValueError("User not found")
        
        # Check if already a member
        existing_membership = self.db.exec(
            select(GroupMember).where(
                GroupMember.group_id == invitation.group_id,
                GroupMember.user_id == user_id,
                GroupMember.is_active == True
            )
        ).first()
        
        if existing_membership:
            raise ValueError("User is already a member of this group")
        
        # Create membership
        membership = GroupMember(
            group_id=invitation.group_id,
            user_id=user_id,
            tenant_id=invitation.tenant_id,
            role=MemberRole.MEMBER,
            invited_by=invitation.invited_by
        )
        
        self.db.add(membership)
        
        # Update invitation status
        invitation.status = InvitationStatus.ACCEPTED
        invitation.responded_at = datetime.utcnow()
        
        self.db.add(invitation)
        self.db.commit()
        
        # Log activity
        user = self.db.get(User, user_id)
        user_name = f"{user.first_name} {user.last_name}" if user else "Unknown"
        
        await self.log_activity(
            invitation.group_id,
            user_id,
            invitation.tenant_id,
            "member_joined",
            f"{user_name} joined the group via invitation"
        )
        
        return True
    
    async def get_group_members(self, group_id: UUID, tenant_id: UUID) -> List[GroupMemberResponse]:
        """Get all members of a group"""
        stmt = select(GroupMember, User).join(User).where(
            GroupMember.group_id == group_id,
            GroupMember.tenant_id == tenant_id,
            GroupMember.is_active == True
        ).order_by(
            GroupMember.role.desc(),  # Leaders first
            GroupMember.joined_at
        )
        
        results = self.db.exec(stmt).all()
        
        member_responses = []
        for membership, user in results:
            # Get inviter name
            inviter_name = None
            if membership.invited_by:
                inviter = self.db.get(User, membership.invited_by)
                inviter_name = f"{inviter.first_name} {inviter.last_name}" if inviter else None
            
            member_response = GroupMemberResponse(
                group_id=membership.group_id,
                user_id=membership.user_id,
                role=membership.role,
                joined_at=membership.joined_at,
                is_active=membership.is_active,
                invited_by=membership.invited_by,
                left_at=membership.left_at,
                user_name=f"{user.first_name} {user.last_name}",
                user_email=user.email,
                user_avatar=user.avatar_url,
                inviter_name=inviter_name
            )
            member_responses.append(member_response)
        
        return member_responses
    
    async def log_activity(self, group_id: UUID, user_id: UUID, tenant_id: UUID, activity_type: str, description: str, activity_data: dict = None):
        """Log group activity"""
        activity = GroupActivity(
            group_id=group_id,
            user_id=user_id,
            tenant_id=tenant_id,
            activity_type=activity_type,
            description=description,
            activity_data=activity_data or {}
        )
        
        self.db.add(activity)
        self.db.commit()
    
    async def _send_invitation_email(self, invitation: GroupInvitation, group: Group):
        """Send invitation email"""
        # This would use the notification service to send email
        # For now, just log it
        print(f"Invitation email would be sent to {invitation.email} for group {group.name}")
        pass
