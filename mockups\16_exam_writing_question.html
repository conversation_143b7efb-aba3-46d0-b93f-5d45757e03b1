<!DOCTYPE html>
<html lang="es" class="h-full bg-gray-50">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pregunta Writing - Arroyo University</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Inter', sans-serif; }
    </style>
</head>
<body class="h-full">
    <div class="min-h-full">
        <!-- Header with Timer -->
        <header class="bg-white shadow">
            <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between items-center py-4">
                    <div class="flex items-center">
                        <div class="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M10.394 2.08a1 1 0 00-.788 0l-7 3a1 1 0 000 1.84L5.25 8.051a.999.999 0 01.356-.257l4-1.714a1 1 0 11.788 1.84L7.667 9.088l1.94.831a1 1 0 00.787 0l7-3a1 1 0 000-1.838l-7-3z"/>
                            </svg>
                        </div>
                        <h1 class="ml-3 text-lg font-semibold text-gray-900">English B2 Assessment</h1>
                    </div>
                    
                    <div class="flex items-center space-x-6">
                        <div class="text-sm text-gray-600">
                            Pregunta <span class="font-medium">1 de 25</span>
                        </div>
                        <div class="flex items-center space-x-2 bg-green-50 px-3 py-1 rounded-full">
                            <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                            </svg>
                            <span class="text-green-600 font-medium text-sm" id="timer">89:45</span>
                        </div>
                    </div>
                </div>
            </div>
        </header>

        <!-- Progress Bar -->
        <div class="bg-white border-b">
            <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="py-2">
                    <div class="w-full bg-gray-200 rounded-full h-1">
                        <div class="bg-blue-600 h-1 rounded-full" style="width: 4%"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <main class="max-w-4xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
            <!-- Question Header -->
            <div class="mb-6">
                <div class="flex items-center space-x-3 mb-4">
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"/>
                        </svg>
                        Writing
                    </span>
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                        Intermedio
                    </span>
                </div>
                <h2 class="text-xl font-semibold text-gray-900">Pregunta 1</h2>
            </div>

            <!-- Question Content -->
            <div class="bg-white shadow rounded-lg">
                <div class="px-6 py-8">
                    <!-- Question Text -->
                    <div class="mb-8">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">
                            Describe un proyecto exitoso que hayas liderado
                        </h3>
                        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                            <p class="text-blue-900 mb-3">
                                En tu respuesta, incluye los siguientes puntos:
                            </p>
                            <ul class="list-disc list-inside text-blue-800 space-y-1">
                                <li>Objetivos del proyecto y contexto organizacional</li>
                                <li>Metodología y herramientas utilizadas</li>
                                <li>Principales desafíos enfrentados y cómo los resolviste</li>
                                <li>Resultados obtenidos y métricas de éxito</li>
                                <li>Lecciones aprendidas y aplicaciones futuras</li>
                            </ul>
                            <div class="mt-4 p-3 bg-blue-100 rounded-md">
                                <p class="text-blue-900 text-sm">
                                    <strong>Requisitos:</strong> Escribe entre 150-200 palabras. Usa vocabulario técnico apropiado y estructura tu respuesta de manera clara y profesional.
                                </p>
                            </div>
                        </div>
                    </div>

                    <!-- Writing Area -->
                    <div class="mb-6">
                        <label for="answer" class="block text-sm font-medium text-gray-700 mb-2">
                            Tu respuesta:
                        </label>
                        <textarea id="answer" rows="12" 
                                  placeholder="Escribe tu respuesta aquí..."
                                  class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none"
                                  oninput="updateWordCount()"></textarea>
                        
                        <!-- Word Count and Tools -->
                        <div class="flex items-center justify-between mt-3">
                            <div class="flex items-center space-x-4 text-sm text-gray-600">
                                <span>Palabras: <span id="wordCount" class="font-medium">0</span>/200</span>
                                <span>Caracteres: <span id="charCount" class="font-medium">0</span></span>
                            </div>
                            
                            <div class="flex items-center space-x-2">
                                <button onclick="clearText()" class="text-sm text-gray-500 hover:text-gray-700">
                                    Limpiar
                                </button>
                                <button onclick="checkSpelling()" class="text-sm text-blue-600 hover:text-blue-500">
                                    Revisar ortografía
                                </button>
                            </div>
                        </div>
                        
                        <!-- Progress Indicator -->
                        <div class="mt-3">
                            <div class="flex items-center justify-between text-xs text-gray-500 mb-1">
                                <span>Progreso de palabras</span>
                                <span id="progressText">0%</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div id="progressBar" class="bg-blue-600 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
                            </div>
                        </div>
                    </div>

                    <!-- Writing Tips -->
                    <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
                        <h4 class="text-sm font-medium text-yellow-800 mb-2">💡 Consejos para una buena respuesta:</h4>
                        <ul class="text-sm text-yellow-700 space-y-1">
                            <li>• Organiza tus ideas en párrafos claros</li>
                            <li>• Usa conectores para unir tus ideas (además, por otro lado, en conclusión...)</li>
                            <li>• Incluye ejemplos específicos para apoyar tus puntos</li>
                            <li>• Revisa la gramática y ortografía antes de continuar</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Auto-save Indicator -->
            <div class="mt-4 flex items-center justify-center">
                <div class="flex items-center text-sm text-gray-500">
                    <svg class="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                    </svg>
                    <span id="saveStatus">Guardado automáticamente</span>
                </div>
            </div>

            <!-- Navigation -->
            <div class="mt-8 flex justify-between">
                <button onclick="window.location.href='15_exam_landing.html'" 
                        class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
                    </svg>
                    Volver al inicio
                </button>
                
                <button onclick="nextQuestion()" 
                        class="inline-flex items-center px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    Siguiente
                    <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                    </svg>
                </button>
            </div>
        </main>
    </div>

    <script>
        let timeLeft = 5385; // 89:45 in seconds
        let autoSaveInterval;

        // Timer countdown
        function updateTimer() {
            const minutes = Math.floor(timeLeft / 60);
            const seconds = timeLeft % 60;
            document.getElementById('timer').textContent = 
                `${minutes}:${seconds.toString().padStart(2, '0')}`;
            
            if (timeLeft <= 0) {
                alert('¡Tiempo agotado! El examen se enviará automáticamente.');
                submitExam();
            }
            timeLeft--;
        }

        // Start timer
        setInterval(updateTimer, 1000);

        // Word count and progress
        function updateWordCount() {
            const text = document.getElementById('answer').value;
            const words = text.trim() ? text.trim().split(/\s+/).length : 0;
            const chars = text.length;
            
            document.getElementById('wordCount').textContent = words;
            document.getElementById('charCount').textContent = chars;
            
            // Update progress bar
            const progress = Math.min((words / 200) * 100, 100);
            document.getElementById('progressBar').style.width = progress + '%';
            document.getElementById('progressText').textContent = Math.round(progress) + '%';
            
            // Change color based on word count
            const progressBar = document.getElementById('progressBar');
            if (words < 150) {
                progressBar.className = 'bg-yellow-500 h-2 rounded-full transition-all duration-300';
            } else if (words <= 200) {
                progressBar.className = 'bg-green-500 h-2 rounded-full transition-all duration-300';
            } else {
                progressBar.className = 'bg-red-500 h-2 rounded-full transition-all duration-300';
            }
            
            // Auto-save
            clearTimeout(autoSaveInterval);
            autoSaveInterval = setTimeout(autoSave, 2000);
        }

        function autoSave() {
            document.getElementById('saveStatus').textContent = 'Guardando...';
            
            // Simulate save
            setTimeout(() => {
                document.getElementById('saveStatus').textContent = 'Guardado automáticamente';
            }, 500);
        }

        function clearText() {
            if (confirm('¿Estás seguro de que quieres borrar todo el texto?')) {
                document.getElementById('answer').value = '';
                updateWordCount();
            }
        }

        function checkSpelling() {
            alert('Función de revisión ortográfica activada. En una implementación real, esto resaltaría errores ortográficos.');
        }

        function nextQuestion() {
            const words = document.getElementById('answer').value.trim() ? 
                         document.getElementById('answer').value.trim().split(/\s+/).length : 0;
            
            if (words < 100) {
                if (!confirm('Tu respuesta tiene menos de 100 palabras. ¿Estás seguro de que quieres continuar?')) {
                    return;
                }
            }
            
            // Navigate to next question (listening)
            window.location.href = '17_exam_listening_question.html';
        }

        function submitExam() {
            // In real implementation, this would submit the exam
            window.location.href = '21_results_dashboard.html';
        }
    </script>
</body>
</html>
