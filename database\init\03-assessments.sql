-- Assessment and Examination System Tables
-- This script creates tables for exams, questions, and submissions

-- Create question_banks table
CREATE TABLE question_banks (
    bank_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(tenant_id) ON DELETE CASCADE,
    name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    description TEXT,
    subject_area VARCHAR(255),
    difficulty_level VARCHAR(50), -- 'beginner', 'intermediate', 'advanced'
    created_by UUID NOT NULL REFERENCES users(user_id),
    is_public BOOLEAN DEFAULT FALSE,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create questions table
CREATE TABLE questions (
    question_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    bank_id UUID NOT NULL REFERENCES question_banks(bank_id) ON DELETE CASCADE,
    question_type VARCHAR(50) NOT NULL, -- 'multiple_choice', 'true_false', 'short_answer', 'essay', 'code'
    question_text TEXT NOT NULL,
    question_data JSONB NOT NULL, -- stores options, correct answers, etc.
    points DECIMAL(5,2) DEFAULT 1.00,
    difficulty_level VARCHAR(50), -- 'beginner', 'intermediate', 'advanced'
    tags TEXT[], -- array of tags for categorization
    explanation TEXT, -- explanation for the correct answer
    metadata JSONB DEFAULT '{}',
    created_by UUID NOT NULL REFERENCES users(user_id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create exams table
CREATE TABLE exams (
    exam_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    course_id UUID REFERENCES courses(course_id) ON DELETE CASCADE,
    module_id UUID REFERENCES course_modules(module_id) ON DELETE CASCADE,
    title VARCHAR(500) NOT NULL,
    description TEXT,
    instructions TEXT,
    status exam_status DEFAULT 'draft',
    exam_type VARCHAR(50) NOT NULL, -- 'quiz', 'midterm', 'final', 'practice'
    total_points DECIMAL(8,2) DEFAULT 0.00,
    passing_score DECIMAL(5,2) DEFAULT 70.00, -- percentage
    time_limit INTEGER, -- in minutes
    attempts_allowed INTEGER DEFAULT 1,
    randomize_questions BOOLEAN DEFAULT FALSE,
    randomize_options BOOLEAN DEFAULT FALSE,
    show_results_immediately BOOLEAN DEFAULT TRUE,
    show_correct_answers BOOLEAN DEFAULT TRUE,
    available_from TIMESTAMP WITH TIME ZONE,
    available_until TIMESTAMP WITH TIME ZONE,
    metadata JSONB DEFAULT '{}',
    created_by UUID NOT NULL REFERENCES users(user_id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create exam_questions table (junction table with additional properties)
CREATE TABLE exam_questions (
    exam_id UUID REFERENCES exams(exam_id) ON DELETE CASCADE,
    question_id UUID REFERENCES questions(question_id) ON DELETE CASCADE,
    order_index INTEGER NOT NULL,
    points_override DECIMAL(5,2), -- override question's default points
    is_required BOOLEAN DEFAULT TRUE,
    added_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (exam_id, question_id),
    UNIQUE(exam_id, order_index)
);

-- Create exam_submissions table
CREATE TABLE exam_submissions (
    submission_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    exam_id UUID NOT NULL REFERENCES exams(exam_id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
    attempt_number INTEGER NOT NULL DEFAULT 1,
    status submission_status DEFAULT 'pending',
    started_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    submitted_at TIMESTAMP WITH TIME ZONE,
    graded_at TIMESTAMP WITH TIME ZONE,
    graded_by UUID REFERENCES users(user_id),
    total_score DECIMAL(8,2),
    percentage_score DECIMAL(5,2),
    passed BOOLEAN,
    time_spent INTEGER, -- in seconds
    metadata JSONB DEFAULT '{}',
    UNIQUE(exam_id, user_id, attempt_number)
);

-- Create submission_answers table
CREATE TABLE submission_answers (
    answer_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    submission_id UUID NOT NULL REFERENCES exam_submissions(submission_id) ON DELETE CASCADE,
    question_id UUID NOT NULL REFERENCES questions(question_id) ON DELETE CASCADE,
    answer_data JSONB NOT NULL, -- stores the user's answer
    is_correct BOOLEAN,
    points_earned DECIMAL(5,2) DEFAULT 0.00,
    feedback TEXT, -- grader's feedback for this answer
    auto_graded BOOLEAN DEFAULT FALSE,
    graded_at TIMESTAMP WITH TIME ZONE,
    graded_by UUID REFERENCES users(user_id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(submission_id, question_id)
);

-- Create ai_question_generations table
CREATE TABLE ai_question_generations (
    generation_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    bank_id UUID NOT NULL REFERENCES question_banks(bank_id) ON DELETE CASCADE,
    prompt_text TEXT NOT NULL,
    generated_questions JSONB NOT NULL, -- array of generated questions
    generation_model VARCHAR(100), -- 'gpt-4', 'claude-3', etc.
    generation_parameters JSONB DEFAULT '{}',
    quality_score DECIMAL(3,2), -- AI confidence score
    human_reviewed BOOLEAN DEFAULT FALSE,
    reviewed_by UUID REFERENCES users(user_id),
    review_notes TEXT,
    created_by UUID NOT NULL REFERENCES users(user_id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create question_analytics table
CREATE TABLE question_analytics (
    analytics_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    question_id UUID NOT NULL REFERENCES questions(question_id) ON DELETE CASCADE,
    total_attempts INTEGER DEFAULT 0,
    correct_attempts INTEGER DEFAULT 0,
    average_time_seconds DECIMAL(8,2) DEFAULT 0.00,
    difficulty_index DECIMAL(3,2), -- calculated difficulty based on performance
    discrimination_index DECIMAL(3,2), -- how well question discriminates between high/low performers
    last_calculated TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    metadata JSONB DEFAULT '{}',
    UNIQUE(question_id)
);

-- Create exam_analytics table
CREATE TABLE exam_analytics (
    analytics_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    exam_id UUID NOT NULL REFERENCES exams(exam_id) ON DELETE CASCADE,
    total_submissions INTEGER DEFAULT 0,
    average_score DECIMAL(5,2) DEFAULT 0.00,
    pass_rate DECIMAL(5,2) DEFAULT 0.00,
    average_time_minutes DECIMAL(8,2) DEFAULT 0.00,
    completion_rate DECIMAL(5,2) DEFAULT 0.00,
    last_calculated TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    metadata JSONB DEFAULT '{}',
    UNIQUE(exam_id)
);

-- Add indexes for performance
CREATE INDEX idx_question_banks_tenant_id ON question_banks(tenant_id);
CREATE INDEX idx_question_banks_created_by ON question_banks(created_by);
CREATE INDEX idx_questions_bank_id ON questions(bank_id);
CREATE INDEX idx_questions_created_by ON questions(created_by);
CREATE INDEX idx_questions_difficulty_level ON questions(difficulty_level);
CREATE INDEX idx_questions_tags ON questions USING GIN(tags);
CREATE INDEX idx_exams_course_id ON exams(course_id);
CREATE INDEX idx_exams_module_id ON exams(module_id);
CREATE INDEX idx_exams_created_by ON exams(created_by);
CREATE INDEX idx_exams_status ON exams(status);
CREATE INDEX idx_exam_submissions_exam_id ON exam_submissions(exam_id);
CREATE INDEX idx_exam_submissions_user_id ON exam_submissions(user_id);
CREATE INDEX idx_exam_submissions_status ON exam_submissions(status);
CREATE INDEX idx_submission_answers_submission_id ON submission_answers(submission_id);
CREATE INDEX idx_submission_answers_question_id ON submission_answers(question_id);
CREATE INDEX idx_ai_question_generations_bank_id ON ai_question_generations(bank_id);
CREATE INDEX idx_question_analytics_question_id ON question_analytics(question_id);
CREATE INDEX idx_exam_analytics_exam_id ON exam_analytics(exam_id);

-- Add updated_at triggers
CREATE TRIGGER update_question_banks_updated_at BEFORE UPDATE ON question_banks
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_questions_updated_at BEFORE UPDATE ON questions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_exams_updated_at BEFORE UPDATE ON exams
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_ai_question_generations_updated_at BEFORE UPDATE ON ai_question_generations
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Enable RLS for assessment tables
ALTER TABLE question_banks ENABLE ROW LEVEL SECURITY;
ALTER TABLE questions ENABLE ROW LEVEL SECURITY;
ALTER TABLE exams ENABLE ROW LEVEL SECURITY;
ALTER TABLE exam_submissions ENABLE ROW LEVEL SECURITY;
ALTER TABLE submission_answers ENABLE ROW LEVEL SECURITY;
ALTER TABLE ai_question_generations ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for assessment tables
CREATE POLICY tenant_isolation_question_banks ON question_banks
    FOR ALL TO PUBLIC
    USING (tenant_id = current_setting('app.current_tenant_id')::UUID);
