import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { Home, ArrowLeft } from 'lucide-react';
import { Button } from '@/components/ui/Button';

export default function NotFoundPage() {
  return (
    <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <div className="text-center">
          {/* 404 Illustration */}
          <div className="mx-auto w-32 h-32 mb-8">
            <svg
              className="w-full h-full text-gray-300"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={1}
                d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6-4h6m2 5.291A7.962 7.962 0 0112 15c-2.34 0-4.47-.881-6.08-2.33"
              />
            </svg>
          </div>

          {/* Error Message */}
          <h1 className="text-6xl font-bold text-gray-900 mb-4">404</h1>
          <h2 className="text-2xl font-semibold text-gray-700 mb-4">
            Página no encontrada
          </h2>
          <p className="text-gray-500 mb-8 max-w-md mx-auto">
            Lo sentimos, la página que estás buscando no existe o ha sido movida.
          </p>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button asChild>
              <Link to="/">
                <Home className="w-4 h-4 mr-2" />
                Ir al Inicio
              </Link>
            </Button>
            
            <Button variant="outline" onClick={() => window.history.back()}>
              <ArrowLeft className="w-4 h-4 mr-2" />
              Volver Atrás
            </Button>
          </div>

          {/* Helpful Links */}
          <div className="mt-12">
            <h3 className="text-sm font-medium text-gray-700 mb-4">
              Enlaces útiles:
            </h3>
            <div className="flex flex-wrap justify-center gap-4 text-sm">
              <Link
                to="/marketplace"
                className="text-blue-600 hover:text-blue-500 hover:underline"
              >
                Marketplace
              </Link>
              <Link
                to="/my-courses"
                className="text-blue-600 hover:text-blue-500 hover:underline"
              >
                Mis Cursos
              </Link>
              <Link
                to="/career-paths"
                className="text-blue-600 hover:text-blue-500 hover:underline"
              >
                Career Paths
              </Link>
              <Link
                to="/leaderboard"
                className="text-blue-600 hover:text-blue-500 hover:underline"
              >
                Leaderboard
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
