import React, { useState } from 'react';
import { Plus, Users, Globe, Lock, UserCheck, Search, Filter } from 'lucide-react';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Badge } from '@/components/ui/Badge';
import { <PERSON><PERSON>, <PERSON><PERSON>eader, ModalFooter } from '@/components/ui/Modal';
import toast from 'react-hot-toast';

interface Group {
  id: string;
  name: string;
  description: string;
  category: string;
  leader: {
    id: string;
    name: string;
    initials: string;
  };
  type: 'public' | 'private';
  memberCount: number;
  createdAt: string;
}

const mockGroups: Group[] = [
  {
    id: '1',
    name: 'Grupo de Práctica IA',
    description: 'Comunidad para practicar y discutir IA',
    category: 'Tecnología',
    leader: { id: '1', name: '<PERSON><PERSON><PERSON> <PERSON>', initials: 'D<PERSON>' },
    type: 'public',
    memberCount: 45,
    createdAt: '2023-10-15',
  },
  {
    id: '2',
    name: 'Comunidad Agile',
    description: 'Metodologías ágiles y Scrum',
    category: 'Metodolog<PERSON>',
    leader: { id: '2', name: 'Prof. <PERSON>', initials: 'PG' },
    type: 'public',
    memberCount: 32,
    createdAt: '2023-10-22',
  },
  {
    id: '3',
    name: 'DevOps Avanzado',
    description: 'Grupo privado para expertos',
    category: 'Tecnología',
    leader: { id: '3', name: 'Ing. López', initials: 'IL' },
    type: 'private',
    memberCount: 12,
    createdAt: '2023-11-01',
  },
];

const categories = [
  'Tecnología',
  'Idiomas',
  'Metodologías',
  'Ciencias',
  'Matemáticas',
  'Arte',
  'Negocios',
  'Otro',
];

const leaders = [
  { id: '1', name: 'Dra. Martínez' },
  { id: '2', name: 'Prof. García' },
  { id: '3', name: 'Prof. Silva' },
  { id: '4', name: 'Ing. López' },
  { id: '5', name: 'Lic. Ruiz' },
];

export default function GroupManagementPage() {
  const [groups, setGroups] = useState<Group[]>(mockGroups);
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [typeFilter, setTypeFilter] = useState<'all' | 'public' | 'private'>('all');
  const [categoryFilter, setCategoryFilter] = useState('');

  const [newGroup, setNewGroup] = useState({
    name: '',
    description: '',
    category: '',
    leaderId: '',
    type: 'public' as 'public' | 'private',
  });

  const stats = {
    total: groups.length,
    public: groups.filter(g => g.type === 'public').length,
    private: groups.filter(g => g.type === 'private').length,
    activeMembers: groups.reduce((sum, g) => sum + g.memberCount, 0),
  };

  const filteredGroups = groups.filter(group => {
    const matchesSearch = group.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         group.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesType = typeFilter === 'all' || group.type === typeFilter;
    const matchesCategory = !categoryFilter || group.category === categoryFilter;
    
    return matchesSearch && matchesType && matchesCategory;
  });

  const handleCreateGroup = () => {
    if (!newGroup.name || !newGroup.leaderId) {
      toast.error('Por favor, completa los campos obligatorios');
      return;
    }

    const leader = leaders.find(l => l.id === newGroup.leaderId);
    if (!leader) return;

    const group: Group = {
      id: Date.now().toString(),
      name: newGroup.name,
      description: newGroup.description,
      category: newGroup.category || 'Otro',
      leader: {
        id: leader.id,
        name: leader.name,
        initials: leader.name.split(' ').map(n => n[0]).join(''),
      },
      type: newGroup.type,
      memberCount: 0,
      createdAt: new Date().toISOString().split('T')[0],
    };

    setGroups([...groups, group]);
    setIsCreateModalOpen(false);
    setNewGroup({
      name: '',
      description: '',
      category: '',
      leaderId: '',
      type: 'public',
    });

    const typeText = newGroup.type === 'public' ? 'público' : 'privado';
    toast.success(
      `¡Grupo "${newGroup.name}" creado exitosamente!\n\n` +
      `Tipo: ${typeText}\n` +
      `Líder: ${leader.name}\n\n` +
      `El líder recibirá una notificación y podrá comenzar a gestionar el grupo.`
    );
  };

  const handleEditGroup = (groupId: string) => {
    toast.info(`Editando grupo: ${groupId}`);
  };

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Gestión de Grupos</h1>
            <p className="text-gray-600 mt-1">
              Crea y administra grupos de estudio y comunidades de práctica
            </p>
          </div>
          <Button onClick={() => setIsCreateModalOpen(true)}>
            <Plus className="w-4 h-4 mr-2" />
            Crear Grupo
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0 p-3 rounded-lg bg-blue-100">
                <Users className="h-6 w-6 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Total Grupos</p>
                <p className="text-2xl font-semibold text-gray-900">{stats.total}</p>
              </div>
            </div>
            <div className="mt-4 text-sm">
              <span className="text-green-600 font-medium">+3</span>
              <span className="text-gray-500"> este mes</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0 p-3 rounded-lg bg-green-100">
                <Globe className="h-6 w-6 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Grupos Públicos</p>
                <p className="text-2xl font-semibold text-gray-900">{stats.public}</p>
              </div>
            </div>
            <div className="mt-4 text-sm">
              <span className="text-green-600 font-medium">
                {((stats.public / stats.total) * 100).toFixed(1)}%
              </span>
              <span className="text-gray-500"> del total</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0 p-3 rounded-lg bg-purple-100">
                <Lock className="h-6 w-6 text-purple-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Grupos Privados</p>
                <p className="text-2xl font-semibold text-gray-900">{stats.private}</p>
              </div>
            </div>
            <div className="mt-4 text-sm">
              <span className="text-purple-600 font-medium">
                {((stats.private / stats.total) * 100).toFixed(1)}%
              </span>
              <span className="text-gray-500"> del total</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0 p-3 rounded-lg bg-yellow-100">
                <UserCheck className="h-6 w-6 text-yellow-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Miembros Activos</p>
                <p className="text-2xl font-semibold text-gray-900">{stats.activeMembers}</p>
              </div>
            </div>
            <div className="mt-4 text-sm">
              <span className="text-yellow-600 font-medium">
                {(stats.activeMembers / stats.total).toFixed(1)}
              </span>
              <span className="text-gray-500"> promedio por grupo</span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card className="mb-6">
        <CardContent className="p-6">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
            <div className="flex items-center space-x-4">
              <div className="relative">
                <Search className="absolute left-3 top-2.5 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Buscar grupos..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 w-64"
                />
              </div>
              
              <select
                value={typeFilter}
                onChange={(e) => setTypeFilter(e.target.value as any)}
                className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">Todos los tipos</option>
                <option value="public">Públicos</option>
                <option value="private">Privados</option>
              </select>

              <select
                value={categoryFilter}
                onChange={(e) => setCategoryFilter(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">Todas las categorías</option>
                {categories.map(category => (
                  <option key={category} value={category}>
                    {category}
                  </option>
                ))}
              </select>
            </div>

            <div className="text-sm text-gray-500">
              {filteredGroups.length} de {groups.length} grupos
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Groups Table */}
      <Card>
        <CardHeader>
          <CardTitle>Grupos Existentes</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Grupo
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Líder
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Tipo
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Miembros
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Creado
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Acciones
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredGroups.map((group) => (
                  <tr key={group.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center mr-3">
                          <Users className="w-5 h-5 text-purple-600" />
                        </div>
                        <div>
                          <div className="text-sm font-medium text-gray-900">{group.name}</div>
                          <div className="text-sm text-gray-500">{group.description}</div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-2">
                          <span className="text-xs font-medium text-blue-600">
                            {group.leader.initials}
                          </span>
                        </div>
                        <span className="text-sm text-gray-900">{group.leader.name}</span>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <Badge
                        variant={group.type === 'public' ? 'green' : 'purple'}
                      >
                        {group.type === 'public' ? 'Público' : 'Privado'}
                      </Badge>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {group.memberCount} miembros
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {new Date(group.createdAt).toLocaleDateString()}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleEditGroup(group.id)}
                      >
                        Editar
                      </Button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>

      {/* Create Group Modal */}
      <Modal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        title="Crear Nuevo Grupo"
        size="md"
      >
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Nombre del Grupo *
            </label>
            <Input
              placeholder="Ej: Grupo de Práctica IA"
              value={newGroup.name}
              onChange={(e) => setNewGroup({ ...newGroup, name: e.target.value })}
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Descripción
            </label>
            <textarea
              rows={3}
              placeholder="Describe el propósito y objetivos del grupo..."
              value={newGroup.description}
              onChange={(e) => setNewGroup({ ...newGroup, description: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Categoría
            </label>
            <select
              value={newGroup.category}
              onChange={(e) => setNewGroup({ ...newGroup, category: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">Seleccionar categoría</option>
              {categories.map(category => (
                <option key={category} value={category}>
                  {category}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Líder del Grupo *
            </label>
            <select
              value={newGroup.leaderId}
              onChange={(e) => setNewGroup({ ...newGroup, leaderId: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">Seleccionar líder</option>
              {leaders.map(leader => (
                <option key={leader.id} value={leader.id}>
                  {leader.name}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-3">
              Tipo de Grupo *
            </label>
            <div className="space-y-3">
              <label className="flex items-start">
                <input
                  type="radio"
                  name="groupType"
                  value="public"
                  checked={newGroup.type === 'public'}
                  onChange={(e) => setNewGroup({ ...newGroup, type: e.target.value as any })}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 mt-0.5"
                />
                <div className="ml-3">
                  <span className="text-sm font-medium text-gray-900">Público</span>
                  <p className="text-xs text-gray-500">
                    Cualquier usuario puede unirse al grupo. Visible en el marketplace.
                  </p>
                </div>
              </label>
              <label className="flex items-start">
                <input
                  type="radio"
                  name="groupType"
                  value="private"
                  checked={newGroup.type === 'private'}
                  onChange={(e) => setNewGroup({ ...newGroup, type: e.target.value as any })}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 mt-0.5"
                />
                <div className="ml-3">
                  <span className="text-sm font-medium text-gray-900">Privado</span>
                  <p className="text-xs text-gray-500">
                    Solo por invitación del líder. No visible en el marketplace.
                  </p>
                </div>
              </label>
            </div>
          </div>
        </div>

        <ModalFooter>
          <Button
            variant="outline"
            onClick={() => setIsCreateModalOpen(false)}
          >
            Cancelar
          </Button>
          <Button onClick={handleCreateGroup}>
            Crear Grupo
          </Button>
        </ModalFooter>
      </Modal>
    </div>
  );
}
