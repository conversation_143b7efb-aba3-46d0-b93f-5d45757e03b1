# Sistema de Career Paths

## Descripción General

El sistema de Career Paths permite a los usuarios descubrir, activar y crear rutas de carrera profesional estructuradas. Incluye un marketplace con paths predefinidos y un editor visual tipo UML para crear paths personalizados.

## Componentes del Sistema

### 1. Marketplace de Career Paths

#### 1.1 Funcionalidades Principales
- **Grid de Career Paths**: Vista organizada con cards informativos
- **Filtros y Búsqueda**: Por área, dificultad, duración y popularidad
- **Vista Previa**: Mini diagramas mostrando progresión A → B
- **Activación**: Usuarios pueden activar múltiples paths simultáneamente
- **Tracking**: Seguimiento automático de progreso en paths activos

#### 1.2 Información de Career Paths
```yaml
career_path_info:
  - nombre_descriptivo: "Sysadmin → DevOps Engineer"
  - descripcion_detallada: "Transición completa desde administrador..."
  - posicion_inicial: "System Administrator"
  - posicion_objetivo: "DevOps Engineer"
  - duracion_estimada: "8-12 meses"
  - nivel_dificultad: "Intermedio"
  - rating_promedio: 4.8
  - usuarios_activos: 1,234
  - skills_totales: 15
  - mini_diagrama: "Visual preview"
```

#### 1.3 Estados de Career Paths para Usuarios
- **Disponible**: Path visible en marketplace, no activado
- **Activo**: Path activado, tracking de progreso habilitado
- **En Progreso**: Usuario ha completado algunas skills
- **Pausado**: Temporalmente inactivo, progreso preservado
- **Completado**: Todas las skills del path completadas
- **Abandonado**: Usuario decidió no continuar

### 2. Editor Visual de Career Paths

#### 2.1 Interfaz del Editor
```mermaid
flowchart LR
    A[Sidebar: Skills Repository] --> B[Canvas Principal]
    B --> C[Floating Toolbar]
    B --> D[Info Panel]
    
    A --> A1[Búsqueda de Skills]
    A --> A2[Filtros por Área]
    A --> A3[Categorías Organizadas]
    
    C --> C1[Modo Conexión]
    C --> C2[Eliminar Elementos]
    C --> C3[Herramientas de Edición]
    
    D --> D1[Nombre del Path]
    D --> D2[Descripción]
    D --> D3[Contador de Skills]
```

#### 2.2 Funcionalidades del Editor
- **Drag & Drop**: Arrastrar skills desde repository al canvas
- **Posicionamiento Libre**: Grid-based positioning con snap
- **Conexiones Direccionales**: Líneas con flechas mostrando dependencias
- **Modo Conexión**: Toggle para crear/editar conexiones
- **Búsqueda y Filtros**: Repository searchable por nombre y área
- **Guardar/Cargar**: Persistencia de paths personalizados

#### 2.3 Tipos de Conexiones
```yaml
connection_types:
  - prerequisite: "Skill A es prerequisito para Skill B"
  - related: "Skills relacionadas pero no dependientes"
  - progression: "Progresión natural en la carrera"
  - alternative: "Paths alternativos para mismo objetivo"
```

### 3. Repositorio de Skills

#### 3.1 Estructura de Skills
```json
{
  "skill_id": "javascript_advanced",
  "name": "JavaScript Avanzado",
  "description": "Conceptos avanzados de JavaScript incluyendo async/await, closures, prototypes",
  "area": "programming",
  "color_class": "bg-yellow-500",
  "difficulty_level": "intermediate",
  "prerequisites": ["javascript_basics", "html_css"],
  "estimated_learning_hours": 40,
  "verification_methods": ["course_completion", "certification", "portfolio_review"]
}
```

#### 3.2 Áreas de Skills
| Área | Color | Descripción | Skills Ejemplo |
|------|-------|-------------|----------------|
| **Programming** | Amarillo/Verde | Lenguajes y frameworks | JavaScript, Python, React, Node.js |
| **DevOps** | Azul/Índigo | Automatización e infraestructura | Docker, Kubernetes, Jenkins, Terraform |
| **Data Science** | Naranja | Análisis de datos y ML | Pandas, TensorFlow, SQL, Statistics |
| **Security** | Rojo | Ciberseguridad | Penetration Testing, CISSP, Ethical Hacking |
| **Management** | Verde/Púrpura | Liderazgo y gestión | Scrum, Leadership, Agile, Project Management |
| **Design** | Rosa/Púrpura | UX/UI y diseño | Figma, UX Research, Prototyping, Design Systems |
| **Marketing** | Naranja/Rojo | Marketing digital | SEO, Analytics, Content Strategy, Social Media |

### 4. Sistema de Progreso y Verificación

#### 4.1 Métodos de Verificación de Skills
```yaml
verification_methods:
  course_completion:
    automation: "Automático"
    reliability: "Alta"
    description: "Completar curso relacionado en la plataforma"
    
  certification:
    automation: "Semi-automático"
    reliability: "Muy Alta"
    description: "Certificaciones oficiales (AWS, Google, Microsoft, etc.)"
    
  expert_review:
    automation: "Manual"
    reliability: "Alta"
    description: "Verificación por experto en el área específica"
    
  portfolio_review:
    automation: "Manual"
    reliability: "Media"
    description: "Revisión de proyectos y portfolio del usuario"
    
  manual:
    automation: "Manual"
    reliability: "Baja"
    description: "Auto-declaración del usuario (requiere validación posterior)"
```

#### 4.2 Cálculo de Progreso
```javascript
// Algoritmo de cálculo de progreso
function calculateProgress(userSkills, pathSkills) {
  const completedSkills = userSkills.filter(skill => 
    skill.status === 'completed' && pathSkills.includes(skill.skill_id)
  );
  
  const progressPercentage = (completedSkills.length / pathSkills.length) * 100;
  
  return {
    percentage: Math.round(progressPercentage * 100) / 100,
    completed: completedSkills.length,
    total: pathSkills.length,
    nextRecommended: getNextRecommendedSkills(userSkills, pathSkills)
  };
}
```

### 5. Algoritmo de Recomendaciones

#### 5.1 Lógica de Recomendaciones
1. **Analizar Skills Completadas**: Identificar qué skills ya domina el usuario
2. **Verificar Prerequisites**: Asegurar que se cumplan dependencias
3. **Calcular Dificultad**: Recomendar progresión gradual de dificultad
4. **Considerar Intereses**: Basado en áreas de mayor actividad del usuario
5. **Optimizar Tiempo**: Sugerir skills con cursos disponibles inmediatamente

#### 5.2 Factores de Recomendación
```yaml
recommendation_factors:
  prerequisite_completion: 40%  # Skills prerequisitos completados
  difficulty_progression: 25%   # Progresión gradual de dificultad
  user_interests: 20%          # Áreas de mayor interés del usuario
  course_availability: 10%     # Disponibilidad de cursos relacionados
  peer_success: 5%            # Éxito de otros usuarios con perfil similar
```

### 6. Integración con Otros Sistemas

#### 6.1 Integración con Cursos
- **Auto-mapping**: Skills se mapean automáticamente a cursos disponibles
- **Recomendaciones**: Sugerir cursos específicos para skills pendientes
- **Progress Sync**: Completar curso actualiza automáticamente skill progress
- **Certificaciones**: Integrar certificaciones externas como verificación

#### 6.2 Integración con Sistema de Expertos
- **Skill Verification**: Expertos pueden verificar skills en su área
- **Path Review**: Expertos pueden revisar y recomendar career paths
- **Mentoring**: Conexión con expertos para guidance personalizado
- **Quality Assurance**: Expertos validan la calidad de paths personalizados

### 7. Métricas y Analytics

#### 7.1 Métricas de Usuario
- **Completion Rate**: Porcentaje de skills completadas por path
- **Time to Complete**: Tiempo promedio para completar skills/paths
- **Engagement**: Frecuencia de uso del sistema de career paths
- **Success Rate**: Porcentaje de usuarios que completan paths activados

#### 7.2 Métricas de Sistema
- **Popular Paths**: Career paths más activados y completados
- **Skill Demand**: Skills más buscadas y completadas
- **Path Effectiveness**: Correlación entre completion y objetivos de carrera
- **User Satisfaction**: Ratings y feedback de career paths

### 8. Consideraciones Técnicas

#### 8.1 Performance
- **Lazy Loading**: Cargar skills repository bajo demanda
- **Caching**: Cache de paths populares y user progress
- **Optimización de Canvas**: Virtualización para diagramas grandes
- **Real-time Updates**: WebSocket para updates de progreso en tiempo real

#### 8.2 Escalabilidad
- **Microservicios**: Separar career paths, skills, y progress tracking
- **Database Sharding**: Particionar por usuario o área de skills
- **CDN**: Distribución de assets visuales y diagramas
- **API Rate Limiting**: Prevenir abuso del sistema de recomendaciones

### 9. Roadmap Futuro

#### 9.1 Funcionalidades Planificadas
- **AI-Powered Recommendations**: ML para recomendaciones más precisas
- **Collaborative Paths**: Career paths creados colaborativamente
- **Industry Integration**: Integración con job boards y LinkedIn
- **Gamification**: Badges, achievements, y leaderboards
- **Mobile App**: Editor móvil para career paths

#### 9.2 Integraciones Externas
- **LinkedIn Learning**: Mapeo automático de cursos externos
- **GitHub**: Análisis de repositories para skill verification
- **Coursera/Udemy**: Integración con plataformas de cursos externos
- **Job Boards**: Análisis de job requirements para path suggestions
- **Professional Networks**: Integración con redes profesionales
