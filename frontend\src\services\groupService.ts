import { apiClient } from './apiClient';

export interface Group {
  id: string;
  name: string;
  description?: string;
  category: string;
  leaderId: string;
  leader: {
    id: string;
    firstName: string;
    lastName: string;
    avatar?: string;
  };
  type: 'public' | 'private';
  memberCount: number;
  members?: GroupMember[];
  createdAt: string;
  updatedAt: string;
  tenantId: string;
}

export interface GroupMember {
  id: string;
  userId: string;
  groupId: string;
  role: 'leader' | 'member';
  joinedAt: string;
  user: {
    id: string;
    firstName: string;
    lastName: string;
    avatar?: string;
  };
}

export interface CreateGroupRequest {
  name: string;
  description?: string;
  category: string;
  leaderId: string;
  type: 'public' | 'private';
}

export interface UpdateGroupRequest {
  name?: string;
  description?: string;
  category?: string;
  leaderId?: string;
  type?: 'public' | 'private';
}

export interface GroupFilters {
  search?: string;
  category?: string;
  type?: 'public' | 'private';
  leaderId?: string;
}

export const groupService = {
  // Get all groups with optional filters
  async getGroups(filters?: GroupFilters): Promise<Group[]> {
    const params = new URLSearchParams();
    if (filters?.search) params.append('search', filters.search);
    if (filters?.category) params.append('category', filters.category);
    if (filters?.type) params.append('type', filters.type);
    if (filters?.leaderId) params.append('leaderId', filters.leaderId);

    const response = await apiClient.get(`/groups?${params.toString()}`);
    return response.data;
  },

  // Get group by ID
  async getGroup(id: string): Promise<Group> {
    const response = await apiClient.get(`/groups/${id}`);
    return response.data;
  },

  // Create new group
  async createGroup(data: CreateGroupRequest): Promise<Group> {
    const response = await apiClient.post('/groups', data);
    return response.data;
  },

  // Update group
  async updateGroup(id: string, data: UpdateGroupRequest): Promise<Group> {
    const response = await apiClient.put(`/groups/${id}`, data);
    return response.data;
  },

  // Delete group
  async deleteGroup(id: string): Promise<void> {
    await apiClient.delete(`/groups/${id}`);
  },

  // Join group
  async joinGroup(groupId: string): Promise<GroupMember> {
    const response = await apiClient.post(`/groups/${groupId}/join`);
    return response.data;
  },

  // Leave group
  async leaveGroup(groupId: string): Promise<void> {
    await apiClient.post(`/groups/${groupId}/leave`);
  },

  // Get group members
  async getGroupMembers(groupId: string): Promise<GroupMember[]> {
    const response = await apiClient.get(`/groups/${groupId}/members`);
    return response.data;
  },

  // Add member to group (leader only)
  async addMember(groupId: string, userId: string): Promise<GroupMember> {
    const response = await apiClient.post(`/groups/${groupId}/members`, { userId });
    return response.data;
  },

  // Remove member from group (leader only)
  async removeMember(groupId: string, userId: string): Promise<void> {
    await apiClient.delete(`/groups/${groupId}/members/${userId}`);
  },

  // Update member role (leader only)
  async updateMemberRole(groupId: string, userId: string, role: 'leader' | 'member'): Promise<GroupMember> {
    const response = await apiClient.put(`/groups/${groupId}/members/${userId}`, { role });
    return response.data;
  },

  // Get groups where user is a member
  async getUserGroups(): Promise<Group[]> {
    const response = await apiClient.get('/groups/my-groups');
    return response.data;
  },

  // Get public groups for marketplace
  async getPublicGroups(filters?: Omit<GroupFilters, 'type'>): Promise<Group[]> {
    const params = new URLSearchParams();
    params.append('type', 'public');
    if (filters?.search) params.append('search', filters.search);
    if (filters?.category) params.append('category', filters.category);

    const response = await apiClient.get(`/groups?${params.toString()}`);
    return response.data;
  },

  // Get group statistics
  async getGroupStats(): Promise<{
    totalGroups: number;
    publicGroups: number;
    privateGroups: number;
    totalMembers: number;
    averageMembersPerGroup: number;
  }> {
    const response = await apiClient.get('/groups/stats');
    return response.data;
  },
};
