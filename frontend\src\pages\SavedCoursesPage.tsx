import React, { useState } from 'react';
import { Heart, Search, Filter } from 'lucide-react';
import { CourseGrid } from '@/components/courses/CourseGrid';
import { CourseFilters } from '@/components/courses/CourseFilters';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Course, CourseFilters as CourseFiltersType } from '@/types';

const savedCourses: Course[] = [
  {
    id: 'data-science-1',
    title: 'Ciencia de Datos',
    description: 'Análisis de datos con Python y R',
    category: 'Tecnología',
    level: 'intermediate',
    duration: 12,
    studentCount: 187,
    rating: 4.6,
    creator: { id: '5', firstName: 'Dra. <PERSON>', lastName: 'Chen' },
    status: 'published',
    tenantId: '1',
    creatorId: '5',
    metadata: {},
    version: 1,
    createdAt: '2024-01-01',
    updatedAt: '2024-01-01',
  },
  {
    id: 'english-b2-1',
    title: 'English B2 Intermediate',
    description: 'Inglés intermedio alto',
    category: 'Idiomas',
    level: 'intermediate',
    duration: 16,
    studentCount: 456,
    rating: 4.9,
    creator: { id: '6', firstName: 'Prof. <PERSON>', lastName: '<PERSON>' },
    status: 'published',
    tenantId: '1',
    creatorId: '6',
    metadata: {},
    version: 1,
    createdAt: '2024-01-01',
    updatedAt: '2024-01-01',
  },
  {
    id: 'ux-design-1',
    title: 'UX Design Fundamentals',
    description: 'Principios básicos del diseño de experiencia de usuario',
    category: 'Diseño',
    level: 'beginner',
    duration: 8,
    studentCount: 234,
    rating: 4.7,
    creator: { id: '7', firstName: 'Prof. Silva', lastName: 'Silva' },
    status: 'published',
    tenantId: '1',
    creatorId: '7',
    metadata: {},
    version: 1,
    createdAt: '2024-01-01',
    updatedAt: '2024-01-01',
  },
  {
    id: 'marketing-digital-1',
    title: 'Marketing Digital',
    description: 'Estrategias de marketing en el mundo digital',
    category: 'Negocios',
    level: 'intermediate',
    duration: 10,
    studentCount: 312,
    rating: 4.5,
    creator: { id: '8', firstName: 'Ing. Morales', lastName: 'Morales' },
    status: 'published',
    tenantId: '1',
    creatorId: '8',
    metadata: {},
    version: 1,
    createdAt: '2024-01-01',
    updatedAt: '2024-01-01',
  },
];

const categories = [
  'Tecnología',
  'Idiomas',
  'Diseño',
  'Negocios',
  'Matemáticas',
  'Ciencias',
  'Arte',
  'Historia',
];

export default function SavedCoursesPage() {
  const [filters, setFilters] = useState<CourseFiltersType>({});
  const [showFilters, setShowFilters] = useState(false);

  // Filter courses based on current filters
  const filteredCourses = savedCourses.filter((course) => {
    if (filters.category && course.category !== filters.category) return false;
    if (filters.level && course.level !== filters.level) return false;
    if (filters.search) {
      const searchLower = filters.search.toLowerCase();
      return (
        course.title.toLowerCase().includes(searchLower) ||
        course.description.toLowerCase().includes(searchLower)
      );
    }
    return true;
  });

  const handleEnrollCourse = (courseId: string) => {
    console.log('Enrolling in course:', courseId);
  };

  const handleUnsaveCourse = (courseId: string) => {
    console.log('Removing from saved:', courseId);
  };

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center">
          <Heart className="w-8 h-8 text-red-500 mr-3" />
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Cursos Guardados</h1>
            <p className="text-gray-600 mt-2">
              Tus cursos favoritos guardados para revisar más tarde
            </p>
          </div>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="mb-8">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
          {/* Search */}
          <div className="relative flex-1 max-w-md">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Search className="h-4 w-4 text-gray-400" />
            </div>
            <Input
              type="text"
              placeholder="Buscar en guardados..."
              value={filters.search || ''}
              onChange={(e) => setFilters({ ...filters, search: e.target.value })}
              className="pl-10"
            />
          </div>

          {/* Filter Toggle */}
          <Button
            variant="outline"
            onClick={() => setShowFilters(!showFilters)}
            className="lg:hidden"
          >
            <Filter className="w-4 h-4 mr-2" />
            Filtros
          </Button>

          {/* Quick Filters */}
          <div className="hidden lg:flex items-center space-x-2">
            <select
              value={filters.category || ''}
              onChange={(e) => setFilters({ ...filters, category: e.target.value || undefined })}
              className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">Todas las categorías</option>
              {categories.map((category) => (
                <option key={category} value={category}>
                  {category}
                </option>
              ))}
            </select>

            <select
              value={filters.level || ''}
              onChange={(e) => setFilters({ ...filters, level: e.target.value || undefined })}
              className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">Todos los niveles</option>
              <option value="beginner">Principiante</option>
              <option value="intermediate">Intermedio</option>
              <option value="advanced">Avanzado</option>
            </select>
          </div>
        </div>

        {/* Mobile Filters */}
        {showFilters && (
          <div className="mt-4 p-4 bg-gray-50 rounded-lg lg:hidden">
            <div className="grid grid-cols-1 gap-4">
              <select
                value={filters.category || ''}
                onChange={(e) => setFilters({ ...filters, category: e.target.value || undefined })}
                className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">Todas las categorías</option>
                {categories.map((category) => (
                  <option key={category} value={category}>
                    {category}
                  </option>
                ))}
              </select>

              <select
                value={filters.level || ''}
                onChange={(e) => setFilters({ ...filters, level: e.target.value || undefined })}
                className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">Todos los niveles</option>
                <option value="beginner">Principiante</option>
                <option value="intermediate">Intermedio</option>
                <option value="advanced">Avanzado</option>
              </select>
            </div>
          </div>
        )}
      </div>

      {/* Results Count */}
      <div className="mb-6">
        <p className="text-gray-600">
          {filteredCourses.length} de {savedCourses.length} cursos guardados
        </p>
      </div>

      {/* Course Grid */}
      {filteredCourses.length > 0 ? (
        <CourseGrid
          courses={filteredCourses}
          onEnroll={handleEnrollCourse}
          onSave={handleUnsaveCourse}
          savedCourses={savedCourses.map(c => c.id)}
          columns={3}
        />
      ) : (
        <div className="text-center py-12">
          {savedCourses.length === 0 ? (
            <>
              <div className="w-24 h-24 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
                <Heart className="w-12 h-12 text-gray-400" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                No tienes cursos guardados
              </h3>
              <p className="text-gray-500 mb-4">
                Explora el marketplace y guarda cursos que te interesen
              </p>
              <Button>
                Explorar Cursos
              </Button>
            </>
          ) : (
            <>
              <div className="w-24 h-24 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
                <Search className="w-12 h-12 text-gray-400" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                No se encontraron cursos
              </h3>
              <p className="text-gray-500 mb-4">
                Intenta ajustar los filtros de búsqueda
              </p>
              <Button
                variant="outline"
                onClick={() => setFilters({})}
              >
                Limpiar Filtros
              </Button>
            </>
          )}
        </div>
      )}
    </div>
  );
}
