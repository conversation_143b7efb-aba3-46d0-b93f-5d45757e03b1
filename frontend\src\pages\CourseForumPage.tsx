import React, { useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { 
  ArrowLeft, 
  Plus, 
  Search, 
  MessageSquare, 
  ThumbsUp, 
  Pin, 
  Lock, 
  Award,
  Clock,
  User,
  Reply
} from 'lucide-react';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Badge } from '@/components/ui/Badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/Avatar';
import { Mo<PERSON>, ModalHeader, ModalFooter } from '@/components/ui/Modal';
import { generateInitials } from '@/lib/utils';
import toast from 'react-hot-toast';

interface ForumPost {
  id: string;
  title: string;
  content: string;
  author: {
    id: string;
    name: string;
    avatar?: string;
    isExpert: boolean;
  };
  category: string;
  replyCount: number;
  likeCount: number;
  isHighlighted: boolean;
  isPinned: boolean;
  isLocked: boolean;
  createdAt: string;
  lastActivity: string;
  tags: string[];
}

interface ForumReply {
  id: string;
  content: string;
  author: {
    id: string;
    name: string;
    avatar?: string;
    isExpert: boolean;
  };
  likeCount: number;
  isSolution: boolean;
  createdAt: string;
  replies?: ForumReply[];
}

const mockCourse = {
  id: 'devops-1',
  title: 'DevOps Fundamentals',
};

const mockCategories = [
  { id: '1', name: 'General', description: 'Discusiones generales del curso' },
  { id: '2', name: 'Tareas', description: 'Preguntas sobre tareas y proyectos' },
  { id: '3', name: 'Exámenes', description: 'Dudas sobre exámenes y evaluaciones' },
  { id: '4', name: 'Recursos', description: 'Compartir recursos adicionales' },
];

const mockPosts: ForumPost[] = [
  {
    id: '1',
    title: '¿Cómo configurar un pipeline de CI/CD con Jenkins?',
    content: 'Estoy teniendo problemas para configurar mi primer pipeline. ¿Alguien puede ayudarme con los pasos básicos?',
    author: {
      id: '1',
      name: 'María García',
      isExpert: false,
    },
    category: 'General',
    replyCount: 8,
    likeCount: 12,
    isHighlighted: true,
    isPinned: false,
    isLocked: false,
    createdAt: '2024-01-15T10:30:00Z',
    lastActivity: '2024-01-15T14:20:00Z',
    tags: ['jenkins', 'ci-cd', 'pipeline'],
  },
  {
    id: '2',
    title: 'Recursos adicionales para Docker',
    content: 'Comparto algunos recursos útiles que encontré para aprender Docker más a fondo.',
    author: {
      id: '2',
      name: 'Dr. López',
      isExpert: true,
    },
    category: 'Recursos',
    replyCount: 15,
    likeCount: 25,
    isHighlighted: false,
    isPinned: true,
    isLocked: false,
    createdAt: '2024-01-14T09:15:00Z',
    lastActivity: '2024-01-15T16:45:00Z',
    tags: ['docker', 'recursos', 'contenedores'],
  },
  {
    id: '3',
    title: 'Dudas sobre el Examen Final',
    content: '¿El examen final incluirá preguntas prácticas sobre Kubernetes?',
    author: {
      id: '3',
      name: 'Carlos Ruiz',
      isExpert: false,
    },
    category: 'Exámenes',
    replyCount: 3,
    likeCount: 7,
    isHighlighted: false,
    isPinned: false,
    isLocked: true,
    createdAt: '2024-01-13T15:20:00Z',
    lastActivity: '2024-01-14T11:30:00Z',
    tags: ['examen', 'kubernetes'],
  },
];

export default function CourseForumPage() {
  const { courseId } = useParams();
  const [course] = useState(mockCourse);
  const [posts, setPosts] = useState<ForumPost[]>(mockPosts);
  const [categories] = useState(mockCategories);
  const [selectedCategory, setSelectedCategory] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [isCreatePostModalOpen, setIsCreatePostModalOpen] = useState(false);
  const [selectedPost, setSelectedPost] = useState<ForumPost | null>(null);

  const [newPost, setNewPost] = useState({
    title: '',
    content: '',
    category: '',
    tags: '',
  });

  const filteredPosts = posts.filter(post => {
    const matchesCategory = !selectedCategory || post.category === selectedCategory;
    const matchesSearch = !searchTerm || 
      post.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      post.content.toLowerCase().includes(searchTerm.toLowerCase()) ||
      post.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));
    
    return matchesCategory && matchesSearch;
  });

  const sortedPosts = filteredPosts.sort((a, b) => {
    // Pinned posts first
    if (a.isPinned && !b.isPinned) return -1;
    if (!a.isPinned && b.isPinned) return 1;
    
    // Then by last activity
    return new Date(b.lastActivity).getTime() - new Date(a.lastActivity).getTime();
  });

  const handleCreatePost = () => {
    if (!newPost.title || !newPost.content || !newPost.category) {
      toast.error('Por favor, completa todos los campos obligatorios');
      return;
    }

    const post: ForumPost = {
      id: Date.now().toString(),
      title: newPost.title,
      content: newPost.content,
      author: {
        id: 'current-user',
        name: 'Usuario Actual',
        isExpert: false,
      },
      category: newPost.category,
      replyCount: 0,
      likeCount: 0,
      isHighlighted: false,
      isPinned: false,
      isLocked: false,
      createdAt: new Date().toISOString(),
      lastActivity: new Date().toISOString(),
      tags: newPost.tags.split(',').map(t => t.trim()).filter(t => t),
    };

    setPosts([post, ...posts]);
    setIsCreatePostModalOpen(false);
    setNewPost({ title: '', content: '', category: '', tags: '' });
    toast.success('Publicación creada exitosamente');
  };

  const handleLikePost = (postId: string) => {
    setPosts(posts.map(post => 
      post.id === postId 
        ? { ...post, likeCount: post.likeCount + 1 }
        : post
    ));
    toast.success('¡Me gusta agregado!');
  };

  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) return 'Hace menos de 1 hora';
    if (diffInHours < 24) return `Hace ${diffInHours} hora${diffInHours > 1 ? 's' : ''}`;
    
    const diffInDays = Math.floor(diffInHours / 24);
    return `Hace ${diffInDays} día${diffInDays > 1 ? 's' : ''}`;
  };

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center space-x-4 mb-4">
          <Link to={`/courses/${courseId}`}>
            <Button variant="ghost" size="icon">
              <ArrowLeft className="h-4 w-4" />
            </Button>
          </Link>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Foro del Curso</h1>
            <p className="text-gray-600">{course.title}</p>
          </div>
        </div>

        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div className="flex items-center space-x-4">
            <div className="relative">
              <Search className="absolute left-3 top-2.5 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Buscar en el foro..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 w-64"
              />
            </div>
            
            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">Todas las categorías</option>
              {categories.map(category => (
                <option key={category.id} value={category.name}>
                  {category.name}
                </option>
              ))}
            </select>
          </div>

          <Button onClick={() => setIsCreatePostModalOpen(true)}>
            <Plus className="w-4 h-4 mr-2" />
            Nueva Publicación
          </Button>
        </div>
      </div>

      {/* Categories Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
        {categories.map((category) => {
          const categoryPosts = posts.filter(p => p.category === category.name);
          const totalReplies = categoryPosts.reduce((sum, p) => sum + p.replyCount, 0);
          
          return (
            <Card 
              key={category.id} 
              className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => setSelectedCategory(category.name)}
            >
              <CardContent className="p-4">
                <h3 className="font-medium text-gray-900 mb-1">{category.name}</h3>
                <p className="text-sm text-gray-600 mb-3">{category.description}</p>
                <div className="flex items-center justify-between text-xs text-gray-500">
                  <span>{categoryPosts.length} publicaciones</span>
                  <span>{totalReplies} respuestas</span>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Forum Posts */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>
              Publicaciones 
              {selectedCategory && ` - ${selectedCategory}`}
              <span className="text-sm font-normal text-gray-500 ml-2">
                ({sortedPosts.length})
              </span>
            </span>
            {selectedCategory && (
              <Button 
                variant="outline" 
                size="sm"
                onClick={() => setSelectedCategory('')}
              >
                Ver Todas
              </Button>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {sortedPosts.length === 0 ? (
              <div className="text-center py-12">
                <MessageSquare className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  No hay publicaciones
                </h3>
                <p className="text-gray-600 mb-4">
                  {selectedCategory 
                    ? `No hay publicaciones en la categoría "${selectedCategory}"`
                    : 'Sé el primero en crear una publicación'
                  }
                </p>
                <Button onClick={() => setIsCreatePostModalOpen(true)}>
                  <Plus className="w-4 h-4 mr-2" />
                  Crear Publicación
                </Button>
              </div>
            ) : (
              sortedPosts.map((post) => (
                <div
                  key={post.id}
                  className={`border rounded-lg p-4 hover:bg-gray-50 transition-colors cursor-pointer ${
                    post.isHighlighted ? 'border-green-300 bg-green-50' : 'border-gray-200'
                  }`}
                  onClick={() => setSelectedPost(post)}
                >
                  <div className="flex items-start space-x-4">
                    <Avatar className="w-10 h-10">
                      <AvatarImage src={post.author.avatar} />
                      <AvatarFallback className="bg-blue-500 text-white">
                        {generateInitials(post.author.name)}
                      </AvatarFallback>
                    </Avatar>

                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2 mb-2">
                        {post.isPinned && (
                          <Pin className="w-4 h-4 text-blue-600" />
                        )}
                        {post.isLocked && (
                          <Lock className="w-4 h-4 text-gray-600" />
                        )}
                        {post.isHighlighted && (
                          <Award className="w-4 h-4 text-green-600" />
                        )}
                        <h3 className="text-lg font-medium text-gray-900 truncate">
                          {post.title}
                        </h3>
                      </div>

                      <p className="text-gray-600 text-sm mb-3 line-clamp-2">
                        {post.content}
                      </p>

                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-4 text-sm text-gray-500">
                          <div className="flex items-center space-x-1">
                            <User className="w-4 h-4" />
                            <span>{post.author.name}</span>
                            {post.author.isExpert && (
                              <Badge variant="outline" className="text-xs">
                                Experto
                              </Badge>
                            )}
                          </div>
                          <div className="flex items-center space-x-1">
                            <Clock className="w-4 h-4" />
                            <span>{formatTimeAgo(post.createdAt)}</span>
                          </div>
                        </div>

                        <div className="flex items-center space-x-4 text-sm text-gray-500">
                          <div className="flex items-center space-x-1">
                            <MessageSquare className="w-4 h-4" />
                            <span>{post.replyCount}</span>
                          </div>
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              handleLikePost(post.id);
                            }}
                            className="flex items-center space-x-1 hover:text-blue-600 transition-colors"
                          >
                            <ThumbsUp className="w-4 h-4" />
                            <span>{post.likeCount}</span>
                          </button>
                        </div>
                      </div>

                      {post.tags.length > 0 && (
                        <div className="flex flex-wrap gap-1 mt-3">
                          {post.tags.map((tag, index) => (
                            <Badge key={index} variant="outline" className="text-xs">
                              {tag}
                            </Badge>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        </CardContent>
      </Card>

      {/* Create Post Modal */}
      <Modal
        isOpen={isCreatePostModalOpen}
        onClose={() => setIsCreatePostModalOpen(false)}
        title="Nueva Publicación"
        size="lg"
      >
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Título *
            </label>
            <Input
              placeholder="Título de tu publicación"
              value={newPost.title}
              onChange={(e) => setNewPost({ ...newPost, title: e.target.value })}
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Categoría *
            </label>
            <select
              value={newPost.category}
              onChange={(e) => setNewPost({ ...newPost, category: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">Seleccionar categoría</option>
              {categories.map(category => (
                <option key={category.id} value={category.name}>
                  {category.name}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Contenido *
            </label>
            <textarea
              rows={6}
              placeholder="Escribe el contenido de tu publicación..."
              value={newPost.content}
              onChange={(e) => setNewPost({ ...newPost, content: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Etiquetas
            </label>
            <Input
              placeholder="etiqueta1, etiqueta2, etiqueta3"
              value={newPost.tags}
              onChange={(e) => setNewPost({ ...newPost, tags: e.target.value })}
            />
            <p className="text-xs text-gray-500 mt-1">Separar con comas</p>
          </div>
        </div>

        <ModalFooter>
          <Button
            variant="outline"
            onClick={() => setIsCreatePostModalOpen(false)}
          >
            Cancelar
          </Button>
          <Button onClick={handleCreatePost}>
            Publicar
          </Button>
        </ModalFooter>
      </Modal>

      {/* Post Detail Modal */}
      {selectedPost && (
        <Modal
          isOpen={!!selectedPost}
          onClose={() => setSelectedPost(null)}
          title={selectedPost.title}
          size="xl"
        >
          <div className="space-y-4">
            <div className="flex items-center space-x-4">
              <Avatar className="w-10 h-10">
                <AvatarImage src={selectedPost.author.avatar} />
                <AvatarFallback className="bg-blue-500 text-white">
                  {generateInitials(selectedPost.author.name)}
                </AvatarFallback>
              </Avatar>
              <div>
                <div className="flex items-center space-x-2">
                  <span className="font-medium">{selectedPost.author.name}</span>
                  {selectedPost.author.isExpert && (
                    <Badge variant="outline">Experto</Badge>
                  )}
                </div>
                <span className="text-sm text-gray-500">
                  {formatTimeAgo(selectedPost.createdAt)}
                </span>
              </div>
            </div>

            <div className="prose max-w-none">
              <p className="whitespace-pre-wrap">{selectedPost.content}</p>
            </div>

            {selectedPost.tags.length > 0 && (
              <div className="flex flex-wrap gap-1">
                {selectedPost.tags.map((tag, index) => (
                  <Badge key={index} variant="outline" className="text-xs">
                    {tag}
                  </Badge>
                ))}
              </div>
            )}

            <div className="flex items-center space-x-4 pt-4 border-t">
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleLikePost(selectedPost.id)}
              >
                <ThumbsUp className="w-4 h-4 mr-2" />
                Me gusta ({selectedPost.likeCount})
              </Button>
              <Button variant="outline" size="sm">
                <Reply className="w-4 h-4 mr-2" />
                Responder
              </Button>
            </div>
          </div>
        </Modal>
      )}
    </div>
  );
}
