"""
Base models and mixins for Arroyo University
"""

from datetime import datetime
from typing import Optional
from uuid import UUID, uuid4

from sqlmodel import SQLModel, Field


class TimestampMixin(SQLModel):
    """Mixin for created_at and updated_at timestamps"""
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)


class BaseModel(SQLModel):
    """Base model with common fields"""
    id: Optional[UUID] = Field(default_factory=uuid4, primary_key=True)
    tenant_id: UUID = Field(foreign_key="tenants.tenant_id", index=True)


class TenantMixin(SQLModel):
    """Mixin for tenant isolation"""
    tenant_id: UUID = Field(foreign_key="tenants.tenant_id", index=True)


class SoftDeleteMixin(SQLModel):
    """Mixin for soft delete functionality"""
    is_deleted: bool = Field(default=False)
    deleted_at: Optional[datetime] = Field(default=None)


class MetadataMixin(SQLModel):
    """Mixin for JSONB metadata field"""
    metadata: Optional[dict] = Field(default_factory=dict, sa_column_kwargs={"type_": "JSONB"})


# Response models
class SuccessResponse(SQLModel):
    """Standard success response"""
    success: bool = True
    message: str
    data: Optional[dict] = None


class ErrorResponse(SQLModel):
    """Standard error response"""
    success: bool = False
    error: str
    details: Optional[dict] = None


class PaginationParams(SQLModel):
    """Pagination parameters"""
    page: int = Field(default=1, ge=1)
    size: int = Field(default=20, ge=1, le=100)
    sort_by: Optional[str] = None
    sort_order: str = Field(default="asc", regex="^(asc|desc)$")


class PaginatedResponse(SQLModel):
    """Paginated response wrapper"""
    items: list
    total: int
    page: int
    size: int
    pages: int
    has_next: bool
    has_prev: bool
