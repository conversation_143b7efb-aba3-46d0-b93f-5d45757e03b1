import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function formatDate(date: string | Date): string {
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  }).format(new Date(date))
}

export function formatRelativeTime(date: string | Date): string {
  const now = new Date()
  const targetDate = new Date(date)
  const diffInSeconds = Math.floor((now.getTime() - targetDate.getTime()) / 1000)

  if (diffInSeconds < 60) {
    return 'just now'
  } else if (diffInSeconds < 3600) {
    const minutes = Math.floor(diffInSeconds / 60)
    return `${minutes} minute${minutes > 1 ? 's' : ''} ago`
  } else if (diffInSeconds < 86400) {
    const hours = Math.floor(diffInSeconds / 3600)
    return `${hours} hour${hours > 1 ? 's' : ''} ago`
  } else if (diffInSeconds < 604800) {
    const days = Math.floor(diffInSeconds / 86400)
    return `${days} day${days > 1 ? 's' : ''} ago`
  } else {
    return formatDate(date)
  }
}

export function truncateText(text: string, maxLength: number): string {
  if (text.length <= maxLength) return text
  return text.slice(0, maxLength) + '...'
}

export function generateInitials(firstName?: string, lastName?: string): string {
  if (!firstName && !lastName) return 'U'
  return `${firstName?.[0] || ''}${lastName?.[0] || ''}`.toUpperCase()
}

export function getGradientByCategory(category: string): string {
  const gradients: Record<string, string> = {
    'Tecnología': 'from-blue-500 to-purple-600',
    'Idiomas': 'from-green-500 to-teal-600',
    'Matemáticas': 'from-red-500 to-pink-600',
    'Ciencias': 'from-teal-500 to-cyan-600',
    'Negocios': 'from-yellow-500 to-orange-600',
    'Arte': 'from-pink-500 to-rose-600',
    'Historia': 'from-indigo-500 to-purple-600',
    'Metodologías': 'from-blue-500 to-teal-600',
  }
  return gradients[category] || 'from-gray-500 to-gray-600'
}

export function getBadgeColorByCategory(category: string): string {
  const colors: Record<string, string> = {
    'Tecnología': 'badge-blue',
    'Idiomas': 'badge-green',
    'Matemáticas': 'badge-red',
    'Ciencias': 'badge-teal',
    'Negocios': 'badge-yellow',
    'Arte': 'badge-pink',
    'Historia': 'badge-indigo',
    'Metodologías': 'badge-blue',
  }
  return colors[category] || 'badge-gray'
}

export function getBadgeColorByLevel(level: string): string {
  const colors: Record<string, string> = {
    'beginner': 'badge-yellow',
    'intermediate': 'badge-green',
    'advanced': 'badge-orange',
  }
  return colors[level] || 'badge-gray'
}

export function formatDuration(weeks: number): string {
  if (weeks === 1) return '1 semana'
  if (weeks < 4) return `${weeks} semanas`
  if (weeks === 4) return '1 mes'
  const months = Math.floor(weeks / 4)
  const remainingWeeks = weeks % 4
  if (remainingWeeks === 0) {
    return months === 1 ? '1 mes' : `${months} meses`
  }
  return `${months} mes${months > 1 ? 'es' : ''} y ${remainingWeeks} semana${remainingWeeks > 1 ? 's' : ''}`
}

export function formatStudentCount(count: number): string {
  if (count < 1000) return count.toString()
  if (count < 1000000) return `${(count / 1000).toFixed(1)}k`
  return `${(count / 1000000).toFixed(1)}M`
}

export function formatRating(rating: number): string {
  return rating.toFixed(1)
}

export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout
  return (...args: Parameters<T>) => {
    clearTimeout(timeout)
    timeout = setTimeout(() => func(...args), wait)
  }
}

export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

export function isValidPassword(password: string): boolean {
  // At least 8 characters, 1 uppercase, 1 lowercase, 1 number
  const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,}$/
  return passwordRegex.test(password)
}
