"""
Question management service
"""

import csv
import json
from datetime import datetime
from typing import Optional, List, Dict, Any
from uuid import UUID
from io import StringIO

from sqlmodel import Session, select, func

from ..models.question import (
    Question, QuestionCreate, QuestionUpdate, QuestionResponse,
    QuestionBank, QuestionBankCreate, QuestionBankUpdate, QuestionBankResponse,
    QuestionStatistics, AIQuestionGeneration, QuestionImport, QuestionImportResult,
    QuestionValidation, QuestionType, DifficultyLevel
)
from ..models.user import User


class QuestionService:
    """Question management service"""
    
    def __init__(self, db: Session):
        self.db = db
    
    async def create_question_bank(self, bank_data: QuestionBankCreate, created_by: UUID, tenant_id: UUID) -> QuestionBank:
        """Create a new question bank"""
        bank = QuestionBank(
            tenant_id=tenant_id,
            created_by=created_by,
            name=bank_data.name,
            description=bank_data.description,
            subject_area=bank_data.subject_area,
            is_public=bank_data.is_public
        )
        
        self.db.add(bank)
        self.db.commit()
        self.db.refresh(bank)
        
        return bank
    
    async def get_question_bank(self, bank_id: UUID, tenant_id: UUID) -> Optional[QuestionBankResponse]:
        """Get question bank by ID"""
        bank = self.db.exec(
            select(QuestionBank).where(
                QuestionBank.bank_id == bank_id,
                QuestionBank.tenant_id == tenant_id
            )
        ).first()
        
        if not bank:
            return None
        
        # Get question count
        question_count = self.db.exec(
            select(func.count(Question.question_id)).where(
                Question.bank_id == bank_id
            )
        ).first() or 0
        
        # Get creator name
        creator = self.db.get(User, bank.created_by)
        creator_name = f"{creator.first_name} {creator.last_name}" if creator else None
        
        return QuestionBankResponse(
            bank_id=bank.bank_id,
            created_by=bank.created_by,
            name=bank.name,
            description=bank.description,
            subject_area=bank.subject_area,
            is_public=bank.is_public,
            difficulty_level=bank.difficulty_level,
            created_at=bank.created_at,
            updated_at=bank.updated_at,
            question_count=question_count,
            creator_name=creator_name
        )
    
    async def create_question(self, question_data: QuestionCreate, created_by: UUID, tenant_id: UUID) -> Question:
        """Create a new question"""
        # Validate question data
        validation = await self.validate_question(question_data.dict())
        if not validation.is_valid:
            raise ValueError(f"Invalid question data: {', '.join(validation.errors)}")
        
        question = Question(
            tenant_id=tenant_id,
            created_by=created_by,
            bank_id=question_data.bank_id,
            title=question_data.title,
            content=question_data.content,
            question_type=question_data.question_type,
            points=question_data.points,
            difficulty=question_data.difficulty,
            category=question_data.category,
            time_limit_seconds=question_data.time_limit_seconds,
            explanation=question_data.explanation,
            question_data=question_data.question_data,
            tags=question_data.tags or []
        )
        
        self.db.add(question)
        self.db.commit()
        self.db.refresh(question)
        
        return question
    
    async def get_question(self, question_id: UUID, tenant_id: UUID) -> Optional[QuestionResponse]:
        """Get question by ID"""
        question = self.db.exec(
            select(Question).where(
                Question.question_id == question_id,
                Question.tenant_id == tenant_id
            )
        ).first()
        
        if not question:
            return None
        
        # Get creator name
        creator = self.db.get(User, question.created_by)
        creator_name = f"{creator.first_name} {creator.last_name}" if creator else None
        
        # Get bank name
        bank_name = None
        if question.bank_id:
            bank = self.db.get(QuestionBank, question.bank_id)
            bank_name = bank.name if bank else None
        
        return QuestionResponse(
            question_id=question.question_id,
            bank_id=question.bank_id,
            created_by=question.created_by,
            title=question.title,
            content=question.content,
            question_type=question.question_type,
            points=question.points,
            difficulty=question.difficulty,
            category=question.category,
            time_limit_seconds=question.time_limit_seconds,
            explanation=question.explanation,
            question_data=question.question_data,
            tags=question.tags,
            is_active=question.is_active,
            usage_count=question.usage_count,
            correct_rate=question.correct_rate,
            average_time_seconds=question.average_time_seconds,
            created_at=question.created_at,
            updated_at=question.updated_at,
            creator_name=creator_name,
            bank_name=bank_name
        )
    
    async def update_question(self, question_id: UUID, question_data: QuestionUpdate, tenant_id: UUID) -> Optional[Question]:
        """Update question"""
        question = self.db.exec(
            select(Question).where(
                Question.question_id == question_id,
                Question.tenant_id == tenant_id
            )
        ).first()
        
        if not question:
            return None
        
        # Update fields
        update_data = question_data.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(question, field, value)
        
        question.updated_at = datetime.utcnow()
        
        self.db.add(question)
        self.db.commit()
        self.db.refresh(question)
        
        return question
    
    async def delete_question(self, question_id: UUID, tenant_id: UUID) -> bool:
        """Delete question (soft delete)"""
        question = self.db.exec(
            select(Question).where(
                Question.question_id == question_id,
                Question.tenant_id == tenant_id
            )
        ).first()
        
        if not question:
            return False
        
        question.is_active = False
        question.updated_at = datetime.utcnow()
        
        self.db.add(question)
        self.db.commit()
        
        return True
    
    async def list_questions(
        self,
        tenant_id: UUID,
        skip: int = 0,
        limit: int = 20,
        search: str = None,
        bank_id: UUID = None,
        question_type: QuestionType = None,
        difficulty: DifficultyLevel = None,
        category: str = None,
        tags: List[str] = None,
        is_active: bool = True
    ) -> List[QuestionResponse]:
        """List questions with filtering"""
        stmt = select(Question).where(
            Question.tenant_id == tenant_id,
            Question.is_active == is_active
        )
        
        # Apply filters
        if search:
            search_term = f"%{search}%"
            stmt = stmt.where(
                (Question.title.ilike(search_term)) |
                (Question.content.ilike(search_term))
            )
        
        if bank_id:
            stmt = stmt.where(Question.bank_id == bank_id)
        
        if question_type:
            stmt = stmt.where(Question.question_type == question_type)
        
        if difficulty:
            stmt = stmt.where(Question.difficulty == difficulty)
        
        if category:
            stmt = stmt.where(Question.category == category)
        
        if tags:
            # PostgreSQL array contains operation
            for tag in tags:
                stmt = stmt.where(Question.tags.contains([tag]))
        
        stmt = stmt.offset(skip).limit(limit).order_by(Question.created_at.desc())
        questions = self.db.exec(stmt).all()
        
        # Convert to response models
        question_responses = []
        for question in questions:
            question_response = await self.get_question(question.question_id, tenant_id)
            if question_response:
                question_responses.append(question_response)
        
        return question_responses
    
    async def validate_question(self, question_data: Dict[str, Any]) -> QuestionValidation:
        """Validate question data"""
        errors = []
        warnings = []
        suggestions = []
        
        # Basic validation
        if not question_data.get('title'):
            errors.append("Title is required")
        
        if not question_data.get('content'):
            errors.append("Content is required")
        
        if not question_data.get('question_type'):
            errors.append("Question type is required")
        
        # Type-specific validation
        question_type = question_data.get('question_type')
        question_data_field = question_data.get('question_data', {})
        
        if question_type == 'multiple_choice':
            if not question_data_field.get('options'):
                errors.append("Multiple choice questions must have options")
            elif len(question_data_field.get('options', [])) < 2:
                errors.append("Multiple choice questions must have at least 2 options")
            
            if not question_data_field.get('correct_answer'):
                errors.append("Multiple choice questions must have a correct answer")
            
            # Check if correct answer is in options
            correct_answer = question_data_field.get('correct_answer')
            options = question_data_field.get('options', [])
            if correct_answer and correct_answer not in options:
                errors.append("Correct answer must be one of the provided options")
        
        elif question_type == 'true_false':
            correct_answer = question_data_field.get('correct_answer')
            if correct_answer not in [True, False, 'true', 'false']:
                errors.append("True/false questions must have a boolean correct answer")
        
        elif question_type == 'essay':
            if not question_data_field.get('max_words') and not question_data_field.get('max_characters'):
                warnings.append("Consider setting word or character limits for essay questions")
        
        # Points validation
        points = question_data.get('points', 0)
        if points <= 0:
            errors.append("Points must be greater than 0")
        
        # Time limit validation
        time_limit = question_data.get('time_limit_seconds')
        if time_limit and time_limit < 10:
            warnings.append("Very short time limits may not give students enough time to read the question")
        
        # Suggestions
        if not question_data.get('explanation'):
            suggestions.append("Consider adding an explanation to help students understand the correct answer")
        
        if not question_data.get('tags'):
            suggestions.append("Adding tags will help organize and find questions later")
        
        return QuestionValidation(
            is_valid=len(errors) == 0,
            errors=errors,
            warnings=warnings,
            suggestions=suggestions
        )
    
    async def import_questions(self, import_data: QuestionImport, tenant_id: UUID, created_by: UUID) -> QuestionImportResult:
        """Import questions from various formats"""
        imported_count = 0
        failed_count = 0
        errors = []
        questions = []
        
        try:
            if import_data.format == 'csv':
                result = await self._import_from_csv(import_data.questions_data, tenant_id, created_by, import_data.bank_id)
            elif import_data.format == 'json':
                result = await self._import_from_json(import_data.questions_data, tenant_id, created_by, import_data.bank_id)
            else:
                raise ValueError(f"Unsupported format: {import_data.format}")
            
            return result
            
        except Exception as e:
            return QuestionImportResult(
                imported_count=0,
                failed_count=len(import_data.questions_data),
                errors=[str(e)],
                questions=[]
            )
    
    async def _import_from_csv(self, csv_data: List[Dict], tenant_id: UUID, created_by: UUID, bank_id: UUID = None) -> QuestionImportResult:
        """Import questions from CSV data"""
        imported_count = 0
        failed_count = 0
        errors = []
        questions = []
        
        for row_idx, row in enumerate(csv_data):
            try:
                # Map CSV columns to question fields
                question_data = {
                    'title': row.get('title', ''),
                    'content': row.get('content', ''),
                    'question_type': row.get('type', 'multiple_choice'),
                    'points': int(row.get('points', 1)),
                    'difficulty': row.get('difficulty', 'medium'),
                    'category': row.get('category', ''),
                    'explanation': row.get('explanation', ''),
                    'tags': row.get('tags', '').split(',') if row.get('tags') else [],
                    'question_data': {}
                }
                
                # Handle multiple choice options
                if question_data['question_type'] == 'multiple_choice':
                    options = []
                    for i in range(1, 6):  # Support up to 5 options
                        option = row.get(f'option_{i}')
                        if option:
                            options.append(option.strip())
                    
                    question_data['question_data'] = {
                        'options': options,
                        'correct_answer': row.get('correct_answer', '')
                    }
                
                # Create question
                question_create = QuestionCreate(
                    bank_id=bank_id,
                    **question_data
                )
                
                question = await self.create_question(question_create, created_by, tenant_id)
                questions.append(await self.get_question(question.question_id, tenant_id))
                imported_count += 1
                
            except Exception as e:
                failed_count += 1
                errors.append(f"Row {row_idx + 1}: {str(e)}")
        
        return QuestionImportResult(
            imported_count=imported_count,
            failed_count=failed_count,
            errors=errors,
            questions=questions
        )
    
    async def _import_from_json(self, json_data: List[Dict], tenant_id: UUID, created_by: UUID, bank_id: UUID = None) -> QuestionImportResult:
        """Import questions from JSON data"""
        imported_count = 0
        failed_count = 0
        errors = []
        questions = []
        
        for idx, question_data in enumerate(json_data):
            try:
                question_data['bank_id'] = bank_id
                question_create = QuestionCreate(**question_data)
                
                question = await self.create_question(question_create, created_by, tenant_id)
                questions.append(await self.get_question(question.question_id, tenant_id))
                imported_count += 1
                
            except Exception as e:
                failed_count += 1
                errors.append(f"Question {idx + 1}: {str(e)}")
        
        return QuestionImportResult(
            imported_count=imported_count,
            failed_count=failed_count,
            errors=errors,
            questions=questions
        )
    
    async def get_question_statistics(self, question_id: UUID, tenant_id: UUID) -> Optional[QuestionStatistics]:
        """Get question usage statistics"""
        question = self.db.exec(
            select(Question).where(
                Question.question_id == question_id,
                Question.tenant_id == tenant_id
            )
        ).first()
        
        if not question:
            return None
        
        # This would be calculated from exam submissions
        # For now, return the stored values
        return QuestionStatistics(
            question_id=question_id,
            total_attempts=question.usage_count,
            correct_attempts=int(question.usage_count * (question.correct_rate or 0)),
            correct_rate=question.correct_rate or 0.0,
            average_time_seconds=question.average_time_seconds or 0.0,
            difficulty_rating=0.5,  # Would be calculated
            usage_count=question.usage_count,
            last_used=question.updated_at
        )
