"""
Authentication router for login, logout, and token management
"""

from fastapi import API<PERSON>outer, Depends, HTTPException, status, Request
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlmodel import Session
from typing import Optional
from uuid import UUID

from ..core.database import get_session
from ..services.auth_service import AuthService
from ..models.user import UserLogin, UserPasswordReset, UserPasswordResetConfirm, UserEmailVerification
from ..models.base import SuccessResponse

router = APIRouter()
security = HTTPBearer()


def get_auth_service(db: Session = Depends(get_session)) -> AuthService:
    """Get authentication service"""
    return AuthService(db)


@router.post("/login")
async def login(
    user_data: UserLogin,
    request: Request,
    tenant_id: UUID,
    auth_service: AuthService = Depends(get_auth_service)
):
    """User login"""
    try:
        # Authenticate user
        user = await auth_service.authenticate_user(
            email=user_data.email,
            password=user_data.password,
            tenant_id=tenant_id
        )
        
        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid email or password"
            )
        
        # Create session
        access_token, refresh_token = await auth_service.create_session(
            user=user,
            ip_address=request.client.host,
            user_agent=request.headers.get("user-agent")
        )
        
        return {
            "success": True,
            "message": "Login successful",
            "data": {
                "access_token": access_token,
                "refresh_token": refresh_token,
                "token_type": "bearer",
                "user": {
                    "user_id": user.user_id,
                    "email": user.email,
                    "first_name": user.first_name,
                    "last_name": user.last_name,
                    "is_verified": user.is_verified
                }
            }
        }
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.post("/logout")
async def logout(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    auth_service: AuthService = Depends(get_auth_service)
):
    """User logout"""
    try:
        success = await auth_service.logout(credentials.credentials)
        
        if success:
            return SuccessResponse(message="Logout successful")
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid token"
            )
            
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Logout failed"
        )


@router.post("/refresh")
async def refresh_token(
    refresh_token: str,
    auth_service: AuthService = Depends(get_auth_service)
):
    """Refresh access token"""
    try:
        access_token, new_refresh_token = await auth_service.refresh_token(refresh_token)
        
        return {
            "success": True,
            "message": "Token refreshed successfully",
            "data": {
                "access_token": access_token,
                "refresh_token": new_refresh_token,
                "token_type": "bearer"
            }
        }
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=str(e)
        )


@router.post("/forgot-password")
async def forgot_password(
    password_reset: UserPasswordReset,
    tenant_id: UUID,
    auth_service: AuthService = Depends(get_auth_service)
):
    """Request password reset"""
    try:
        await auth_service.request_password_reset(
            email=password_reset.email,
            tenant_id=tenant_id
        )
        
        return SuccessResponse(
            message="If the email exists, a password reset link has been sent"
        )
        
    except Exception as e:
        # Don't reveal if email exists
        return SuccessResponse(
            message="If the email exists, a password reset link has been sent"
        )


@router.post("/reset-password")
async def reset_password(
    reset_data: UserPasswordResetConfirm,
    auth_service: AuthService = Depends(get_auth_service)
):
    """Reset password with token"""
    try:
        success = await auth_service.reset_password(
            token=reset_data.token,
            new_password=reset_data.new_password
        )
        
        if success:
            return SuccessResponse(message="Password reset successful")
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Password reset failed"
            )
            
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.post("/verify-email")
async def verify_email(
    verification_data: UserEmailVerification,
    auth_service: AuthService = Depends(get_auth_service)
):
    """Verify email with token"""
    try:
        success = await auth_service.verify_email(verification_data.token)
        
        if success:
            return SuccessResponse(message="Email verified successfully")
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Email verification failed"
            )
            
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.post("/resend-verification")
async def resend_verification(
    email_data: UserPasswordReset,  # Reuse the same model
    tenant_id: UUID,
    auth_service: AuthService = Depends(get_auth_service)
):
    """Resend email verification"""
    try:
        await auth_service.resend_verification_email(
            email=email_data.email,
            tenant_id=tenant_id
        )
        
        return SuccessResponse(
            message="If the email exists and is unverified, a verification link has been sent"
        )
        
    except Exception as e:
        # Don't reveal if email exists
        return SuccessResponse(
            message="If the email exists and is unverified, a verification link has been sent"
        )


@router.get("/me")
async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    auth_service: AuthService = Depends(get_auth_service)
):
    """Get current user information"""
    try:
        user = await auth_service.verify_token(credentials.credentials)
        
        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid or expired token"
            )
        
        return {
            "success": True,
            "data": {
                "user_id": user.user_id,
                "email": user.email,
                "first_name": user.first_name,
                "last_name": user.last_name,
                "is_verified": user.is_verified,
                "is_active": user.is_active,
                "avatar_url": user.avatar_url,
                "timezone": user.timezone,
                "language": user.language,
                "last_login_at": user.last_login_at
            }
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Authentication failed"
        )
