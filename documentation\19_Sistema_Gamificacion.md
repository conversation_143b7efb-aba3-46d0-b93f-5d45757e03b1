# Sistema de Gamificación y Leaderboard

## Descripción General

El sistema de gamificación incentiva la participación y el aprendizaje mediante un sistema de puntos dual para estudiantes y creadores de contenido, con leaderboards competitivos semanales, mensuales y totales.

## Arquitectura del Sistema de Puntos

### 1. Tipos de Puntuación

#### 1.1 Puntos de Estudiante 🎓
Recompensas por actividades de aprendizaje y participación en la comunidad.

#### 1.2 Puntos de Creador 👨‍🏫
Recompensas por crear contenido de calidad y contribuir al ecosistema educativo.

### 2. Fórmulas de Cálculo

#### 2.1 Puntos Base por Completar Cursos

```javascript
function calculateCourseCompletionPoints(course) {
  const basePoints = {
    'beginner': 75,
    'intermediate': 150, 
    'advanced': 250
  };
  
  let points = basePoints[course.difficulty_level] || 150;
  
  // Bonus por Career Path activo
  if (course.isPartOfActiveCareerPath) {
    points *= 1.5; // +50% bonus
  }
  
  // Bonus por validación de experto
  if (course.hasExpertValidation) {
    points *= 1.25; // +25% bonus
  }
  
  return Math.round(points);
}
```

#### 2.2 Puntos por Crear Cursos

```javascript
function calculateCourseCreationPoints(course, metrics) {
  let basePoints = 200; // Puntos base por crear curso
  
  // Puntos por likes recibidos
  const likePoints = metrics.likes * 10;
  
  // Puntos por completaciones de estudiantes
  const completionPoints = metrics.completions * 25;
  
  // Bonus por rating alto
  let ratingMultiplier = 1.0;
  if (metrics.averageRating >= 4.5) {
    ratingMultiplier = 1.5; // +50% bonus
  } else if (metrics.averageRating >= 4.0) {
    ratingMultiplier = 1.25; // +25% bonus
  }
  
  // Bonus por validación de experto
  let expertBonus = 0;
  if (course.hasPositiveExpertReview) {
    expertBonus = 100;
    ratingMultiplier *= 1.25; // +25% adicional
  }
  
  const totalPoints = Math.round(
    (basePoints + likePoints + completionPoints + expertBonus) * ratingMultiplier
  );
  
  return totalPoints;
}
```

#### 2.3 Puntos por Actividad en Foros

```javascript
function calculateForumPoints(action, context) {
  const points = {
    'create_post': 10,
    'receive_like': 5,
    'helpful_answer': 15, // marcado como útil por el autor
    'expert_highlight': 25 // mensaje destacado por experto
  };
  
  let basePoints = points[action] || 0;
  
  // Bonus por participación en curso de Career Path
  if (context.isCareerPathCourse) {
    basePoints *= 1.3; // +30% bonus
  }
  
  return basePoints;
}
```

### 3. Sistema de Períodos

#### 3.1 Períodos de Competencia
- **Semanal**: Lunes 00:00 UTC a Domingo 23:59 UTC
- **Mensual**: Primer día del mes 00:00 UTC al último día 23:59 UTC  
- **Total**: Acumulativo desde el registro del usuario

#### 3.2 Algoritmo de Ranking

```javascript
function calculateRankings(scoreType, periodType, periodStart) {
  // 1. Obtener todos los usuarios con puntos en el período
  const users = getUserScoresForPeriod(scoreType, periodType, periodStart);
  
  // 2. Ordenar por puntos (descendente) y por fecha de último punto (ascendente para desempate)
  users.sort((a, b) => {
    if (a.total_points !== b.total_points) {
      return b.total_points - a.total_points;
    }
    return new Date(a.last_score_date) - new Date(b.last_score_date);
  });
  
  // 3. Asignar posiciones
  users.forEach((user, index) => {
    user.position_rank = index + 1;
    user.percentile = ((users.length - index) / users.length) * 100;
  });
  
  return users;
}
```

### 4. Validación por Expertos

#### 4.1 Tipos de Validación

| Tipo | Descripción | Bonus para Creador | Criterios |
|------|-------------|-------------------|-----------|
| **Difficulty Assessment** | Validación de nivel de dificultad | +25% en puntos base | Experto confirma dificultad declarada |
| **Quality Review** | Review completo de calidad | +100 pts + 25% multiplicador | Rating 4+ en calidad y utilidad |
| **Utility Validation** | Validación de utilidad práctica | +50 pts | Confirmación de aplicabilidad real |
| **Content Accuracy** | Verificación de precisión técnica | +75 pts | Validación de exactitud del contenido |

#### 4.2 Proceso de Validación

```mermaid
flowchart TD
    A[Curso Creado] --> B[Disponible para Validación]
    B --> C[Experto Solicita Validación]
    C --> D[Review del Contenido]
    D --> E{¿Aprobado?}
    E -->|Sí| F[Puntos Bonus al Creador]
    E -->|No| G[Feedback para Mejora]
    F --> H[Badge de Validación]
    G --> I[Oportunidad de Revisión]
    I --> D
```

### 5. Leaderboard y Competencias

#### 5.1 Estructura del Leaderboard

```yaml
leaderboard_structure:
  categories:
    - student_leaderboard:
        metrics:
          - courses_completed
          - forum_participation
          - career_path_progress
          - community_contributions
    - creator_leaderboard:
        metrics:
          - courses_created
          - student_enrollments
          - average_ratings
          - expert_validations

  periods:
    - weekly: "Competencia semanal con reset cada lunes"
    - monthly: "Competencia mensual con reset cada 1ro"
    - alltime: "Ranking histórico acumulativo"

  display_elements:
    - top_3_podium: "Destacado visual para primeros 3 lugares"
    - user_position: "Posición actual del usuario logueado"
    - rank_changes: "Indicadores de subida/bajada de posición"
    - point_breakdown: "Desglose de fuentes de puntos"
```

#### 5.2 Algoritmo de Cache del Leaderboard

```javascript
class LeaderboardCache {
  async generateLeaderboard(scoreType, periodType, periodStart) {
    // 1. Calcular puntos para todos los usuarios
    const userScores = await this.calculateUserScores(scoreType, periodType, periodStart);
    
    // 2. Generar rankings
    const rankings = this.calculateRankings(userScores);
    
    // 3. Calcular cambios de posición
    const previousPeriod = this.getPreviousPeriod(periodType, periodStart);
    const rankChanges = await this.calculateRankChanges(rankings, previousPeriod);
    
    // 4. Guardar en cache
    await this.saveToCache(rankings, rankChanges);
    
    // 5. Programar próxima regeneración
    this.scheduleNextRegeneration(periodType);
    
    return rankings;
  }
  
  calculateRankChanges(currentRankings, previousRankings) {
    return currentRankings.map(current => {
      const previous = previousRankings.find(p => p.user_id === current.user_id);
      const rankChange = previous ? previous.position_rank - current.position_rank : null;
      
      return {
        ...current,
        previous_rank: previous?.position_rank,
        rank_change: rankChange
      };
    });
  }
}
```

### 6. Métricas y Analytics

#### 6.1 Métricas de Engagement

```yaml
engagement_metrics:
  user_retention:
    - daily_active_users_with_points
    - weekly_leaderboard_viewers
    - monthly_position_climbers
    
  competition_health:
    - points_distribution_curve
    - new_users_entering_leaderboard
    - top_performers_retention
    
  content_quality:
    - expert_validation_rate
    - average_course_ratings
    - correlation_points_vs_quality
```

#### 6.2 Detección de Anomalías

```javascript
class AnomalyDetection {
  detectSuspiciousActivity(user, scoreTransactions) {
    const flags = [];
    
    // Detección de farming de puntos
    const recentTransactions = this.getRecentTransactions(scoreTransactions, 24); // últimas 24h
    if (recentTransactions.length > 50) {
      flags.push('EXCESSIVE_ACTIVITY');
    }
    
    // Detección de patrones no naturales
    const timePattern = this.analyzeTimePattern(recentTransactions);
    if (timePattern.isRobotic) {
      flags.push('ROBOTIC_PATTERN');
    }
    
    // Validación de completaciones de curso
    const courseCompletions = recentTransactions.filter(t => t.action_type === 'course_completion');
    for (const completion of courseCompletions) {
      const courseTime = this.getCourseCompletionTime(completion.reference_id, user.user_id);
      if (courseTime < this.getMinimumReasonableTime(completion.reference_id)) {
        flags.push('SUSPICIOUS_COMPLETION_TIME');
      }
    }
    
    return flags;
  }
}
```

### 7. Configuración y Balanceado

#### 7.1 Parámetros Configurables

```yaml
scoring_config:
  base_points:
    course_completion:
      beginner: 75
      intermediate: 150
      advanced: 250
    course_creation: 200
    forum_post: 10
    forum_like_received: 5
    course_rating: 15
    
  multipliers:
    career_path_bonus: 1.5
    expert_validation: 1.25
    high_rating_bonus: 1.5  # rating >= 4.5
    
  expert_bonuses:
    quality_review: 100
    difficulty_validation: 50
    utility_confirmation: 75
    
  limits:
    max_daily_forum_points: 200
    max_weekly_creation_points: 2000
    min_course_duration_minutes: 30
```

#### 7.2 Algoritmo de Balanceado

```javascript
class PointBalancer {
  async analyzeAndAdjust() {
    // 1. Analizar distribución actual de puntos
    const distribution = await this.analyzePointDistribution();
    
    // 2. Identificar desbalances
    const imbalances = this.detectImbalances(distribution);
    
    // 3. Sugerir ajustes
    const adjustments = this.calculateAdjustments(imbalances);
    
    // 4. Aplicar ajustes graduales
    await this.applyGradualAdjustments(adjustments);
    
    return adjustments;
  }
  
  detectImbalances(distribution) {
    const imbalances = [];
    
    // Verificar si alguna actividad domina excesivamente
    if (distribution.forum_points_percentage > 60) {
      imbalances.push({
        type: 'FORUM_DOMINANCE',
        severity: 'HIGH',
        recommendation: 'Reducir puntos por actividad en foros'
      });
    }
    
    // Verificar participación de creadores vs estudiantes
    const creatorStudentRatio = distribution.creator_points / distribution.student_points;
    if (creatorStudentRatio < 0.3) {
      imbalances.push({
        type: 'LOW_CREATOR_PARTICIPATION',
        severity: 'MEDIUM',
        recommendation: 'Incrementar incentivos para creadores'
      });
    }
    
    return imbalances;
  }
}
```

### 8. Integración con Otros Sistemas

#### 8.1 Integración con Career Paths

```javascript
// Bonus automático por completar cursos en Career Path activo
function handleCourseCompletion(userId, courseId) {
  const activeCareerPaths = getUserActiveCareerPaths(userId);
  const isCareerPathCourse = activeCareerPaths.some(path => 
    path.courses.includes(courseId)
  );
  
  const basePoints = calculateCourseCompletionPoints(course);
  const finalPoints = isCareerPathCourse ? basePoints * 1.5 : basePoints;
  
  awardPoints(userId, 'student', 'course_completion', finalPoints, {
    course_id: courseId,
    career_path_bonus: isCareerPathCourse,
    base_points: basePoints
  });
}
```

#### 8.2 Integración con Sistema de Expertos

```javascript
// Bonus automático por validación de expertos
function handleExpertValidation(validationId, result) {
  if (result.validation_result === 'approved') {
    const course = getCourse(result.target_id);
    const creator = course.created_by;
    
    let bonusPoints = 0;
    let multiplier = 1.0;
    
    switch (result.validation_type) {
      case 'quality_review':
        bonusPoints = 100;
        multiplier = 1.25;
        break;
      case 'difficulty_assessment':
        multiplier = 1.25;
        break;
      case 'utility_validation':
        bonusPoints = 50;
        break;
    }
    
    // Aplicar bonus y recalcular puntos del curso
    if (bonusPoints > 0) {
      awardPoints(creator, 'creator', 'expert_validation', bonusPoints, {
        validation_id: validationId,
        validation_type: result.validation_type
      });
    }
    
    // Recalcular puntos del curso con nuevo multiplicador
    recalculateCoursePoints(course.course_id, multiplier);
  }
}
```

### 9. Roadmap y Futuras Mejoras

#### 9.1 Funcionalidades Planificadas
- **Badges y Achievements**: Sistema de logros por hitos específicos
- **Seasonal Competitions**: Competencias temáticas trimestrales
- **Team Challenges**: Competencias por grupos o organizaciones
- **Skill-based Leaderboards**: Rankings específicos por área de conocimiento
- **Mentorship Points**: Puntos por mentoría y ayuda a otros usuarios

#### 9.2 Optimizaciones Técnicas
- **Real-time Updates**: WebSocket para actualizaciones en tiempo real
- **Predictive Analytics**: ML para predecir y prevenir gaming del sistema
- **Advanced Caching**: Redis para cache distribuido de leaderboards
- **API Rate Limiting**: Prevención de abuso del sistema de puntos
- **Audit Dashboard**: Panel administrativo para monitoreo y ajustes
