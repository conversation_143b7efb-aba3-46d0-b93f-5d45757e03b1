import React from 'react';
import { BarChart3, TrendingUp, Users, Clock, BookOpen, Star } from 'lucide-react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';

const stats = [
  {
    name: 'Tiempo Total de Estudio',
    value: '156h',
    change: '+12%',
    changeType: 'positive',
    icon: Clock,
  },
  {
    name: '<PERSON><PERSON><PERSON>',
    value: '8',
    change: '+2',
    changeType: 'positive',
    icon: BookOpen,
  },
  {
    name: 'Promedio de Calificación',
    value: '4.6',
    change: '+0.2',
    changeType: 'positive',
    icon: Star,
  },
  {
    name: 'Racha Actual',
    value: '15 días',
    change: '+3',
    changeType: 'positive',
    icon: TrendingUp,
  },
];

const monthlyProgress = [
  { month: 'Ene', hours: 12, courses: 1 },
  { month: 'Feb', hours: 18, courses: 1 },
  { month: 'Mar', hours: 24, courses: 2 },
  { month: 'Abr', hours: 32, courses: 2 },
  { month: 'May', hours: 28, courses: 1 },
  { month: 'Jun', hours: 42, courses: 1 },
];

const categoryProgress = [
  { category: 'Tecnología', completed: 5, inProgress: 2, percentage: 71 },
  { category: 'Idiomas', completed: 2, inProgress: 1, percentage: 67 },
  { category: 'Negocios', completed: 1, inProgress: 0, percentage: 100 },
];

export default function AnalyticsPage() {
  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900">Analíticas</h1>
        <p className="text-gray-600 mt-2">
          Visualiza tu progreso de aprendizaje y estadísticas
        </p>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {stats.map((stat) => {
          const Icon = stat.icon;
          return (
            <Card key={stat.name}>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-500 truncate">
                      {stat.name}
                    </p>
                    <p className="text-2xl font-semibold text-gray-900">
                      {stat.value}
                    </p>
                    <p className={`text-sm ${
                      stat.changeType === 'positive' ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {stat.change} este mes
                    </p>
                  </div>
                  <div className="flex-shrink-0">
                    <Icon className="h-8 w-8 text-gray-400" />
                  </div>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Monthly Progress Chart */}
        <Card>
          <CardHeader>
            <CardTitle>Progreso Mensual</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {monthlyProgress.map((month) => (
                <div key={month.month} className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className="w-12 text-sm font-medium text-gray-600">
                      {month.month}
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 text-sm text-gray-600">
                        <Clock className="w-4 h-4" />
                        <span>{month.hours}h</span>
                        <BookOpen className="w-4 h-4 ml-4" />
                        <span>{month.courses} cursos</span>
                      </div>
                    </div>
                  </div>
                  <div className="w-24 bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-blue-600 h-2 rounded-full"
                      style={{ width: `${(month.hours / 50) * 100}%` }}
                    />
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Category Progress */}
        <Card>
          <CardHeader>
            <CardTitle>Progreso por Categoría</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              {categoryProgress.map((category) => (
                <div key={category.category}>
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="text-sm font-medium text-gray-900">
                      {category.category}
                    </h4>
                    <span className="text-sm text-gray-500">
                      {category.percentage}%
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2 mb-2">
                    <div
                      className="bg-green-600 h-2 rounded-full"
                      style={{ width: `${category.percentage}%` }}
                    />
                  </div>
                  <div className="flex items-center justify-between text-xs text-gray-500">
                    <span>{category.completed} completados</span>
                    <span>{category.inProgress} en progreso</span>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Learning Streak */}
        <Card>
          <CardHeader>
            <CardTitle>Racha de Aprendizaje</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-center py-6">
              <div className="w-24 h-24 mx-auto mb-4 bg-orange-100 rounded-full flex items-center justify-center">
                <TrendingUp className="w-12 h-12 text-orange-600" />
              </div>
              <h3 className="text-3xl font-bold text-gray-900 mb-2">15 días</h3>
              <p className="text-gray-600 mb-4">¡Racha actual!</p>
              <div className="grid grid-cols-7 gap-1 max-w-xs mx-auto">
                {Array.from({ length: 21 }, (_, i) => (
                  <div
                    key={i}
                    className={`w-6 h-6 rounded ${
                      i < 15 ? 'bg-orange-500' : 'bg-gray-200'
                    }`}
                  />
                ))}
              </div>
              <p className="text-xs text-gray-500 mt-2">Últimas 3 semanas</p>
            </div>
          </CardContent>
        </Card>

        {/* Achievements */}
        <Card>
          <CardHeader>
            <CardTitle>Logros Recientes</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-yellow-100 rounded-full flex items-center justify-center">
                  <Star className="w-5 h-5 text-yellow-600" />
                </div>
                <div>
                  <h4 className="text-sm font-medium text-gray-900">
                    Primer Curso Completado
                  </h4>
                  <p className="text-xs text-gray-500">Hace 2 semanas</p>
                </div>
              </div>
              
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                  <BookOpen className="w-5 h-5 text-blue-600" />
                </div>
                <div>
                  <h4 className="text-sm font-medium text-gray-900">
                    5 Cursos Completados
                  </h4>
                  <p className="text-xs text-gray-500">Hace 1 semana</p>
                </div>
              </div>
              
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                  <TrendingUp className="w-5 h-5 text-green-600" />
                </div>
                <div>
                  <h4 className="text-sm font-medium text-gray-900">
                    Racha de 10 Días
                  </h4>
                  <p className="text-xs text-gray-500">Hace 5 días</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Analytics */}
      <Card className="mt-8">
        <CardHeader>
          <CardTitle>Resumen Detallado</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <h4 className="text-lg font-semibold text-gray-900 mb-2">
                Tiempo Promedio por Sesión
              </h4>
              <p className="text-3xl font-bold text-blue-600">2.3h</p>
              <p className="text-sm text-gray-500">+15 min vs mes anterior</p>
            </div>
            
            <div className="text-center">
              <h4 className="text-lg font-semibold text-gray-900 mb-2">
                Días Activos este Mes
              </h4>
              <p className="text-3xl font-bold text-green-600">18</p>
              <p className="text-sm text-gray-500">de 30 días</p>
            </div>
            
            <div className="text-center">
              <h4 className="text-lg font-semibold text-gray-900 mb-2">
                Tasa de Finalización
              </h4>
              <p className="text-3xl font-bold text-purple-600">89%</p>
              <p className="text-sm text-gray-500">8 de 9 cursos iniciados</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
