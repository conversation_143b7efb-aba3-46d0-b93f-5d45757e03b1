# Prometheus Alert Rules for Arroyo University

groups:
  - name: arroyo.rules
    rules:
      # High Error Rate
      - alert: HighErrorRate
        expr: rate(http_requests_total{status=~"5.."}[5m]) > 0.1
        for: 5m
        labels:
          severity: critical
          service: "{{ $labels.job }}"
        annotations:
          summary: "High error rate detected on {{ $labels.job }}"
          description: "Error rate is {{ $value | humanizePercentage }} for service {{ $labels.job }}"

      # High Response Time
      - alert: HighResponseTime
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 2
        for: 5m
        labels:
          severity: warning
          service: "{{ $labels.job }}"
        annotations:
          summary: "High response time on {{ $labels.job }}"
          description: "95th percentile response time is {{ $value }}s for service {{ $labels.job }}"

      # Database Connection High
      - alert: DatabaseConnectionHigh
        expr: pg_stat_activity_count > 80
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "Database connection count is high"
          description: "PostgreSQL has {{ $value }} active connections"

      # Redis Memory Usage High
      - alert: RedisMemoryHigh
        expr: redis_memory_used_bytes / redis_memory_max_bytes > 0.9
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Redis memory usage is high"
          description: "Redis memory usage is {{ $value | humanizePercentage }}"

      # Service Down
      - alert: ServiceDown
        expr: up == 0
        for: 1m
        labels:
          severity: critical
          service: "{{ $labels.job }}"
        annotations:
          summary: "Service {{ $labels.job }} is down"
          description: "Service {{ $labels.job }} has been down for more than 1 minute"

      # High CPU Usage
      - alert: HighCPUUsage
        expr: 100 - (avg by(instance) (irate(node_cpu_seconds_total{mode="idle"}[5m])) * 100) > 80
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High CPU usage on {{ $labels.instance }}"
          description: "CPU usage is {{ $value }}% on {{ $labels.instance }}"

      # High Memory Usage
      - alert: HighMemoryUsage
        expr: (1 - (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes)) * 100 > 85
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High memory usage on {{ $labels.instance }}"
          description: "Memory usage is {{ $value }}% on {{ $labels.instance }}"

      # Disk Space Low
      - alert: DiskSpaceLow
        expr: (1 - (node_filesystem_avail_bytes / node_filesystem_size_bytes)) * 100 > 85
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Low disk space on {{ $labels.instance }}"
          description: "Disk usage is {{ $value }}% on {{ $labels.instance }} ({{ $labels.mountpoint }})"

      # AI Service Queue Length High
      - alert: AIQueueLengthHigh
        expr: celery_queue_length{queue="generation"} > 100
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "AI generation queue is backing up"
          description: "AI generation queue has {{ $value }} pending tasks"

      # Failed AI Tasks
      - alert: FailedAITasks
        expr: rate(celery_task_failed_total[5m]) > 0.1
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High rate of failed AI tasks"
          description: "AI task failure rate is {{ $value | humanizePercentage }}"

      # Email Delivery Failures
      - alert: EmailDeliveryFailures
        expr: rate(email_delivery_failed_total[5m]) > 0.1
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High rate of email delivery failures"
          description: "Email delivery failure rate is {{ $value | humanizePercentage }}"

  - name: business.rules
    rules:
      # Low User Activity
      - alert: LowUserActivity
        expr: rate(user_login_total[1h]) < 10
        for: 30m
        labels:
          severity: info
        annotations:
          summary: "Low user activity detected"
          description: "User login rate is {{ $value }} per hour"

      # High Exam Failure Rate
      - alert: HighExamFailureRate
        expr: rate(exam_failed_total[1h]) / rate(exam_completed_total[1h]) > 0.5
        for: 30m
        labels:
          severity: info
        annotations:
          summary: "High exam failure rate"
          description: "Exam failure rate is {{ $value | humanizePercentage }}"
