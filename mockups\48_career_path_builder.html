<!DOCTYPE html>
<html lang="es" class="h-full bg-gray-50">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Career Path Builder - Arroyo University</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Inter', sans-serif; }
        .canvas-container {
            position: relative;
            background-image: radial-gradient(circle, #e5e7eb 1px, transparent 1px);
            background-size: 20px 20px;
        }
        .skill-node {
            position: absolute;
            cursor: move;
            user-select: none;
        }
        .skill-node.selected {
            box-shadow: 0 0 0 3px #3b82f6;
        }
        .connection-line {
            position: absolute;
            pointer-events: none;
            z-index: 1;
        }
        .skill-node {
            z-index: 2;
        }
    </style>
</head>
<body class="h-full">
    <div class="flex h-full">
        <!-- Sidebar -->
        <div class="hidden md:flex md:w-80 md:flex-col">
            <div class="flex flex-col flex-grow pt-5 bg-white border-r border-gray-200 overflow-y-auto">
                <!-- Header -->
                <div class="flex items-center justify-between px-4 mb-4">
                    <h2 class="text-lg font-semibold text-gray-900">Skills Repository</h2>
                    <button onclick="window.location.href='47_career_path_marketplace.html'" 
                            class="text-gray-400 hover:text-gray-600">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                        </svg>
                    </button>
                </div>

                <!-- Search Skills -->
                <div class="px-4 mb-4">
                    <div class="relative">
                        <input type="text" id="skillSearch" placeholder="Buscar skills..." 
                               class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                               onkeyup="filterSkills()">
                        <svg class="absolute left-3 top-2.5 h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                        </svg>
                    </div>
                </div>

                <!-- Filter by Area -->
                <div class="px-4 mb-4">
                    <select id="areaFilter" onchange="filterSkills()" 
                            class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">Todas las áreas</option>
                        <option value="programming">Programación</option>
                        <option value="devops">DevOps</option>
                        <option value="data">Data Science</option>
                        <option value="security">Ciberseguridad</option>
                        <option value="management">Gestión</option>
                        <option value="design">Diseño</option>
                        <option value="marketing">Marketing</option>
                    </select>
                </div>

                <!-- Skills List -->
                <div class="flex-1 px-4">
                    <div id="skillsList" class="space-y-2">
                        <!-- Programming Skills -->
                        <div class="skill-category" data-area="programming">
                            <h3 class="text-sm font-medium text-gray-700 mb-2">Programación</h3>
                            <div class="space-y-1">
                                <div class="skill-item p-2 bg-blue-50 border border-blue-200 rounded cursor-pointer hover:bg-blue-100" 
                                     draggable="true" ondragstart="dragStart(event)" 
                                     data-skill='{"id":"javascript","name":"JavaScript","area":"programming","color":"bg-yellow-500"}'>
                                    <div class="flex items-center">
                                        <div class="w-3 h-3 bg-yellow-500 rounded mr-2"></div>
                                        <span class="text-sm">JavaScript</span>
                                    </div>
                                </div>
                                <div class="skill-item p-2 bg-blue-50 border border-blue-200 rounded cursor-pointer hover:bg-blue-100" 
                                     draggable="true" ondragstart="dragStart(event)" 
                                     data-skill='{"id":"python","name":"Python","area":"programming","color":"bg-green-500"}'>
                                    <div class="flex items-center">
                                        <div class="w-3 h-3 bg-green-500 rounded mr-2"></div>
                                        <span class="text-sm">Python</span>
                                    </div>
                                </div>
                                <div class="skill-item p-2 bg-blue-50 border border-blue-200 rounded cursor-pointer hover:bg-blue-100" 
                                     draggable="true" ondragstart="dragStart(event)" 
                                     data-skill='{"id":"react","name":"React","area":"programming","color":"bg-cyan-500"}'>
                                    <div class="flex items-center">
                                        <div class="w-3 h-3 bg-cyan-500 rounded mr-2"></div>
                                        <span class="text-sm">React</span>
                                    </div>
                                </div>
                                <div class="skill-item p-2 bg-blue-50 border border-blue-200 rounded cursor-pointer hover:bg-blue-100" 
                                     draggable="true" ondragstart="dragStart(event)" 
                                     data-skill='{"id":"nodejs","name":"Node.js","area":"programming","color":"bg-green-600"}'>
                                    <div class="flex items-center">
                                        <div class="w-3 h-3 bg-green-600 rounded mr-2"></div>
                                        <span class="text-sm">Node.js</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- DevOps Skills -->
                        <div class="skill-category" data-area="devops">
                            <h3 class="text-sm font-medium text-gray-700 mb-2">DevOps</h3>
                            <div class="space-y-1">
                                <div class="skill-item p-2 bg-blue-50 border border-blue-200 rounded cursor-pointer hover:bg-blue-100" 
                                     draggable="true" ondragstart="dragStart(event)" 
                                     data-skill='{"id":"docker","name":"Docker","area":"devops","color":"bg-blue-600"}'>
                                    <div class="flex items-center">
                                        <div class="w-3 h-3 bg-blue-600 rounded mr-2"></div>
                                        <span class="text-sm">Docker</span>
                                    </div>
                                </div>
                                <div class="skill-item p-2 bg-blue-50 border border-blue-200 rounded cursor-pointer hover:bg-blue-100" 
                                     draggable="true" ondragstart="dragStart(event)" 
                                     data-skill='{"id":"kubernetes","name":"Kubernetes","area":"devops","color":"bg-indigo-600"}'>
                                    <div class="flex items-center">
                                        <div class="w-3 h-3 bg-indigo-600 rounded mr-2"></div>
                                        <span class="text-sm">Kubernetes</span>
                                    </div>
                                </div>
                                <div class="skill-item p-2 bg-blue-50 border border-blue-200 rounded cursor-pointer hover:bg-blue-100" 
                                     draggable="true" ondragstart="dragStart(event)" 
                                     data-skill='{"id":"jenkins","name":"Jenkins","area":"devops","color":"bg-red-500"}'>
                                    <div class="flex items-center">
                                        <div class="w-3 h-3 bg-red-500 rounded mr-2"></div>
                                        <span class="text-sm">Jenkins</span>
                                    </div>
                                </div>
                                <div class="skill-item p-2 bg-blue-50 border border-blue-200 rounded cursor-pointer hover:bg-blue-100" 
                                     draggable="true" ondragstart="dragStart(event)" 
                                     data-skill='{"id":"terraform","name":"Terraform","area":"devops","color":"bg-purple-600"}'>
                                    <div class="flex items-center">
                                        <div class="w-3 h-3 bg-purple-600 rounded mr-2"></div>
                                        <span class="text-sm">Terraform</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Data Science Skills -->
                        <div class="skill-category" data-area="data">
                            <h3 class="text-sm font-medium text-gray-700 mb-2">Data Science</h3>
                            <div class="space-y-1">
                                <div class="skill-item p-2 bg-blue-50 border border-blue-200 rounded cursor-pointer hover:bg-blue-100" 
                                     draggable="true" ondragstart="dragStart(event)" 
                                     data-skill='{"id":"pandas","name":"Pandas","area":"data","color":"bg-orange-500"}'>
                                    <div class="flex items-center">
                                        <div class="w-3 h-3 bg-orange-500 rounded mr-2"></div>
                                        <span class="text-sm">Pandas</span>
                                    </div>
                                </div>
                                <div class="skill-item p-2 bg-blue-50 border border-blue-200 rounded cursor-pointer hover:bg-blue-100" 
                                     draggable="true" ondragstart="dragStart(event)" 
                                     data-skill='{"id":"tensorflow","name":"TensorFlow","area":"data","color":"bg-orange-600"}'>
                                    <div class="flex items-center">
                                        <div class="w-3 h-3 bg-orange-600 rounded mr-2"></div>
                                        <span class="text-sm">TensorFlow</span>
                                    </div>
                                </div>
                                <div class="skill-item p-2 bg-blue-50 border border-blue-200 rounded cursor-pointer hover:bg-blue-100" 
                                     draggable="true" ondragstart="dragStart(event)" 
                                     data-skill='{"id":"sql","name":"SQL","area":"data","color":"bg-gray-600"}'>
                                    <div class="flex items-center">
                                        <div class="w-3 h-3 bg-gray-600 rounded mr-2"></div>
                                        <span class="text-sm">SQL</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Management Skills -->
                        <div class="skill-category" data-area="management">
                            <h3 class="text-sm font-medium text-gray-700 mb-2">Gestión</h3>
                            <div class="space-y-1">
                                <div class="skill-item p-2 bg-blue-50 border border-blue-200 rounded cursor-pointer hover:bg-blue-100" 
                                     draggable="true" ondragstart="dragStart(event)" 
                                     data-skill='{"id":"scrum","name":"Scrum","area":"management","color":"bg-green-700"}'>
                                    <div class="flex items-center">
                                        <div class="w-3 h-3 bg-green-700 rounded mr-2"></div>
                                        <span class="text-sm">Scrum</span>
                                    </div>
                                </div>
                                <div class="skill-item p-2 bg-blue-50 border border-blue-200 rounded cursor-pointer hover:bg-blue-100" 
                                     draggable="true" ondragstart="dragStart(event)" 
                                     data-skill='{"id":"leadership","name":"Leadership","area":"management","color":"bg-purple-700"}'>
                                    <div class="flex items-center">
                                        <div class="w-3 h-3 bg-purple-700 rounded mr-2"></div>
                                        <span class="text-sm">Leadership</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="flex flex-col flex-1 overflow-hidden">
            <!-- Top Header -->
            <header class="bg-white shadow-sm border-b border-gray-200">
                <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div class="flex justify-between items-center py-4">
                        <div>
                            <h1 class="text-2xl font-bold text-gray-900">Career Path Builder</h1>
                            <p class="text-gray-600">Crea tu ruta de carrera personalizada conectando skills</p>
                        </div>
                        <div class="flex items-center space-x-4">
                            <button onclick="clearCanvas()" 
                                    class="border border-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-50 font-medium">
                                Limpiar
                            </button>
                            <button onclick="saveCareerPath()" 
                                    class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 font-medium">
                                Guardar Career Path
                            </button>
                        </div>
                    </div>
                </div>
            </header>

            <!-- Canvas Area -->
            <main class="flex-1 overflow-hidden">
                <div class="h-full relative">
                    <!-- Canvas -->
                    <div id="canvas" class="canvas-container w-full h-full" 
                         ondrop="drop(event)" ondragover="allowDrop(event)" onclick="deselectAll()">
                        <!-- SVG for connections -->
                        <svg id="connectionsSvg" class="absolute inset-0 w-full h-full pointer-events-none" style="z-index: 1;">
                        </svg>
                        
                        <!-- Skills will be added here dynamically -->
                    </div>

                    <!-- Floating Toolbar -->
                    <div class="absolute top-4 right-4 bg-white rounded-lg shadow-lg border border-gray-200 p-4">
                        <div class="flex items-center space-x-2 mb-3">
                            <h3 class="text-sm font-medium text-gray-900">Herramientas</h3>
                        </div>
                        <div class="space-y-2">
                            <button id="connectMode" onclick="toggleConnectMode()" 
                                    class="w-full text-left px-3 py-2 text-sm border border-gray-300 rounded hover:bg-gray-50">
                                🔗 Modo Conexión
                            </button>
                            <button onclick="deleteSelected()" 
                                    class="w-full text-left px-3 py-2 text-sm border border-gray-300 rounded hover:bg-gray-50 text-red-600">
                                🗑️ Eliminar Seleccionado
                            </button>
                        </div>
                    </div>

                    <!-- Career Path Info Panel -->
                    <div class="absolute bottom-4 left-4 bg-white rounded-lg shadow-lg border border-gray-200 p-4 w-80">
                        <h3 class="text-sm font-medium text-gray-900 mb-3">Información del Career Path</h3>
                        <div class="space-y-2">
                            <div>
                                <label class="block text-xs font-medium text-gray-700">Nombre</label>
                                <input type="text" id="pathName" placeholder="Mi Career Path Personalizado" 
                                       class="w-full text-sm border border-gray-300 rounded px-2 py-1 focus:outline-none focus:ring-1 focus:ring-blue-500">
                            </div>
                            <div>
                                <label class="block text-xs font-medium text-gray-700">Descripción</label>
                                <textarea id="pathDescription" rows="2" placeholder="Describe tu ruta de carrera..." 
                                          class="w-full text-sm border border-gray-300 rounded px-2 py-1 focus:outline-none focus:ring-1 focus:ring-blue-500"></textarea>
                            </div>
                            <div class="text-xs text-gray-500">
                                <span id="skillCount">0</span> skills agregadas
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script>
        let isConnectMode = false;
        let selectedNode = null;
        let connections = [];
        let nodeCounter = 0;
        let draggedSkill = null;

        // Drag and Drop Functions
        function dragStart(event) {
            draggedSkill = JSON.parse(event.target.getAttribute('data-skill'));
        }

        function allowDrop(event) {
            event.preventDefault();
        }

        function drop(event) {
            event.preventDefault();
            if (!draggedSkill) return;

            const canvas = document.getElementById('canvas');
            const rect = canvas.getBoundingClientRect();
            const x = event.clientX - rect.left;
            const y = event.clientY - rect.top;

            createSkillNode(draggedSkill, x, y);
            draggedSkill = null;
            updateSkillCount();
        }

        // Create Skill Node
        function createSkillNode(skill, x, y) {
            const node = document.createElement('div');
            node.className = 'skill-node bg-white border-2 border-gray-300 rounded-lg p-3 shadow-sm hover:shadow-md transition-shadow';
            node.style.left = x + 'px';
            node.style.top = y + 'px';
            node.style.width = '120px';
            node.id = 'node_' + (++nodeCounter);

            node.innerHTML = `
                <div class="flex items-center">
                    <div class="w-3 h-3 ${skill.color} rounded mr-2"></div>
                    <span class="text-sm font-medium text-gray-900">${skill.name}</span>
                </div>
                <div class="text-xs text-gray-500 mt-1">${skill.area}</div>
            `;

            // Make draggable
            node.draggable = true;
            node.ondragstart = function(e) {
                e.dataTransfer.setData('text/plain', '');
                this.style.opacity = '0.5';
            };
            node.ondragend = function(e) {
                this.style.opacity = '1';
                updateConnections();
            };

            // Click handler
            node.onclick = function(e) {
                e.stopPropagation();
                if (isConnectMode) {
                    handleConnectionClick(this);
                } else {
                    selectNode(this);
                }
            };

            // Store skill data
            node.skillData = skill;

            document.getElementById('canvas').appendChild(node);
        }

        // Node Selection
        function selectNode(node) {
            deselectAll();
            node.classList.add('selected');
            selectedNode = node;
        }

        function deselectAll() {
            document.querySelectorAll('.skill-node').forEach(node => {
                node.classList.remove('selected');
            });
            selectedNode = null;
        }

        // Connection Mode
        function toggleConnectMode() {
            isConnectMode = !isConnectMode;
            const button = document.getElementById('connectMode');
            if (isConnectMode) {
                button.classList.add('bg-blue-100', 'text-blue-700');
                button.textContent = '🔗 Modo Conexión (Activo)';
            } else {
                button.classList.remove('bg-blue-100', 'text-blue-700');
                button.textContent = '🔗 Modo Conexión';
            }
            deselectAll();
        }

        let firstConnectionNode = null;

        function handleConnectionClick(node) {
            if (!firstConnectionNode) {
                firstConnectionNode = node;
                node.classList.add('selected');
            } else if (firstConnectionNode === node) {
                // Clicked same node, deselect
                firstConnectionNode.classList.remove('selected');
                firstConnectionNode = null;
            } else {
                // Create connection
                createConnection(firstConnectionNode, node);
                firstConnectionNode.classList.remove('selected');
                firstConnectionNode = null;
            }
        }

        // Create Connection
        function createConnection(fromNode, toNode) {
            const connection = {
                id: 'conn_' + Date.now(),
                from: fromNode.id,
                to: toNode.id
            };
            connections.push(connection);
            drawConnection(fromNode, toNode, connection.id);
        }

        function drawConnection(fromNode, toNode, connectionId) {
            const svg = document.getElementById('connectionsSvg');
            const fromRect = fromNode.getBoundingClientRect();
            const toRect = toNode.getBoundingClientRect();
            const canvasRect = document.getElementById('canvas').getBoundingClientRect();

            const fromX = fromRect.left - canvasRect.left + fromRect.width / 2;
            const fromY = fromRect.top - canvasRect.top + fromRect.height / 2;
            const toX = toRect.left - canvasRect.left + toRect.width / 2;
            const toY = toRect.top - canvasRect.top + toRect.height / 2;

            // Create arrow line
            const line = document.createElementNS('http://www.w3.org/2000/svg', 'line');
            line.setAttribute('x1', fromX);
            line.setAttribute('y1', fromY);
            line.setAttribute('x2', toX);
            line.setAttribute('y2', toY);
            line.setAttribute('stroke', '#6b7280');
            line.setAttribute('stroke-width', '2');
            line.setAttribute('marker-end', 'url(#arrowhead)');
            line.id = connectionId;

            svg.appendChild(line);

            // Add arrowhead marker if not exists
            if (!document.getElementById('arrowhead')) {
                const defs = document.createElementNS('http://www.w3.org/2000/svg', 'defs');
                const marker = document.createElementNS('http://www.w3.org/2000/svg', 'marker');
                marker.id = 'arrowhead';
                marker.setAttribute('markerWidth', '10');
                marker.setAttribute('markerHeight', '7');
                marker.setAttribute('refX', '9');
                marker.setAttribute('refY', '3.5');
                marker.setAttribute('orient', 'auto');

                const polygon = document.createElementNS('http://www.w3.org/2000/svg', 'polygon');
                polygon.setAttribute('points', '0 0, 10 3.5, 0 7');
                polygon.setAttribute('fill', '#6b7280');

                marker.appendChild(polygon);
                defs.appendChild(marker);
                svg.appendChild(defs);
            }
        }

        function updateConnections() {
            const svg = document.getElementById('connectionsSvg');
            svg.innerHTML = '';

            // Re-add arrowhead marker
            const defs = document.createElementNS('http://www.w3.org/2000/svg', 'defs');
            const marker = document.createElementNS('http://www.w3.org/2000/svg', 'marker');
            marker.id = 'arrowhead';
            marker.setAttribute('markerWidth', '10');
            marker.setAttribute('markerHeight', '7');
            marker.setAttribute('refX', '9');
            marker.setAttribute('refY', '3.5');
            marker.setAttribute('orient', 'auto');

            const polygon = document.createElementNS('http://www.w3.org/2000/svg', 'polygon');
            polygon.setAttribute('points', '0 0, 10 3.5, 0 7');
            polygon.setAttribute('fill', '#6b7280');

            marker.appendChild(polygon);
            defs.appendChild(marker);
            svg.appendChild(defs);

            // Redraw all connections
            connections.forEach(conn => {
                const fromNode = document.getElementById(conn.from);
                const toNode = document.getElementById(conn.to);
                if (fromNode && toNode) {
                    drawConnection(fromNode, toNode, conn.id);
                }
            });
        }

        // Utility Functions
        function deleteSelected() {
            if (selectedNode) {
                // Remove connections involving this node
                connections = connections.filter(conn =>
                    conn.from !== selectedNode.id && conn.to !== selectedNode.id
                );

                selectedNode.remove();
                selectedNode = null;
                updateConnections();
                updateSkillCount();
            }
        }

        function clearCanvas() {
            if (confirm('¿Estás seguro de que quieres limpiar el canvas? Se perderán todos los cambios.')) {
                document.querySelectorAll('.skill-node').forEach(node => node.remove());
                connections = [];
                updateConnections();
                updateSkillCount();
                document.getElementById('pathName').value = '';
                document.getElementById('pathDescription').value = '';
            }
        }

        function updateSkillCount() {
            const count = document.querySelectorAll('.skill-node').length;
            document.getElementById('skillCount').textContent = count;
        }

        function filterSkills() {
            const searchTerm = document.getElementById('skillSearch').value.toLowerCase();
            const areaFilter = document.getElementById('areaFilter').value;

            document.querySelectorAll('.skill-category').forEach(category => {
                const area = category.getAttribute('data-area');
                let categoryVisible = false;

                if (!areaFilter || area === areaFilter) {
                    category.querySelectorAll('.skill-item').forEach(item => {
                        const skillName = item.textContent.toLowerCase();
                        if (skillName.includes(searchTerm)) {
                            item.style.display = 'block';
                            categoryVisible = true;
                        } else {
                            item.style.display = 'none';
                        }
                    });
                }

                category.style.display = categoryVisible ? 'block' : 'none';
            });
        }

        function saveCareerPath() {
            const pathName = document.getElementById('pathName').value || 'Mi Career Path Personalizado';
            const pathDescription = document.getElementById('pathDescription').value || 'Career path creado con el builder';
            const skills = Array.from(document.querySelectorAll('.skill-node')).map(node => ({
                id: node.id,
                skill: node.skillData,
                position: {
                    x: parseInt(node.style.left),
                    y: parseInt(node.style.top)
                }
            }));

            const careerPath = {
                name: pathName,
                description: pathDescription,
                skills: skills,
                connections: connections,
                createdAt: new Date().toISOString()
            };

            console.log('Career Path guardado:', careerPath);
            alert(`¡Career Path "${pathName}" guardado exitosamente!\n\n${skills.length} skills conectadas\n${connections.length} conexiones creadas\n\nAhora aparecerá en tu lista de Career Paths personalizados.`);
        }

        // Initialize
        window.addEventListener('resize', updateConnections);
    </script>
</body>
</html>
