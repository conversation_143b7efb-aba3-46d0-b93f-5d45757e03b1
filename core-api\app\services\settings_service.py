"""
Settings management service
"""

import json
import yaml
from datetime import datetime
from typing import Optional, List, Dict, Any, Union
from uuid import UUID

from sqlmodel import Session, select

from ..models.settings import (
    SystemSetting, SystemSettingCreate, SystemSettingUpdate, SystemSettingResponse,
    SettingsBulkUpdate, SettingsExport, SettingsImport, SettingsValidationResult,
    SettingType, SettingCategory, DEFAULT_SETTINGS
)
from ..models.user import User


class SettingsService:
    """Settings management service"""
    
    def __init__(self, db: Session):
        self.db = db
    
    async def initialize_default_settings(self, tenant_id: UUID = None):
        """Initialize default settings for a tenant"""
        for setting_data in DEFAULT_SETTINGS:
            # Check if setting already exists
            existing_setting = self.db.exec(
                select(SystemSetting).where(
                    SystemSetting.key == setting_data["key"],
                    SystemSetting.tenant_id == tenant_id
                )
            ).first()
            
            if not existing_setting:
                setting = SystemSetting(
                    tenant_id=tenant_id,
                    key=setting_data["key"],
                    value=setting_data["default_value"],
                    default_value=setting_data["default_value"],
                    setting_type=setting_data["setting_type"],
                    category=setting_data["category"],
                    description=setting_data["description"],
                    is_public=setting_data.get("is_public", False),
                    is_required=setting_data.get("is_required", False),
                    validation_rules=setting_data.get("validation_rules", {})
                )
                
                self.db.add(setting)
        
        self.db.commit()
    
    async def get_setting(self, key: str, tenant_id: UUID = None) -> Optional[SystemSettingResponse]:
        """Get a setting by key"""
        setting = self.db.exec(
            select(SystemSetting).where(
                SystemSetting.key == key,
                SystemSetting.tenant_id == tenant_id
            )
        ).first()
        
        if not setting:
            return None
        
        # Get updated by user name
        updated_by_name = None
        if setting.updated_by:
            user = self.db.get(User, setting.updated_by)
            updated_by_name = f"{user.first_name} {user.last_name}" if user else None
        
        return SystemSettingResponse(
            setting_id=setting.setting_id,
            key=setting.key,
            value=setting.value,
            setting_type=setting.setting_type,
            category=setting.category,
            description=setting.description,
            is_public=setting.is_public,
            is_required=setting.is_required,
            validation_rules=setting.validation_rules,
            default_value=setting.default_value,
            updated_by=setting.updated_by,
            created_at=setting.created_at,
            updated_at=setting.updated_at,
            updated_by_name=updated_by_name
        )
    
    async def get_setting_value(self, key: str, tenant_id: UUID = None, default: Any = None) -> Any:
        """Get setting value with type conversion"""
        setting = await self.get_setting(key, tenant_id)
        
        if not setting or setting.value is None:
            return default
        
        # Convert value based on type
        return self._convert_setting_value(setting.value, setting.setting_type)
    
    async def set_setting(
        self, 
        key: str, 
        value: Any, 
        tenant_id: UUID = None, 
        updated_by: UUID = None
    ) -> SystemSetting:
        """Set a setting value"""
        setting = self.db.exec(
            select(SystemSetting).where(
                SystemSetting.key == key,
                SystemSetting.tenant_id == tenant_id
            )
        ).first()
        
        if not setting:
            raise ValueError(f"Setting '{key}' not found")
        
        # Validate value
        validation_result = await self.validate_setting_value(key, value, setting.validation_rules)
        if not validation_result.is_valid:
            raise ValueError(f"Invalid value: {', '.join(validation_result.errors)}")
        
        # Convert value to string for storage
        setting.value = self._serialize_setting_value(value, setting.setting_type)
        setting.updated_by = updated_by
        setting.updated_at = datetime.utcnow()
        
        self.db.add(setting)
        self.db.commit()
        self.db.refresh(setting)
        
        return setting
    
    async def list_settings(
        self, 
        tenant_id: UUID = None,
        category: SettingCategory = None,
        is_public: bool = None,
        search: str = None
    ) -> List[SystemSettingResponse]:
        """List settings with filtering"""
        stmt = select(SystemSetting).where(SystemSetting.tenant_id == tenant_id)
        
        if category:
            stmt = stmt.where(SystemSetting.category == category)
        
        if is_public is not None:
            stmt = stmt.where(SystemSetting.is_public == is_public)
        
        if search:
            search_term = f"%{search}%"
            stmt = stmt.where(
                (SystemSetting.key.ilike(search_term)) |
                (SystemSetting.description.ilike(search_term))
            )
        
        stmt = stmt.order_by(SystemSetting.category, SystemSetting.key)
        settings = self.db.exec(stmt).all()
        
        # Convert to response models
        setting_responses = []
        for setting in settings:
            setting_response = await self.get_setting(setting.key, tenant_id)
            if setting_response:
                setting_responses.append(setting_response)
        
        return setting_responses
    
    async def bulk_update_settings(
        self, 
        updates: SettingsBulkUpdate, 
        tenant_id: UUID = None, 
        updated_by: UUID = None
    ) -> Dict[str, bool]:
        """Bulk update multiple settings"""
        results = {}
        
        for key, value in updates.settings.items():
            try:
                await self.set_setting(key, value, tenant_id, updated_by)
                results[key] = True
            except Exception as e:
                results[key] = False
                # Log error
                print(f"Failed to update setting {key}: {e}")
        
        return results
    
    async def export_settings(
        self, 
        export_config: SettingsExport, 
        tenant_id: UUID = None
    ) -> Dict[str, Any]:
        """Export settings to various formats"""
        stmt = select(SystemSetting).where(SystemSetting.tenant_id == tenant_id)
        
        # Filter by categories if specified
        if export_config.categories:
            stmt = stmt.where(SystemSetting.category.in_(export_config.categories))
        
        # Exclude encrypted settings unless explicitly requested
        if not export_config.include_encrypted:
            stmt = stmt.where(SystemSetting.setting_type != SettingType.ENCRYPTED)
        
        settings = self.db.exec(stmt).all()
        
        # Build export data
        export_data = {}
        for setting in settings:
            if setting.value is not None:
                export_data[setting.key] = self._convert_setting_value(setting.value, setting.setting_type)
        
        # Format based on requested format
        if export_config.format == "json":
            return {"settings": export_data, "format": "json"}
        elif export_config.format == "yaml":
            return {"settings": yaml.dump(export_data), "format": "yaml"}
        elif export_config.format == "env":
            env_lines = []
            for key, value in export_data.items():
                env_key = key.upper().replace(".", "_")
                env_lines.append(f"{env_key}={value}")
            return {"settings": "\n".join(env_lines), "format": "env"}
        else:
            return {"settings": export_data, "format": "json"}
    
    async def import_settings(
        self, 
        import_config: SettingsImport, 
        tenant_id: UUID = None, 
        updated_by: UUID = None
    ) -> SettingsValidationResult:
        """Import settings from external data"""
        errors = []
        warnings = []
        validated_settings = {}
        
        for key, value in import_config.settings.items():
            try:
                # Check if setting exists
                setting = self.db.exec(
                    select(SystemSetting).where(
                        SystemSetting.key == key,
                        SystemSetting.tenant_id == tenant_id
                    )
                ).first()
                
                if not setting:
                    if not import_config.overwrite_existing:
                        warnings.append(f"Setting '{key}' does not exist and will be skipped")
                        continue
                    else:
                        errors.append(f"Setting '{key}' does not exist")
                        continue
                
                # Validate value
                validation_result = await self.validate_setting_value(key, value, setting.validation_rules)
                if not validation_result.is_valid:
                    errors.extend([f"{key}: {error}" for error in validation_result.errors])
                    continue
                
                validated_settings[key] = value
                
                # Apply setting if not validation-only
                if not import_config.validate_only:
                    await self.set_setting(key, value, tenant_id, updated_by)
                
            except Exception as e:
                errors.append(f"Error processing setting '{key}': {str(e)}")
        
        return SettingsValidationResult(
            is_valid=len(errors) == 0,
            errors=errors,
            warnings=warnings,
            validated_settings=validated_settings
        )
    
    async def validate_setting_value(self, key: str, value: Any, validation_rules: Dict[str, Any]) -> SettingsValidationResult:
        """Validate a setting value against its rules"""
        errors = []
        warnings = []
        
        if not validation_rules:
            return SettingsValidationResult(
                is_valid=True,
                errors=[],
                warnings=[],
                validated_settings={key: value}
            )
        
        # Type validation
        if "type" in validation_rules:
            expected_type = validation_rules["type"]
            if not isinstance(value, expected_type):
                errors.append(f"Expected {expected_type.__name__}, got {type(value).__name__}")
        
        # String validations
        if isinstance(value, str):
            if "min_length" in validation_rules and len(value) < validation_rules["min_length"]:
                errors.append(f"Minimum length is {validation_rules['min_length']}")
            
            if "max_length" in validation_rules and len(value) > validation_rules["max_length"]:
                errors.append(f"Maximum length is {validation_rules['max_length']}")
            
            if "choices" in validation_rules and value not in validation_rules["choices"]:
                errors.append(f"Value must be one of: {', '.join(validation_rules['choices'])}")
            
            if "email" in validation_rules and validation_rules["email"]:
                if "@" not in value or "." not in value:
                    errors.append("Invalid email format")
        
        # Numeric validations
        if isinstance(value, (int, float)):
            if "min" in validation_rules and value < validation_rules["min"]:
                errors.append(f"Minimum value is {validation_rules['min']}")
            
            if "max" in validation_rules and value > validation_rules["max"]:
                errors.append(f"Maximum value is {validation_rules['max']}")
        
        return SettingsValidationResult(
            is_valid=len(errors) == 0,
            errors=errors,
            warnings=warnings,
            validated_settings={key: value} if len(errors) == 0 else {}
        )
    
    def _convert_setting_value(self, value: str, setting_type: SettingType) -> Any:
        """Convert string value to appropriate type"""
        if value is None:
            return None
        
        try:
            if setting_type == SettingType.STRING or setting_type == SettingType.ENCRYPTED:
                return value
            elif setting_type == SettingType.INTEGER:
                return int(value)
            elif setting_type == SettingType.FLOAT:
                return float(value)
            elif setting_type == SettingType.BOOLEAN:
                return value.lower() in ('true', '1', 'yes', 'on')
            elif setting_type == SettingType.JSON:
                return json.loads(value)
            else:
                return value
        except (ValueError, json.JSONDecodeError):
            return value  # Return as string if conversion fails
    
    def _serialize_setting_value(self, value: Any, setting_type: SettingType) -> str:
        """Serialize value to string for storage"""
        if value is None:
            return None
        
        if setting_type == SettingType.JSON:
            return json.dumps(value)
        elif setting_type == SettingType.BOOLEAN:
            return "true" if value else "false"
        else:
            return str(value)
    
    async def reset_setting(self, key: str, tenant_id: UUID = None, updated_by: UUID = None) -> bool:
        """Reset setting to default value"""
        setting = self.db.exec(
            select(SystemSetting).where(
                SystemSetting.key == key,
                SystemSetting.tenant_id == tenant_id
            )
        ).first()
        
        if not setting:
            return False
        
        setting.value = setting.default_value
        setting.updated_by = updated_by
        setting.updated_at = datetime.utcnow()
        
        self.db.add(setting)
        self.db.commit()
        
        return True
    
    async def get_public_settings(self, tenant_id: UUID = None) -> Dict[str, Any]:
        """Get all public settings as a dictionary"""
        settings = await self.list_settings(tenant_id=tenant_id, is_public=True)
        
        public_settings = {}
        for setting in settings:
            public_settings[setting.key] = self._convert_setting_value(setting.value, setting.setting_type)
        
        return public_settings
