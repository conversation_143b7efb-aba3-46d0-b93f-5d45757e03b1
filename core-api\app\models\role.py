"""
Role and permission models for RBAC
"""

from datetime import datetime
from typing import Optional, List
from uuid import UUID

from sqlmodel import SQLModel, Field

from .base import TimestampMixin, TenantMixin


class RoleBase(SQLModel):
    """Base role model"""
    name: str = Field(max_length=100)
    description: Optional[str] = Field(default=None, max_length=500)
    is_system: bool = Field(default=False)
    is_active: bool = Field(default=True)


class Role(RoleBase, TenantMixin, TimestampMixin, table=True):
    """Role table model"""
    __tablename__ = "roles"
    
    role_id: UUID = Field(primary_key=True)
    permissions: Optional[List[str]] = Field(default_factory=list, sa_column_kwargs={"type_": "JSONB"})


class RoleCreate(RoleBase):
    """Role creation model"""
    permissions: Optional[List[str]] = Field(default_factory=list)


class RoleUpdate(SQLModel):
    """Role update model"""
    name: Optional[str] = Field(default=None, max_length=100)
    description: Optional[str] = Field(default=None, max_length=500)
    is_active: Optional[bool] = None
    permissions: Optional[List[str]] = None


class RoleResponse(RoleBase):
    """Role response model"""
    role_id: UUID
    created_at: datetime
    updated_at: datetime
    permissions: List[str]
    user_count: Optional[int] = None


class Permission(SQLModel):
    """Permission model"""
    id: str = Field(primary_key=True)
    name: str = Field(max_length=100)
    description: str = Field(max_length=500)
    category: str = Field(max_length=50)
    resource: str = Field(max_length=50)
    action: str = Field(max_length=50)


class UserRole(SQLModel, table=True):
    """User-Role association table"""
    __tablename__ = "user_roles"
    
    user_id: UUID = Field(foreign_key="users.user_id", primary_key=True)
    role_id: UUID = Field(foreign_key="roles.role_id", primary_key=True)
    tenant_id: UUID = Field(foreign_key="tenants.tenant_id", index=True)
    assigned_by: UUID = Field(foreign_key="users.user_id")
    assigned_at: datetime = Field(default_factory=datetime.utcnow)
    expires_at: Optional[datetime] = None
    is_active: bool = Field(default=True)


class RolePermissionCheck(SQLModel):
    """Role permission check model"""
    user_id: UUID
    permission: str
    resource_id: Optional[UUID] = None
    context: Optional[dict] = None


class PermissionCategory(SQLModel):
    """Permission category model"""
    category: str
    name: str
    description: str
    permissions: List[Permission]


# Predefined permissions
SYSTEM_PERMISSIONS = [
    # User Management
    Permission(
        id="user.create",
        name="Create Users",
        description="Create new users in the system",
        category="Users",
        resource="user",
        action="create"
    ),
    Permission(
        id="user.read",
        name="View Users",
        description="View user information",
        category="Users",
        resource="user",
        action="read"
    ),
    Permission(
        id="user.update",
        name="Update Users",
        description="Update user information",
        category="Users",
        resource="user",
        action="update"
    ),
    Permission(
        id="user.delete",
        name="Delete Users",
        description="Delete users from the system",
        category="Users",
        resource="user",
        action="delete"
    ),
    
    # Course Management
    Permission(
        id="course.create",
        name="Create Courses",
        description="Create new courses",
        category="Courses",
        resource="course",
        action="create"
    ),
    Permission(
        id="course.read",
        name="View Courses",
        description="View course information",
        category="Courses",
        resource="course",
        action="read"
    ),
    Permission(
        id="course.update",
        name="Update Courses",
        description="Update course information",
        category="Courses",
        resource="course",
        action="update"
    ),
    Permission(
        id="course.delete",
        name="Delete Courses",
        description="Delete courses",
        category="Courses",
        resource="course",
        action="delete"
    ),
    Permission(
        id="course.publish",
        name="Publish Courses",
        description="Publish courses to students",
        category="Courses",
        resource="course",
        action="publish"
    ),
    
    # Assessment Management
    Permission(
        id="exam.create",
        name="Create Exams",
        description="Create new exams",
        category="Assessments",
        resource="exam",
        action="create"
    ),
    Permission(
        id="exam.grade",
        name="Grade Exams",
        description="Grade student exam submissions",
        category="Assessments",
        resource="exam",
        action="grade"
    ),
    Permission(
        id="question.create",
        name="Create Questions",
        description="Create questions for question bank",
        category="Assessments",
        resource="question",
        action="create"
    ),
    
    # Group Management
    Permission(
        id="group.create",
        name="Create Groups",
        description="Create study groups",
        category="Groups",
        resource="group",
        action="create"
    ),
    Permission(
        id="group.manage",
        name="Manage Groups",
        description="Manage group memberships and settings",
        category="Groups",
        resource="group",
        action="manage"
    ),
    
    # Analytics
    Permission(
        id="analytics.view",
        name="View Analytics",
        description="View system analytics and reports",
        category="Analytics",
        resource="analytics",
        action="view"
    ),
    Permission(
        id="analytics.export",
        name="Export Data",
        description="Export analytics data",
        category="Analytics",
        resource="analytics",
        action="export"
    ),
    
    # System Administration
    Permission(
        id="system.settings",
        name="System Settings",
        description="Manage system configuration",
        category="System",
        resource="system",
        action="settings"
    ),
    Permission(
        id="role.manage",
        name="Manage Roles",
        description="Create and manage user roles",
        category="System",
        resource="role",
        action="manage"
    ),
]
