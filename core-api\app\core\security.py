"""
Security utilities and permission checking
"""

from functools import wraps
from typing import List, Optional, Callable, Any
from uuid import UUID

from fastapi import HTTPException, status, Depends
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlmodel import Session, select

from .database import get_session
from ..services.auth_service import AuthService
from ..models.user import User
from ..models.role import Role, UserRole, SYSTEM_PERMISSIONS


security = HTTPBearer()


class PermissionChecker:
    """Permission checking utility"""
    
    def __init__(self, db: Session):
        self.db = db
        self.auth_service = AuthService(db)
    
    async def get_current_user(self, credentials: HTTPAuthorizationCredentials) -> User:
        """Get current authenticated user"""
        user = await self.auth_service.verify_token(credentials.credentials)
        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid or expired token"
            )
        return user
    
    async def get_user_permissions(self, user_id: UUID) -> List[str]:
        """Get all permissions for a user"""
        # Get user roles
        stmt = select(Role).join(UserRole).where(
            UserRole.user_id == user_id,
            UserRole.is_active == True
        )
        roles = self.db.exec(stmt).all()
        
        # Collect all permissions
        permissions = set()
        for role in roles:
            if role.permissions:
                permissions.update(role.permissions)
        
        return list(permissions)
    
    async def check_permission(self, user: User, permission: str, resource_id: UUID = None) -> bool:
        """Check if user has specific permission"""
        user_permissions = await self.get_user_permissions(user.user_id)
        
        # Check exact permission
        if permission in user_permissions:
            return True
        
        # Check wildcard permissions
        permission_parts = permission.split('.')
        if len(permission_parts) == 2:
            resource, action = permission_parts
            wildcard_permission = f"{resource}.*"
            if wildcard_permission in user_permissions:
                return True
        
        # Check admin permission
        if "admin.*" in user_permissions:
            return True
        
        return False
    
    async def check_resource_access(self, user: User, resource_type: str, resource_id: UUID) -> bool:
        """Check if user can access specific resource"""
        # This would implement resource-specific access control
        # For now, just check if user is in same tenant
        
        if resource_type == "course":
            from ..models.course import Course
            resource = self.db.get(Course, resource_id)
            return resource and resource.tenant_id == user.tenant_id
        
        elif resource_type == "exam":
            from ..models.exam import Exam
            resource = self.db.get(Exam, resource_id)
            return resource and resource.tenant_id == user.tenant_id
        
        elif resource_type == "user":
            target_user = self.db.get(User, resource_id)
            return target_user and target_user.tenant_id == user.tenant_id
        
        return False


def get_permission_checker(db: Session = Depends(get_session)) -> PermissionChecker:
    """Get permission checker dependency"""
    return PermissionChecker(db)


async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    permission_checker: PermissionChecker = Depends(get_permission_checker)
) -> User:
    """Get current authenticated user dependency"""
    return await permission_checker.get_current_user(credentials)


def require_permissions(permissions: List[str]):
    """Decorator to require specific permissions"""
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # Extract user from kwargs (should be injected by dependency)
            current_user = kwargs.get('current_user')
            if not current_user:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Authentication required"
                )
            
            # Get permission checker from kwargs or create new one
            permission_checker = kwargs.get('permission_checker')
            if not permission_checker:
                # This is a fallback, ideally permission_checker should be injected
                from .database import get_db
                db = next(get_db())
                permission_checker = PermissionChecker(db)
            
            # Check permissions
            for permission in permissions:
                has_permission = await permission_checker.check_permission(current_user, permission)
                if not has_permission:
                    raise HTTPException(
                        status_code=status.HTTP_403_FORBIDDEN,
                        detail=f"Permission denied: {permission}"
                    )
            
            return await func(*args, **kwargs)
        return wrapper
    return decorator


def require_resource_access(resource_type: str, resource_id_param: str = "resource_id"):
    """Decorator to require access to specific resource"""
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def wrapper(*args, **kwargs):
            current_user = kwargs.get('current_user')
            if not current_user:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Authentication required"
                )
            
            resource_id = kwargs.get(resource_id_param)
            if not resource_id:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Resource ID parameter '{resource_id_param}' is required"
                )
            
            permission_checker = kwargs.get('permission_checker')
            if not permission_checker:
                from .database import get_db
                db = next(get_db())
                permission_checker = PermissionChecker(db)
            
            has_access = await permission_checker.check_resource_access(
                current_user, resource_type, resource_id
            )
            
            if not has_access:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"Access denied to {resource_type}"
                )
            
            return await func(*args, **kwargs)
        return wrapper
    return decorator


class RoleChecker:
    """Role-based access control"""
    
    def __init__(self, required_roles: List[str]):
        self.required_roles = required_roles
    
    def __call__(self, current_user: User = Depends(get_current_user)) -> User:
        """Check if user has required roles"""
        # This would check user roles
        # For now, just return the user
        return current_user


def require_roles(roles: List[str]):
    """Dependency to require specific roles"""
    return RoleChecker(roles)


# Permission constants for easy reference
class Permissions:
    """Permission constants"""
    
    # User management
    USER_CREATE = "user.create"
    USER_READ = "user.read"
    USER_UPDATE = "user.update"
    USER_DELETE = "user.delete"
    
    # Course management
    COURSE_CREATE = "course.create"
    COURSE_READ = "course.read"
    COURSE_UPDATE = "course.update"
    COURSE_DELETE = "course.delete"
    COURSE_PUBLISH = "course.publish"
    
    # Exam management
    EXAM_CREATE = "exam.create"
    EXAM_READ = "exam.read"
    EXAM_UPDATE = "exam.update"
    EXAM_DELETE = "exam.delete"
    EXAM_GRADE = "exam.grade"
    
    # Question management
    QUESTION_CREATE = "question.create"
    QUESTION_READ = "question.read"
    QUESTION_UPDATE = "question.update"
    QUESTION_DELETE = "question.delete"
    
    # Group management
    GROUP_CREATE = "group.create"
    GROUP_READ = "group.read"
    GROUP_UPDATE = "group.update"
    GROUP_DELETE = "group.delete"
    GROUP_MANAGE = "group.manage"
    
    # Analytics
    ANALYTICS_VIEW = "analytics.view"
    ANALYTICS_EXPORT = "analytics.export"
    
    # System administration
    SYSTEM_SETTINGS = "system.settings"
    ROLE_MANAGE = "role.manage"
    
    # Admin wildcard
    ADMIN_ALL = "admin.*"


# Utility functions for common permission checks
async def check_course_instructor(user: User, course_id: UUID, db: Session) -> bool:
    """Check if user is instructor of the course"""
    from ..models.course import Course
    course = db.get(Course, course_id)
    return course and course.instructor_id == user.user_id


async def check_exam_access(user: User, exam_id: UUID, db: Session) -> bool:
    """Check if user can access exam"""
    from ..models.exam import Exam
    from ..models.enrollment import CourseEnrollment
    
    exam = db.get(Exam, exam_id)
    if not exam:
        return False
    
    # Check if user is instructor
    if await check_course_instructor(user, exam.course_id, db):
        return True
    
    # Check if user is enrolled in course
    enrollment = db.exec(
        select(CourseEnrollment).where(
            CourseEnrollment.course_id == exam.course_id,
            CourseEnrollment.user_id == user.user_id,
            CourseEnrollment.status == "active"
        )
    ).first()
    
    return enrollment is not None


async def check_group_membership(user: User, group_id: UUID, db: Session) -> bool:
    """Check if user is member of group"""
    from ..models.group import GroupMember
    
    membership = db.exec(
        select(GroupMember).where(
            GroupMember.group_id == group_id,
            GroupMember.user_id == user.user_id,
            GroupMember.is_active == True
        )
    ).first()
    
    return membership is not None


async def check_group_leader(user: User, group_id: UUID, db: Session) -> bool:
    """Check if user is leader of group"""
    from ..models.group import GroupMember
    
    membership = db.exec(
        select(GroupMember).where(
            GroupMember.group_id == group_id,
            GroupMember.user_id == user.user_id,
            GroupMember.role == "leader",
            GroupMember.is_active == True
        )
    ).first()
    
    return membership is not None
