-- Gamification and Scoring System Tables
-- This script creates tables for user scores, leaderboards, and expert validations

-- Create user_scores table
CREATE TABLE user_scores (
    score_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
    score_type VARCHAR(20) NOT NULL, -- student, creator
    period_type VARCHAR(20) NOT NULL, -- weekly, monthly, alltime
    period_start DATE NOT NULL,
    period_end DATE,
    total_points INTEGER DEFAULT 0,
    position_rank INTEGER,
    courses_completed INTEGER DEFAULT 0,
    courses_created INTEGER DEFAULT 0,
    forum_posts INTEGER DEFAULT 0,
    forum_likes_received INTEGER DEFAULT 0,
    course_ratings_given INTEGER DEFAULT 0,
    career_path_bonus_points INTEGER DEFAULT 0,
    expert_validation_bonus INTEGER DEFAULT 0,
    last_updated TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, score_type, period_type, period_start)
);

-- Create score_transactions table
CREATE TABLE score_transactions (
    transaction_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
    score_type VARCHAR(20) NOT NULL, -- student, creator
    action_type VARCHAR(50) NOT NULL, -- course_completion, course_creation, forum_post, etc.
    points_awarded INTEGER NOT NULL,
    bonus_multiplier DECIMAL(3,2) DEFAULT 1.00,
    final_points INTEGER NOT NULL,
    reference_id UUID, -- ID of course, post, etc. that generated points
    reference_type VARCHAR(50), -- course, forum_post, course_rating, etc.
    metadata JSONB DEFAULT '{}', -- additional context (difficulty level, expert validation, etc.)
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create leaderboard_cache table
CREATE TABLE leaderboard_cache (
    cache_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    score_type VARCHAR(20) NOT NULL, -- student, creator
    period_type VARCHAR(20) NOT NULL, -- weekly, monthly, alltime
    period_start DATE NOT NULL,
    period_end DATE,
    user_id UUID NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
    total_points INTEGER NOT NULL,
    position_rank INTEGER NOT NULL,
    previous_rank INTEGER,
    rank_change INTEGER, -- positive = moved up, negative = moved down
    percentile DECIMAL(5,2), -- user's percentile in this leaderboard
    generated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(score_type, period_type, period_start, user_id)
);

-- Create expert_validations table
CREATE TABLE expert_validations (
    validation_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    expert_id UUID NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
    target_type VARCHAR(50) NOT NULL, -- course, skill_completion, career_path
    target_id UUID NOT NULL,
    validation_type VARCHAR(50) NOT NULL, -- difficulty_assessment, quality_review, skill_verification
    validation_result VARCHAR(20) NOT NULL, -- approved, rejected, needs_revision
    difficulty_level VARCHAR(20), -- beginner, intermediate, advanced (for courses)
    quality_rating INTEGER CHECK (quality_rating >= 1 AND quality_rating <= 5),
    utility_rating INTEGER CHECK (utility_rating >= 1 AND utility_rating <= 5),
    comments TEXT,
    points_awarded INTEGER DEFAULT 0, -- bonus points for creator if validation is positive
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create scoring_config table
CREATE TABLE scoring_config (
    config_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    config_key VARCHAR(100) NOT NULL UNIQUE,
    config_value JSONB NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    updated_by UUID REFERENCES users(user_id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create user_achievements table
CREATE TABLE user_achievements (
    achievement_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
    achievement_type VARCHAR(50) NOT NULL, -- course_master, forum_contributor, streak_keeper, etc.
    achievement_level VARCHAR(20), -- bronze, silver, gold, platinum
    title VARCHAR(255) NOT NULL,
    description TEXT,
    icon_url TEXT,
    points_awarded INTEGER DEFAULT 0,
    earned_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    metadata JSONB DEFAULT '{}',
    UNIQUE(user_id, achievement_type, achievement_level)
);

-- Create point_multipliers table
CREATE TABLE point_multipliers (
    multiplier_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL,
    description TEXT,
    multiplier_value DECIMAL(3,2) NOT NULL,
    conditions JSONB NOT NULL, -- conditions that must be met for multiplier to apply
    is_active BOOLEAN DEFAULT TRUE,
    valid_from TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    valid_until TIMESTAMP WITH TIME ZONE,
    created_by UUID REFERENCES users(user_id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create leaderboard_periods table
CREATE TABLE leaderboard_periods (
    period_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    period_type VARCHAR(20) NOT NULL, -- weekly, monthly, quarterly, yearly
    period_start DATE NOT NULL,
    period_end DATE NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    total_participants INTEGER DEFAULT 0,
    total_points_awarded INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(period_type, period_start)
);

-- Add indexes for performance
CREATE INDEX idx_user_scores_user_id ON user_scores(user_id);
CREATE INDEX idx_user_scores_score_type ON user_scores(score_type);
CREATE INDEX idx_user_scores_period_type ON user_scores(period_type);
CREATE INDEX idx_user_scores_period_start ON user_scores(period_start);
CREATE INDEX idx_user_scores_total_points ON user_scores(total_points);
CREATE INDEX idx_user_scores_position_rank ON user_scores(position_rank);

CREATE INDEX idx_score_transactions_user_id ON score_transactions(user_id);
CREATE INDEX idx_score_transactions_score_type ON score_transactions(score_type);
CREATE INDEX idx_score_transactions_action_type ON score_transactions(action_type);
CREATE INDEX idx_score_transactions_reference_id ON score_transactions(reference_id);
CREATE INDEX idx_score_transactions_created_at ON score_transactions(created_at);

CREATE INDEX idx_leaderboard_cache_score_type ON leaderboard_cache(score_type);
CREATE INDEX idx_leaderboard_cache_period_type ON leaderboard_cache(period_type);
CREATE INDEX idx_leaderboard_cache_period_start ON leaderboard_cache(period_start);
CREATE INDEX idx_leaderboard_cache_position_rank ON leaderboard_cache(position_rank);
CREATE INDEX idx_leaderboard_cache_user_id ON leaderboard_cache(user_id);

CREATE INDEX idx_expert_validations_expert_id ON expert_validations(expert_id);
CREATE INDEX idx_expert_validations_target_type ON expert_validations(target_type);
CREATE INDEX idx_expert_validations_target_id ON expert_validations(target_id);
CREATE INDEX idx_expert_validations_validation_result ON expert_validations(validation_result);

CREATE INDEX idx_user_achievements_user_id ON user_achievements(user_id);
CREATE INDEX idx_user_achievements_achievement_type ON user_achievements(achievement_type);
CREATE INDEX idx_user_achievements_earned_at ON user_achievements(earned_at);

-- Add updated_at triggers
CREATE TRIGGER update_expert_validations_updated_at BEFORE UPDATE ON expert_validations
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_scoring_config_updated_at BEFORE UPDATE ON scoring_config
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_point_multipliers_updated_at BEFORE UPDATE ON point_multipliers
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Enable RLS for gamification tables
ALTER TABLE user_scores ENABLE ROW LEVEL SECURITY;
ALTER TABLE score_transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE leaderboard_cache ENABLE ROW LEVEL SECURITY;
ALTER TABLE expert_validations ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_achievements ENABLE ROW LEVEL SECURITY;

-- Function to award points
CREATE OR REPLACE FUNCTION award_points(
    p_user_id UUID,
    p_score_type VARCHAR(20),
    p_action_type VARCHAR(50),
    p_base_points INTEGER,
    p_reference_id UUID DEFAULT NULL,
    p_reference_type VARCHAR(50) DEFAULT NULL,
    p_metadata JSONB DEFAULT '{}'
)
RETURNS INTEGER AS $$
DECLARE
    v_multiplier DECIMAL(3,2) := 1.00;
    v_final_points INTEGER;
    v_period_start DATE;
BEGIN
    -- Calculate multipliers based on conditions
    -- This is a simplified version - real implementation would check various conditions
    
    -- Career path bonus
    IF p_metadata->>'career_path_bonus' = 'true' THEN
        v_multiplier := v_multiplier * 1.5;
    END IF;
    
    -- Expert validation bonus
    IF p_metadata->>'expert_validated' = 'true' THEN
        v_multiplier := v_multiplier * 1.25;
    END IF;
    
    v_final_points := ROUND(p_base_points * v_multiplier);
    
    -- Insert transaction record
    INSERT INTO score_transactions (
        user_id, score_type, action_type, points_awarded, 
        bonus_multiplier, final_points, reference_id, reference_type, metadata
    ) VALUES (
        p_user_id, p_score_type, p_action_type, p_base_points,
        v_multiplier, v_final_points, p_reference_id, p_reference_type, p_metadata
    );
    
    -- Update user scores for current periods
    -- Weekly
    v_period_start := DATE_TRUNC('week', CURRENT_DATE);
    INSERT INTO user_scores (user_id, score_type, period_type, period_start, total_points)
    VALUES (p_user_id, p_score_type, 'weekly', v_period_start, v_final_points)
    ON CONFLICT (user_id, score_type, period_type, period_start)
    DO UPDATE SET 
        total_points = user_scores.total_points + v_final_points,
        last_updated = CURRENT_TIMESTAMP;
    
    -- Monthly
    v_period_start := DATE_TRUNC('month', CURRENT_DATE);
    INSERT INTO user_scores (user_id, score_type, period_type, period_start, total_points)
    VALUES (p_user_id, p_score_type, 'monthly', v_period_start, v_final_points)
    ON CONFLICT (user_id, score_type, period_type, period_start)
    DO UPDATE SET 
        total_points = user_scores.total_points + v_final_points,
        last_updated = CURRENT_TIMESTAMP;
    
    -- All-time
    INSERT INTO user_scores (user_id, score_type, period_type, period_start, total_points)
    VALUES (p_user_id, p_score_type, 'alltime', '1970-01-01', v_final_points)
    ON CONFLICT (user_id, score_type, period_type, period_start)
    DO UPDATE SET 
        total_points = user_scores.total_points + v_final_points,
        last_updated = CURRENT_TIMESTAMP;
    
    RETURN v_final_points;
END;
$$ LANGUAGE plpgsql;

-- Function to generate leaderboard cache
CREATE OR REPLACE FUNCTION generate_leaderboard_cache(
    p_score_type VARCHAR(20),
    p_period_type VARCHAR(20),
    p_period_start DATE
)
RETURNS INTEGER AS $$
DECLARE
    v_user_record RECORD;
    v_rank INTEGER := 1;
    v_total_users INTEGER;
BEGIN
    -- Clear existing cache for this period
    DELETE FROM leaderboard_cache 
    WHERE score_type = p_score_type 
    AND period_type = p_period_type 
    AND period_start = p_period_start;
    
    -- Get total users for percentile calculation
    SELECT COUNT(*) INTO v_total_users
    FROM user_scores 
    WHERE score_type = p_score_type 
    AND period_type = p_period_type 
    AND period_start = p_period_start
    AND total_points > 0;
    
    -- Generate new cache entries
    FOR v_user_record IN
        SELECT us.user_id, us.total_points,
               LAG(lc.position_rank) OVER (PARTITION BY us.user_id ORDER BY p_period_start) as previous_rank
        FROM user_scores us
        LEFT JOIN leaderboard_cache lc ON lc.user_id = us.user_id 
            AND lc.score_type = p_score_type 
            AND lc.period_type = p_period_type
            AND lc.period_start < p_period_start
        WHERE us.score_type = p_score_type 
        AND us.period_type = p_period_type 
        AND us.period_start = p_period_start
        AND us.total_points > 0
        ORDER BY us.total_points DESC, us.last_updated ASC
    LOOP
        INSERT INTO leaderboard_cache (
            score_type, period_type, period_start, user_id, total_points,
            position_rank, previous_rank, rank_change, percentile
        ) VALUES (
            p_score_type, p_period_type, p_period_start, v_user_record.user_id,
            v_user_record.total_points, v_rank, v_user_record.previous_rank,
            CASE 
                WHEN v_user_record.previous_rank IS NULL THEN NULL
                ELSE v_user_record.previous_rank - v_rank
            END,
            ((v_total_users - v_rank + 1) * 100.0 / v_total_users)
        );
        
        v_rank := v_rank + 1;
    END LOOP;
    
    RETURN v_rank - 1; -- Return number of users processed
END;
$$ LANGUAGE plpgsql;
