# Integración de IA - Arroyo University

## Introducción

Este documento describe la arquitectura, implementación y gestión de servicios de Inteligencia Artificial en Arroyo University. La plataforma integra múltiples servicios de IA para automatizar la generación de contenido educativo, evaluación de respuestas y moderación de contenido, optimizando la experiencia educativa mientras mantiene calidad y control de costos.

---

## 1. Arquitectura de Servicios IA

### 1.1 Componentes Principales

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Core API      │    │   AI Service    │    │  External APIs  │
│   (FastAPI)     │    │   (FastAPI)     │    │                 │
└─────────┬───────┘    └─────────┬───────┘    └─────────┬───────┘
          │                      │                      │
          │                      │                      │
    ┌─────┴─────┐         ┌──────┴──────┐        ┌──────┴──────┐
    │  Request  │         │   Celery    │        │   OpenAI    │
    │Validation │         │   Workers   │        │   GPT-4     │
    └───────────┘         └──────┬──────┘        └─────────────┘
                                 │                      
                          ┌──────┴──────┐        ┌─────────────┐
                          │    Redis    │        │ Azure Speech│
                          │    Queue    │        │  TTS & STT  │
                          └─────────────┘        └─────────────┘
```

### 1.2 Tecnologías y Proveedores

| Servicio | Proveedor | Modelo/API | Propósito | SLA |
|----------|-----------|------------|-----------|-----|
| **Text Generation** | OpenAI | GPT-4 Turbo | Generación de preguntas y contenido | < 8s |
| **Text-to-Speech** | Azure | Neural Voices | Síntesis de audio para Listening | < 5s |
| **Speech-to-Text** | Azure | Whisper v3 | Transcripción de respuestas Speaking | < 3s |
| **Content Moderation** | OpenAI | Moderation API | Filtrado de contenido inapropiado | < 2s |
| **Plagiarism Detection** | Turnitin | Similarity API | Detección de similitud en textos | < 10s |
| **Scoring Engine** | OpenAI | GPT-4 + Custom Prompts | Evaluación de respuestas abiertas | < 4s |

---

## 2. Generación de Contenido

### 2.1 Tipos de Preguntas Soportadas

#### Writing Questions
```python
# Prompt template para preguntas de escritura
WRITING_PROMPT_TEMPLATE = """
Generate {count} writing prompts for {difficulty_level} level learners.
Topic: {topic}
Style: {style} (formal/informal/academic)

Requirements:
- Each prompt should elicit 150-200 words
- Include clear instructions
- Specify expected response format
- Provide context when necessary

Format as JSON array with fields: prompt, instructions, word_count_target, estimated_time_minutes
"""
```

#### Listening Questions
```python
# Proceso de generación en 3 etapas
LISTENING_GENERATION_FLOW = {
    "stage_1": "Generate conversation script (2 speakers, 150-200 words)",
    "stage_2": "Create 5 MCQ questions based on script",
    "stage_3": "Synthesize audio using Azure TTS"
}

CONVERSATION_PROMPT = """
Create a natural conversation between two people about {topic}.
Level: {difficulty_level}
Duration: approximately 45-60 seconds when spoken
Speakers: Person A and Person B
Include: greetings, main discussion, natural conclusion
"""
```

#### Speaking Questions
```python
SPEAKING_PROMPT_TEMPLATE = """
Generate {count} speaking prompts for {difficulty_level} level.
Topic: {topic}

Each prompt should:
- Be answerable in 60-90 seconds
- Include preparation time guidance (30 seconds)
- Provide clear context and expectations
- Include follow-up questions if appropriate

Format: JSON with prompt, preparation_time, response_time, evaluation_criteria
"""
```

### 2.2 Flujo de Generación

#### Proceso Asíncrono
```python
@celery_app.task(bind=True, max_retries=3)
def generate_questions_task(self, tenant_id: str, request_data: dict):
    try:
        # 1. Validar cuota IA del tenant
        quota_check = validate_ai_quota(tenant_id, request_data['count'])
        if not quota_check.allowed:
            raise QuotaExceededException()
        
        # 2. Generar contenido según tipo
        if request_data['type'] == 'listening':
            result = generate_listening_questions(request_data)
        elif request_data['type'] == 'writing':
            result = generate_writing_questions(request_data)
        elif request_data['type'] == 'speaking':
            result = generate_speaking_questions(request_data)
        
        # 3. Moderación de contenido
        moderated_result = moderate_content(result)
        
        # 4. Almacenar en base de datos
        question_ids = store_generated_questions(tenant_id, moderated_result)
        
        # 5. Actualizar métricas de uso
        update_ai_usage_metrics(tenant_id, request_data['count'])
        
        return {"status": "completed", "question_ids": question_ids}
        
    except Exception as exc:
        # Retry con backoff exponencial
        raise self.retry(exc=exc, countdown=60 * (2 ** self.request.retries))
```

### 2.3 Control de Calidad

#### Validación Automática
```python
def validate_generated_content(content: dict) -> ValidationResult:
    checks = [
        check_language_appropriateness(content),
        check_difficulty_level(content),
        check_content_length(content),
        check_grammar_correctness(content),
        check_cultural_sensitivity(content)
    ]
    
    return ValidationResult(
        passed=all(check.passed for check in checks),
        issues=[check.issue for check in checks if not check.passed],
        confidence_score=calculate_confidence(checks)
    )
```

#### Moderación de Contenido
```python
async def moderate_content(text: str) -> ModerationResult:
    # OpenAI Moderation API
    response = await openai_client.moderations.create(input=text)
    
    if response.results[0].flagged:
        return ModerationResult(
            flagged=True,
            categories=response.results[0].categories,
            action="reject"
        )
    
    # Verificación adicional con reglas personalizadas
    custom_check = await custom_content_filter(text)
    
    return ModerationResult(
        flagged=custom_check.flagged,
        confidence=custom_check.confidence,
        action="approve" if not custom_check.flagged else "review"
    )
```

---

## 3. Evaluación y Scoring

### 3.1 Scoring Automático

#### MCQ (Multiple Choice Questions)
```python
def score_mcq_answer(question: TestItem, answer: str) -> ScoreResult:
    correct_option = question.metadata['correct_answer']
    is_correct = answer.lower() == correct_option.lower()
    
    return ScoreResult(
        score=1.0 if is_correct else 0.0,
        max_score=1.0,
        confidence=1.0,  # MCQ scoring is deterministic
        feedback="Correct answer" if is_correct else f"Correct answer was {correct_option}"
    )
```

#### Writing Assessment
```python
WRITING_SCORING_PROMPT = """
Evaluate this {difficulty_level} level writing response using the following rubric:

Response: "{student_response}"
Prompt: "{original_prompt}"

Scoring Criteria (0-5 scale each):
1. Content & Ideas: Relevance, development, examples
2. Organization: Structure, coherence, transitions
3. Language Use: Grammar, vocabulary, sentence variety
4. Mechanics: Spelling, punctuation, formatting

Provide:
- Individual scores for each criterion
- Overall score (average)
- Specific feedback for improvement
- Estimated difficulty level

Format as JSON with scores, overall_score, feedback, estimated_level
"""

async def score_writing_response(question: TestItem, response: str) -> ScoreResult:
    prompt = WRITING_SCORING_PROMPT.format(
        cefr_level=question.difficulty,
        student_response=response,
        original_prompt=question.prompt
    )
    
    ai_response = await openai_client.chat.completions.create(
        model="gpt-4-turbo",
        messages=[{"role": "user", "content": prompt}],
        temperature=0.1  # Low temperature for consistent scoring
    )
    
    result = json.loads(ai_response.choices[0].message.content)
    
    return ScoreResult(
        score=result['overall_score'],
        max_score=5.0,
        rubric_breakdown=result['scores'],
        feedback=result['feedback'],
        confidence=calculate_scoring_confidence(result),
        estimated_cefr=result['estimated_cefr']
    )
```

#### Speaking Assessment
```python
async def score_speaking_response(question: TestItem, audio_url: str) -> ScoreResult:
    # 1. Transcribir audio
    transcript = await transcribe_audio(audio_url)
    
    # 2. Análisis de pronunciación (Azure Speech)
    pronunciation_score = await analyze_pronunciation(audio_url, transcript)
    
    # 3. Evaluación de contenido con IA
    content_score = await score_speaking_content(question, transcript)
    
    # 4. Combinar scores
    overall_score = calculate_weighted_score({
        'content': content_score.score * 0.4,
        'pronunciation': pronunciation_score * 0.3,
        'fluency': content_score.fluency * 0.3
    })
    
    return ScoreResult(
        score=overall_score,
        max_score=5.0,
        rubric_breakdown={
            'content': content_score.score,
            'pronunciation': pronunciation_score,
            'fluency': content_score.fluency
        },
        transcript=transcript,
        confidence=min(content_score.confidence, 0.8)  # Speaking scoring less certain
    )
```

### 3.2 Detección de Plagio

```python
async def detect_plagiarism(response: str, question_id: str) -> PlagiarismResult:
    # 1. Comparación con respuestas previas del mismo examen
    internal_similarity = await check_internal_similarity(response, question_id)
    
    # 2. Verificación externa con Turnitin (si disponible)
    external_check = None
    if TURNITIN_ENABLED:
        external_check = await turnitin_similarity_check(response)
    
    # 3. Análisis de patrones sospechosos
    pattern_analysis = analyze_suspicious_patterns(response)
    
    max_similarity = max(
        internal_similarity.percentage,
        external_check.percentage if external_check else 0,
        pattern_analysis.suspicion_score * 100
    )
    
    return PlagiarismResult(
        similarity_percentage=max_similarity,
        flagged=max_similarity > PLAGIARISM_THRESHOLD,
        sources=compile_similarity_sources(internal_similarity, external_check),
        confidence=calculate_plagiarism_confidence(max_similarity)
    )
```

---

## 4. Gestión de Costos y Cuotas

### 4.1 Tracking de Uso

```python
class AIUsageTracker:
    def __init__(self, tenant_id: str):
        self.tenant_id = tenant_id
        
    async def track_generation_usage(self, request_type: str, count: int, tokens_used: int):
        cost = calculate_cost(request_type, tokens_used)
        
        await self.db.execute("""
            INSERT INTO ai_usage_logs (tenant_id, operation_type, tokens_used, cost_usd, created_at)
            VALUES ($1, $2, $3, $4, NOW())
        """, self.tenant_id, request_type, tokens_used, cost)
        
        # Actualizar contador total del tenant
        await self.db.execute("""
            UPDATE tenants 
            SET ia_tokens_used = ia_tokens_used + $1 
            WHERE tenant_id = $2
        """, tokens_used, self.tenant_id)
        
    async def check_quota_status(self) -> QuotaStatus:
        tenant = await self.get_tenant_info()
        plan_limits = PLAN_LIMITS[tenant.plan]
        
        usage_percentage = (tenant.ia_tokens_used / plan_limits.monthly_tokens) * 100
        
        return QuotaStatus(
            current_usage=tenant.ia_tokens_used,
            limit=plan_limits.monthly_tokens,
            percentage_used=usage_percentage,
            can_proceed=usage_percentage < 100,
            warning_level=self.get_warning_level(usage_percentage)
        )
```

### 4.2 Optimización de Costos

#### Caching Inteligente
```python
class AIResponseCache:
    def __init__(self):
        self.redis = Redis()
        
    async def get_cached_response(self, prompt_hash: str) -> Optional[dict]:
        cached = await self.redis.get(f"ai_cache:{prompt_hash}")
        if cached:
            return json.loads(cached)
        return None
        
    async def cache_response(self, prompt_hash: str, response: dict, ttl: int = 3600):
        await self.redis.setex(
            f"ai_cache:{prompt_hash}", 
            ttl, 
            json.dumps(response)
        )
        
    def generate_prompt_hash(self, prompt: str, parameters: dict) -> str:
        content = f"{prompt}:{json.dumps(parameters, sort_keys=True)}"
        return hashlib.sha256(content.encode()).hexdigest()
```

#### Batch Processing
```python
async def batch_generate_questions(requests: List[GenerationRequest]) -> List[GenerationResult]:
    # Agrupar requests similares para optimizar API calls
    batched_requests = group_similar_requests(requests)
    
    results = []
    for batch in batched_requests:
        # Procesar en lotes para reducir overhead
        batch_result = await process_generation_batch(batch)
        results.extend(batch_result)
    
    return results
```

---

## 5. Monitoreo y Observabilidad

### 5.1 Métricas Clave

```python
AI_METRICS = {
    "generation_latency": "Tiempo de respuesta para generación de contenido",
    "scoring_accuracy": "Precisión del scoring automático vs manual",
    "content_quality_score": "Calidad promedio del contenido generado",
    "moderation_flag_rate": "Porcentaje de contenido flaggeado",
    "cost_per_operation": "Costo promedio por operación de IA",
    "quota_utilization": "Uso de cuota por tenant",
    "error_rate": "Tasa de errores en servicios IA",
    "cache_hit_rate": "Efectividad del caching"
}
```

### 5.2 Alertas y Notificaciones

```python
class AIMonitoringService:
    async def check_service_health(self):
        checks = [
            self.check_openai_availability(),
            self.check_azure_speech_status(),
            self.check_response_times(),
            self.check_error_rates(),
            self.check_cost_anomalies()
        ]
        
        results = await asyncio.gather(*checks)
        
        for check_result in results:
            if check_result.status == "critical":
                await self.send_alert(check_result)
                
    async def send_alert(self, alert: AlertInfo):
        # Slack notification para equipo técnico
        await self.slack_client.send_message(
            channel="#ai-alerts",
            message=f"🚨 AI Service Alert: {alert.message}"
        )
        
        # Email para stakeholders críticos
        if alert.severity == "critical":
            await self.email_service.send_alert_email(alert)
```

---

## 6. Configuración y Personalización

### 6.1 Configuración por Tenant

```python
class TenantAIConfig:
    def __init__(self, tenant_id: str):
        self.tenant_id = tenant_id
        
    async def get_ai_preferences(self) -> AIPreferences:
        config = await self.db.fetch_one("""
            SELECT ai_preferences FROM tenant_configurations 
            WHERE tenant_id = $1
        """, self.tenant_id)
        
        return AIPreferences(
            preferred_difficulty_distribution=config.get('difficulty_dist', 'balanced'),
            content_style=config.get('style', 'neutral'),
            moderation_strictness=config.get('moderation', 'standard'),
            custom_rubrics=config.get('rubrics', {}),
            language_variants=config.get('language', ['en-US'])
        )
```

### 6.2 Rúbricas Personalizables

```python
CUSTOM_RUBRIC_TEMPLATE = {
    "writing": {
        "criteria": [
            {"name": "content", "weight": 0.3, "max_score": 5},
            {"name": "organization", "weight": 0.25, "max_score": 5},
            {"name": "language_use", "weight": 0.25, "max_score": 5},
            {"name": "mechanics", "weight": 0.2, "max_score": 5}
        ],
        "descriptors": {
            "5": "Excellent - Exceeds expectations",
            "4": "Good - Meets expectations",
            "3": "Satisfactory - Adequate performance",
            "2": "Needs Improvement - Below expectations", 
            "1": "Poor - Well below expectations"
        }
    }
}
```

---

## 7. Seguridad y Compliance

### 7.1 Protección de Datos
- **Data Minimization**: Solo enviar datos necesarios a APIs externas
- **Encryption**: Cifrado de respuestas de estudiantes en tránsito
- **Retention**: Políticas de retención para datos procesados por IA
- **Anonymization**: Eliminación de información personal antes del procesamiento

### 7.2 Audit Trail
```python
async def log_ai_operation(operation: AIOperation):
    await audit_logger.log({
        "timestamp": datetime.utcnow(),
        "tenant_id": operation.tenant_id,
        "operation_type": operation.type,
        "input_hash": hash_sensitive_data(operation.input),
        "model_used": operation.model,
        "tokens_consumed": operation.tokens,
        "cost_usd": operation.cost,
        "success": operation.success,
        "error": operation.error if not operation.success else None
    })
```

---

## Conclusión

La integración de IA en Arroyo University proporciona capacidades avanzadas de automatización educativa mientras mantiene control sobre calidad, costos y compliance. La arquitectura modular permite evolución continua y adaptación a nuevas tecnologías de IA, asegurando que la plataforma permanezca a la vanguardia de la innovación educativa.
