"""
Course enrollment and progress tracking models
"""

from datetime import datetime
from typing import Optional
from uuid import UUID
from enum import Enum

from sqlmodel import SQLModel, Field

from .base import TimestampMixin, TenantMixin


class EnrollmentStatus(str, Enum):
    """Enrollment status enumeration"""
    ACTIVE = "active"
    COMPLETED = "completed"
    DROPPED = "dropped"
    SUSPENDED = "suspended"


class ProgressStatus(str, Enum):
    """Progress status enumeration"""
    NOT_STARTED = "not_started"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    SKIPPED = "skipped"


class CourseEnrollmentBase(SQLModel):
    """Base course enrollment model"""
    enrollment_date: datetime = Field(default_factory=datetime.utcnow)
    status: EnrollmentStatus = Field(default=EnrollmentStatus.ACTIVE)
    completion_percentage: float = Field(default=0.0, ge=0.0, le=100.0)
    last_accessed_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None


class CourseEnrollment(CourseEnrollmentBase, TenantMixin, TimestampMixin, table=True):
    """Course enrollment table model"""
    __tablename__ = "course_enrollments"
    
    enrollment_id: UUID = Field(primary_key=True)
    user_id: UUID = Field(foreign_key="users.user_id", index=True)
    course_id: UUID = Field(foreign_key="courses.course_id", index=True)
    enrolled_by: Optional[UUID] = Field(foreign_key="users.user_id", default=None)
    certificate_issued: bool = Field(default=False)
    certificate_url: Optional[str] = Field(default=None, max_length=500)
    final_grade: Optional[float] = Field(default=None, ge=0.0, le=100.0)
    time_spent_minutes: int = Field(default=0)


class CourseEnrollmentCreate(SQLModel):
    """Course enrollment creation model"""
    user_id: UUID
    course_id: UUID


class CourseEnrollmentUpdate(SQLModel):
    """Course enrollment update model"""
    status: Optional[EnrollmentStatus] = None
    completion_percentage: Optional[float] = Field(default=None, ge=0.0, le=100.0)
    final_grade: Optional[float] = Field(default=None, ge=0.0, le=100.0)


class CourseEnrollmentResponse(CourseEnrollmentBase):
    """Course enrollment response model"""
    enrollment_id: UUID
    user_id: UUID
    course_id: UUID
    created_at: datetime
    updated_at: datetime
    course_title: Optional[str] = None
    instructor_name: Optional[str] = None
    user_name: Optional[str] = None


class UserProgressBase(SQLModel):
    """Base user progress model"""
    status: ProgressStatus = Field(default=ProgressStatus.NOT_STARTED)
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    time_spent_minutes: int = Field(default=0)
    attempts: int = Field(default=0)
    score: Optional[float] = Field(default=None, ge=0.0, le=100.0)


class UserProgress(UserProgressBase, TenantMixin, TimestampMixin, table=True):
    """User progress table model"""
    __tablename__ = "user_progress"
    
    progress_id: UUID = Field(primary_key=True)
    user_id: UUID = Field(foreign_key="users.user_id", index=True)
    course_id: UUID = Field(foreign_key="courses.course_id", index=True)
    module_id: Optional[UUID] = Field(foreign_key="course_modules.module_id", default=None, index=True)
    content_id: Optional[UUID] = Field(foreign_key="content_items.content_id", default=None, index=True)
    progress_data: Optional[dict] = Field(default_factory=dict, sa_column_kwargs={"type_": "JSONB"})


class UserProgressCreate(SQLModel):
    """User progress creation model"""
    user_id: UUID
    course_id: UUID
    module_id: Optional[UUID] = None
    content_id: Optional[UUID] = None


class UserProgressUpdate(SQLModel):
    """User progress update model"""
    status: Optional[ProgressStatus] = None
    time_spent_minutes: Optional[int] = None
    score: Optional[float] = Field(default=None, ge=0.0, le=100.0)
    progress_data: Optional[dict] = None


class UserProgressResponse(UserProgressBase):
    """User progress response model"""
    progress_id: UUID
    user_id: UUID
    course_id: UUID
    module_id: Optional[UUID] = None
    content_id: Optional[UUID] = None
    created_at: datetime
    updated_at: datetime
    content_title: Optional[str] = None
    module_title: Optional[str] = None


class LearningPath(SQLModel, table=True):
    """Learning path model"""
    __tablename__ = "learning_paths"
    
    path_id: UUID = Field(primary_key=True)
    user_id: UUID = Field(foreign_key="users.user_id", index=True)
    tenant_id: UUID = Field(foreign_key="tenants.tenant_id", index=True)
    name: str = Field(max_length=255)
    description: Optional[str] = None
    course_sequence: List[UUID] = Field(sa_column_kwargs={"type_": "JSONB"})
    is_custom: bool = Field(default=True)
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)


class LearningPathCreate(SQLModel):
    """Learning path creation model"""
    name: str = Field(max_length=255)
    description: Optional[str] = None
    course_sequence: List[UUID]


class SavedCourse(SQLModel, table=True):
    """Saved course model"""
    __tablename__ = "saved_courses"
    
    saved_id: UUID = Field(primary_key=True)
    user_id: UUID = Field(foreign_key="users.user_id", index=True)
    course_id: UUID = Field(foreign_key="courses.course_id", index=True)
    tenant_id: UUID = Field(foreign_key="tenants.tenant_id", index=True)
    saved_at: datetime = Field(default_factory=datetime.utcnow)
    notes: Optional[str] = Field(default=None, max_length=1000)


class SavedCourseCreate(SQLModel):
    """Saved course creation model"""
    course_id: UUID
    notes: Optional[str] = Field(default=None, max_length=1000)
