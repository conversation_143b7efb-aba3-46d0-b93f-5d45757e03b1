{"annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "gnetId": null, "graphTooltip": 0, "id": null, "links": [], "panels": [{"datasource": "Prometheus", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "ms"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}, "id": 1, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single"}}, "targets": [{"expr": "histogram_quantile(0.50, rate(http_request_duration_seconds_bucket{job=\"core-api\"}[5m])) * 1000", "interval": "", "legendFormat": "50th percentile", "refId": "A"}, {"expr": "histogram_quantile(0.95, rate(http_request_duration_seconds_bucket{job=\"core-api\"}[5m])) * 1000", "interval": "", "legendFormat": "95th percentile", "refId": "B"}, {"expr": "histogram_quantile(0.99, rate(http_request_duration_seconds_bucket{job=\"core-api\"}[5m])) * 1000", "interval": "", "legendFormat": "99th percentile", "refId": "C"}], "title": "API Response Time Percentiles", "type": "timeseries"}, {"datasource": "Prometheus", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "reqps"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}, "id": 2, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single"}}, "targets": [{"expr": "rate(http_requests_total{job=\"core-api\", status_code=~\"2..\"}[5m])", "interval": "", "legendFormat": "2xx Success", "refId": "A"}, {"expr": "rate(http_requests_total{job=\"core-api\", status_code=~\"4..\"}[5m])", "interval": "", "legendFormat": "4xx Client Error", "refId": "B"}, {"expr": "rate(http_requests_total{job=\"core-api\", status_code=~\"5..\"}[5m])", "interval": "", "legendFormat": "5xx Server Error", "refId": "C"}], "title": "API Request Rate by Status Code", "type": "timeseries"}, {"datasource": "Prometheus", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}, "id": 3, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single"}}, "targets": [{"expr": "postgres_connections_active", "interval": "", "legendFormat": "Active Connections", "refId": "A"}, {"expr": "postgres_connections_idle", "interval": "", "legendFormat": "Idle Connections", "refId": "B"}, {"expr": "postgres_connections_max", "interval": "", "legendFormat": "Max Connections", "refId": "C"}], "title": "Database Connection Pool", "type": "timeseries"}, {"datasource": "Prometheus", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "ms"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}, "id": 4, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single"}}, "targets": [{"expr": "histogram_quantile(0.50, rate(postgres_query_duration_seconds_bucket[5m])) * 1000", "interval": "", "legendFormat": "50th percentile", "refId": "A"}, {"expr": "histogram_quantile(0.95, rate(postgres_query_duration_seconds_bucket[5m])) * 1000", "interval": "", "legendFormat": "95th percentile", "refId": "B"}, {"expr": "histogram_quantile(0.99, rate(postgres_query_duration_seconds_bucket[5m])) * 1000", "interval": "", "legendFormat": "99th percentile", "refId": "C"}], "title": "Database Query Performance", "type": "timeseries"}], "schemaVersion": 27, "style": "dark", "tags": ["application", "performance", "api"], "templating": {"list": []}, "time": {"from": "now-1h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Arroyo University - API Performance", "uid": "arroyo-api-performance", "version": 1, "description": "Detailed API performance metrics including response times, error rates, and database performance"}