<!DOCTYPE html>
<html lang="es" class="h-full bg-gray-50">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Leaderboard - Arroyo University</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Inter', sans-serif; }
        .trophy-gold { color: #FFD700; }
        .trophy-silver { color: #C0C0C0; }
        .trophy-bronze { color: #CD7F32; }
    </style>
</head>
<body class="h-full">
    <div class="flex h-full">
        <!-- Sidebar -->
        <div class="hidden md:flex md:w-64 md:flex-col">
            <div class="flex flex-col flex-grow pt-5 bg-white border-r border-gray-200 overflow-y-auto">
                <!-- Logo -->
                <div class="flex items-center flex-shrink-0 px-4">
                    <div class="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                        <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M10.394 2.08a1 1 0 00-.788 0l-7 3a1 1 0 000 1.84L5.25 8.051a.999.999 0 01.356-.257l4-1.714a1 1 0 11.788 1.84L7.667 9.088l1.94.831a1 1 0 00.787 0l7-3a1 1 0 000-1.838l-7-3z"/>
                        </svg>
                    </div>
                    <h1 class="ml-3 text-lg font-semibold text-gray-900">Universidad Ejemplo</h1>
                </div>

                <!-- Navigation -->
                <nav class="mt-8 flex-1 px-2 space-y-1">
                    <a href="43_user_home_dashboard.html" class="text-gray-600 hover:bg-gray-50 hover:text-gray-900 group flex items-center px-2 py-2 text-sm font-medium rounded-md">
                        <svg class="text-gray-400 mr-3 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"/>
                        </svg>
                        Home
                    </a>

                    <a href="39_course_marketplace.html" class="text-gray-600 hover:bg-gray-50 hover:text-gray-900 group flex items-center px-2 py-2 text-sm font-medium rounded-md">
                        <svg class="text-gray-400 mr-3 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"/>
                        </svg>
                        Marketplace
                    </a>

                    <a href="47_career_path_marketplace.html" class="text-gray-600 hover:bg-gray-50 hover:text-gray-900 group flex items-center px-2 py-2 text-sm font-medium rounded-md">
                        <svg class="text-gray-400 mr-3 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v4a2 2 0 01-2 2h-2a2 2 0 00-2-2z"/>
                        </svg>
                        Career Paths
                    </a>

                    <a href="#" class="bg-blue-100 text-blue-700 group flex items-center px-2 py-2 text-sm font-medium rounded-md">
                        <svg class="text-blue-500 mr-3 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"/>
                        </svg>
                        Leaderboard
                    </a>

                    <a href="#" class="text-gray-600 hover:bg-gray-50 hover:text-gray-900 group flex items-center px-2 py-2 text-sm font-medium rounded-md">
                        <svg class="text-gray-400 mr-3 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"/>
                        </svg>
                        Mis Cursos
                    </a>

                    <!-- Profile Section -->
                    <div class="pt-4 mt-4 border-t border-gray-200">
                        <a href="#" class="text-gray-600 hover:bg-gray-50 hover:text-gray-900 group flex items-center px-2 py-2 text-sm font-medium rounded-md">
                            <svg class="text-gray-400 mr-3 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                            </svg>
                            Perfil
                        </a>
                    </div>
                </nav>

                <!-- User Info -->
                <div class="flex-shrink-0 flex border-t border-gray-200 p-4">
                    <div class="flex items-center">
                        <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                            <span class="text-sm font-medium text-white">AJ</span>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm font-medium text-gray-700">Alejandro</p>
                            <p class="text-xs text-gray-500">Usuario</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="flex flex-col flex-1 overflow-hidden">
            <!-- Top Header -->
            <header class="bg-white shadow-sm border-b border-gray-200">
                <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div class="flex justify-between items-center py-4">
                        <div>
                            <h1 class="text-2xl font-bold text-gray-900">Leaderboard</h1>
                            <p class="text-gray-600">Compite con otros usuarios y ve tu progreso en la comunidad</p>
                        </div>
                        <div class="flex items-center space-x-4">
                            <!-- Period Selector -->
                            <div class="flex bg-gray-100 rounded-lg p-1">
                                <button onclick="switchPeriod('weekly')" id="weeklyBtn" 
                                        class="px-3 py-1 text-sm font-medium rounded-md bg-white text-gray-900 shadow-sm">
                                    Semanal
                                </button>
                                <button onclick="switchPeriod('monthly')" id="monthlyBtn" 
                                        class="px-3 py-1 text-sm font-medium rounded-md text-gray-600 hover:text-gray-900">
                                    Mensual
                                </button>
                                <button onclick="switchPeriod('alltime')" id="alltimeBtn" 
                                        class="px-3 py-1 text-sm font-medium rounded-md text-gray-600 hover:text-gray-900">
                                    Total
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </header>

            <!-- Main Content Area -->
            <main class="flex-1 overflow-y-auto">
                <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
                    <!-- Category Tabs -->
                    <div class="mb-8">
                        <div class="border-b border-gray-200">
                            <nav class="-mb-px flex space-x-8">
                                <button onclick="switchCategory('student')" id="studentTab" 
                                        class="border-blue-500 text-blue-600 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                                    🎓 Estudiantes
                                </button>
                                <button onclick="switchCategory('creator')" id="creatorTab" 
                                        class="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                                    👨‍🏫 Creadores
                                </button>
                            </nav>
                        </div>
                    </div>

                    <!-- My Position Card -->
                    <div class="mb-8 bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-6">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-4">
                                <div class="w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center">
                                    <span class="text-white font-bold">AJ</span>
                                </div>
                                <div>
                                    <h3 class="text-lg font-semibold text-gray-900">Tu Posición</h3>
                                    <p class="text-sm text-gray-600" id="myPosition">Posición #47 en Estudiantes (Semanal)</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <div class="text-2xl font-bold text-blue-600" id="myScore">2,450</div>
                                <div class="text-sm text-gray-500">puntos</div>
                                <div class="text-xs text-green-600 mt-1" id="myTrend">↗ +180 esta semana</div>
                            </div>
                        </div>
                    </div>

                    <!-- Leaderboard Content -->
                    <div id="leaderboardContent">
                        <!-- Student Leaderboard (Default) -->
                        <div id="studentLeaderboard" class="space-y-4">
                            <!-- Top 3 Podium -->
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
                                <!-- 2nd Place -->
                                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 text-center order-2 md:order-1">
                                    <div class="trophy-silver text-3xl mb-2">🥈</div>
                                    <div class="w-16 h-16 bg-gray-400 rounded-full mx-auto mb-3 flex items-center justify-center">
                                        <span class="text-white font-bold text-lg">MR</span>
                                    </div>
                                    <h3 class="font-semibold text-gray-900">María Rodríguez</h3>
                                    <p class="text-sm text-gray-500 mb-2">Experta en Data Science</p>
                                    <div class="text-xl font-bold text-gray-700">4,890</div>
                                    <div class="text-sm text-gray-500">puntos</div>
                                    <div class="mt-3 flex justify-center space-x-4 text-xs text-gray-500">
                                        <span>📚 23 cursos</span>
                                        <span>💬 156 posts</span>
                                    </div>
                                </div>

                                <!-- 1st Place -->
                                <div class="bg-gradient-to-b from-yellow-50 to-yellow-100 rounded-lg shadow-md border-2 border-yellow-300 p-6 text-center order-1 md:order-2">
                                    <div class="trophy-gold text-4xl mb-2">👑</div>
                                    <div class="w-20 h-20 bg-yellow-500 rounded-full mx-auto mb-3 flex items-center justify-center">
                                        <span class="text-white font-bold text-xl">CG</span>
                                    </div>
                                    <h3 class="font-bold text-gray-900 text-lg">Carlos García</h3>
                                    <p class="text-sm text-gray-600 mb-2">DevOps Master</p>
                                    <div class="text-2xl font-bold text-yellow-600">5,240</div>
                                    <div class="text-sm text-gray-600">puntos</div>
                                    <div class="mt-3 flex justify-center space-x-4 text-xs text-gray-600">
                                        <span>📚 31 cursos</span>
                                        <span>💬 203 posts</span>
                                    </div>
                                </div>

                                <!-- 3rd Place -->
                                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 text-center order-3">
                                    <div class="trophy-bronze text-3xl mb-2">🥉</div>
                                    <div class="w-16 h-16 bg-orange-400 rounded-full mx-auto mb-3 flex items-center justify-center">
                                        <span class="text-white font-bold text-lg">LP</span>
                                    </div>
                                    <h3 class="font-semibold text-gray-900">Luis Pérez</h3>
                                    <p class="text-sm text-gray-500 mb-2">Full Stack Developer</p>
                                    <div class="text-xl font-bold text-orange-600">4,120</div>
                                    <div class="text-sm text-gray-500">puntos</div>
                                    <div class="mt-3 flex justify-center space-x-4 text-xs text-gray-500">
                                        <span>📚 19 cursos</span>
                                        <span>💬 98 posts</span>
                                    </div>
                                </div>
                            </div>

                            <!-- Rest of Leaderboard -->
                            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                                <div class="px-6 py-4 border-b border-gray-200">
                                    <h3 class="text-lg font-semibold text-gray-900">Ranking Completo</h3>
                                </div>
                                <div class="divide-y divide-gray-200">
                                    <!-- Position 4 -->
                                    <div class="px-6 py-4 flex items-center justify-between hover:bg-gray-50">
                                        <div class="flex items-center space-x-4">
                                            <div class="text-lg font-bold text-gray-500 w-8">#4</div>
                                            <div class="w-10 h-10 bg-purple-500 rounded-full flex items-center justify-center">
                                                <span class="text-white font-medium">AS</span>
                                            </div>
                                            <div>
                                                <h4 class="font-medium text-gray-900">Ana Silva</h4>
                                                <p class="text-sm text-gray-500">UX Designer</p>
                                            </div>
                                        </div>
                                        <div class="text-right">
                                            <div class="text-lg font-semibold text-gray-900">3,890</div>
                                            <div class="text-sm text-gray-500">puntos</div>
                                        </div>
                                    </div>

                                    <!-- Position 5 -->
                                    <div class="px-6 py-4 flex items-center justify-between hover:bg-gray-50">
                                        <div class="flex items-center space-x-4">
                                            <div class="text-lg font-bold text-gray-500 w-8">#5</div>
                                            <div class="w-10 h-10 bg-green-500 rounded-full flex items-center justify-center">
                                                <span class="text-white font-medium">JM</span>
                                            </div>
                                            <div>
                                                <h4 class="font-medium text-gray-900">Jorge Martínez</h4>
                                                <p class="text-sm text-gray-500">Cybersecurity Analyst</p>
                                            </div>
                                        </div>
                                        <div class="text-right">
                                            <div class="text-lg font-semibold text-gray-900">3,650</div>
                                            <div class="text-sm text-gray-500">puntos</div>
                                        </div>
                                    </div>

                                    <!-- More positions... -->
                                    <div class="px-6 py-4 flex items-center justify-between hover:bg-gray-50">
                                        <div class="flex items-center space-x-4">
                                            <div class="text-lg font-bold text-gray-500 w-8">#6</div>
                                            <div class="w-10 h-10 bg-pink-500 rounded-full flex items-center justify-center">
                                                <span class="text-white font-medium">SF</span>
                                            </div>
                                            <div>
                                                <h4 class="font-medium text-gray-900">Sofía Fernández</h4>
                                                <p class="text-sm text-gray-500">Product Manager</p>
                                            </div>
                                        </div>
                                        <div class="text-right">
                                            <div class="text-lg font-semibold text-gray-900">3,420</div>
                                            <div class="text-sm text-gray-500">puntos</div>
                                        </div>
                                    </div>

                                    <!-- User's position if not in top -->
                                    <div class="px-6 py-4 flex items-center justify-between bg-blue-50 border-l-4 border-blue-500">
                                        <div class="flex items-center space-x-4">
                                            <div class="text-lg font-bold text-blue-600 w-8">#47</div>
                                            <div class="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center">
                                                <span class="text-white font-medium">AJ</span>
                                            </div>
                                            <div>
                                                <h4 class="font-medium text-gray-900">Alejandro (Tú)</h4>
                                                <p class="text-sm text-gray-500">Estudiante</p>
                                            </div>
                                        </div>
                                        <div class="text-right">
                                            <div class="text-lg font-semibold text-blue-600">2,450</div>
                                            <div class="text-sm text-gray-500">puntos</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Creator Leaderboard (Hidden by default) -->
                        <div id="creatorLeaderboard" class="space-y-4 hidden">
                            <!-- Top 3 Podium for Creators -->
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
                                <!-- 2nd Place Creator -->
                                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 text-center order-2 md:order-1">
                                    <div class="trophy-silver text-3xl mb-2">🥈</div>
                                    <div class="w-16 h-16 bg-gray-400 rounded-full mx-auto mb-3 flex items-center justify-center">
                                        <span class="text-white font-bold text-lg">DR</span>
                                    </div>
                                    <h3 class="font-semibold text-gray-900">Dr. Roberto Díaz</h3>
                                    <p class="text-sm text-gray-500 mb-2">Experto en AI</p>
                                    <div class="text-xl font-bold text-gray-700">8,920</div>
                                    <div class="text-sm text-gray-500">puntos</div>
                                    <div class="mt-3 flex justify-center space-x-4 text-xs text-gray-500">
                                        <span>📚 12 cursos</span>
                                        <span>⭐ 4.9 rating</span>
                                    </div>
                                </div>

                                <!-- 1st Place Creator -->
                                <div class="bg-gradient-to-b from-yellow-50 to-yellow-100 rounded-lg shadow-md border-2 border-yellow-300 p-6 text-center order-1 md:order-2">
                                    <div class="trophy-gold text-4xl mb-2">👑</div>
                                    <div class="w-20 h-20 bg-yellow-500 rounded-full mx-auto mb-3 flex items-center justify-center">
                                        <span class="text-white font-bold text-xl">MG</span>
                                    </div>
                                    <h3 class="font-bold text-gray-900 text-lg">Dr. María González</h3>
                                    <p class="text-sm text-gray-600 mb-2">Experta en DevOps</p>
                                    <div class="text-2xl font-bold text-yellow-600">12,340</div>
                                    <div class="text-sm text-gray-600">puntos</div>
                                    <div class="mt-3 flex justify-center space-x-4 text-xs text-gray-600">
                                        <span>📚 18 cursos</span>
                                        <span>⭐ 4.8 rating</span>
                                    </div>
                                </div>

                                <!-- 3rd Place Creator -->
                                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 text-center order-3">
                                    <div class="trophy-bronze text-3xl mb-2">🥉</div>
                                    <div class="w-16 h-16 bg-orange-400 rounded-full mx-auto mb-3 flex items-center justify-center">
                                        <span class="text-white font-bold text-lg">JL</span>
                                    </div>
                                    <h3 class="font-semibold text-gray-900">Juan López</h3>
                                    <p class="text-sm text-gray-500 mb-2">Instructor de Programación</p>
                                    <div class="text-xl font-bold text-orange-600">7,680</div>
                                    <div class="text-sm text-gray-500">puntos</div>
                                    <div class="mt-3 flex justify-center space-x-4 text-xs text-gray-500">
                                        <span>📚 9 cursos</span>
                                        <span>⭐ 4.7 rating</span>
                                    </div>
                                </div>
                            </div>

                            <!-- Creator Rankings -->
                            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                                <div class="px-6 py-4 border-b border-gray-200">
                                    <h3 class="text-lg font-semibold text-gray-900">Ranking de Creadores</h3>
                                </div>
                                <div class="divide-y divide-gray-200">
                                    <div class="px-6 py-4 flex items-center justify-between hover:bg-gray-50">
                                        <div class="flex items-center space-x-4">
                                            <div class="text-lg font-bold text-gray-500 w-8">#4</div>
                                            <div class="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center">
                                                <span class="text-white font-medium">CP</span>
                                            </div>
                                            <div>
                                                <h4 class="font-medium text-gray-900">Carmen Pérez</h4>
                                                <p class="text-sm text-gray-500">Instructora de Marketing</p>
                                            </div>
                                        </div>
                                        <div class="text-right">
                                            <div class="text-lg font-semibold text-gray-900">6,890</div>
                                            <div class="text-sm text-gray-500">puntos</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Scoring Information -->
                    <div class="mt-8 bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">¿Cómo se calculan los puntos?</h3>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- Student Scoring -->
                            <div>
                                <h4 class="font-medium text-gray-900 mb-3 flex items-center">
                                    🎓 Puntos de Estudiante
                                </h4>
                                <div class="space-y-2 text-sm text-gray-600">
                                    <div class="flex justify-between">
                                        <span>Completar curso básico</span>
                                        <span class="font-medium">50-100 pts</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span>Completar curso intermedio</span>
                                        <span class="font-medium">100-200 pts</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span>Completar curso avanzado</span>
                                        <span class="font-medium">200-300 pts</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span>Curso en Career Path activo</span>
                                        <span class="font-medium text-green-600">+50% bonus</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span>Post en foro</span>
                                        <span class="font-medium">10 pts</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span>Like recibido en post</span>
                                        <span class="font-medium">5 pts</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span>Calificar curso completado</span>
                                        <span class="font-medium">15 pts</span>
                                    </div>
                                </div>
                            </div>

                            <!-- Creator Scoring -->
                            <div>
                                <h4 class="font-medium text-gray-900 mb-3 flex items-center">
                                    👨‍🏫 Puntos de Creador
                                </h4>
                                <div class="space-y-2 text-sm text-gray-600">
                                    <div class="flex justify-between">
                                        <span>Crear curso (base)</span>
                                        <span class="font-medium">200 pts</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span>Por cada like del curso</span>
                                        <span class="font-medium">10 pts</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span>Dificultad validada por experto</span>
                                        <span class="font-medium text-blue-600">+25% bonus</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span>Rating promedio 4.5+</span>
                                        <span class="font-medium text-green-600">+50% bonus</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span>Curso completado por estudiante</span>
                                        <span class="font-medium">25 pts</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span>Review de experto positivo</span>
                                        <span class="font-medium">100 pts</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script>
        let currentPeriod = 'weekly';
        let currentCategory = 'student';

        function switchPeriod(period) {
            currentPeriod = period;

            // Update button styles
            document.querySelectorAll('[id$="Btn"]').forEach(btn => {
                btn.className = 'px-3 py-1 text-sm font-medium rounded-md text-gray-600 hover:text-gray-900';
            });
            document.getElementById(period + 'Btn').className = 'px-3 py-1 text-sm font-medium rounded-md bg-white text-gray-900 shadow-sm';

            // Update content based on period
            updateLeaderboardData();
        }

        function switchCategory(category) {
            currentCategory = category;

            // Update tab styles
            document.getElementById('studentTab').className =
                category === 'student'
                ? 'border-blue-500 text-blue-600 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm';

            document.getElementById('creatorTab').className =
                category === 'creator'
                ? 'border-blue-500 text-blue-600 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm';

            // Show/hide leaderboards
            document.getElementById('studentLeaderboard').style.display = category === 'student' ? 'block' : 'none';
            document.getElementById('creatorLeaderboard').style.display = category === 'creator' ? 'block' : 'none';

            updateMyPosition();
        }

        function updateLeaderboardData() {
            // Simulate data update based on period
            const periodText = {
                'weekly': 'Semanal',
                'monthly': 'Mensual',
                'alltime': 'Total'
            };

            const scores = {
                'weekly': { student: 2450, creator: 890 },
                'monthly': { student: 8920, creator: 3240 },
                'alltime': { student: 24680, creator: 12450 }
            };

            document.getElementById('myScore').textContent = scores[currentPeriod][currentCategory].toLocaleString();
            updateMyPosition();
        }

        function updateMyPosition() {
            const positions = {
                'weekly': { student: 47, creator: 23 },
                'monthly': { student: 34, creator: 18 },
                'alltime': { student: 156, creator: 45 }
            };

            const categoryText = currentCategory === 'student' ? 'Estudiantes' : 'Creadores';
            const periodText = {
                'weekly': 'Semanal',
                'monthly': 'Mensual',
                'alltime': 'Total'
            };

            const position = positions[currentPeriod][currentCategory];
            document.getElementById('myPosition').textContent =
                `Posición #${position} en ${categoryText} (${periodText[currentPeriod]})`;

            // Update trend
            const trends = {
                'weekly': '↗ +180 esta semana',
                'monthly': '↗ +520 este mes',
                'alltime': '↗ +2,340 total'
            };
            document.getElementById('myTrend').textContent = trends[currentPeriod];
        }

        // Initialize
        updateMyPosition();
    </script>
</body>
</html>
