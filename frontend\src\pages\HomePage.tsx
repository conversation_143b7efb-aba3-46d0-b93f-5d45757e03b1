import React from 'react';
import { CheckCircle, BookOpen, Star, Zap } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/Card';
import { useAuthStore } from '@/store/authStore';

const stats = [
  {
    name: '<PERSON>ursos <PERSON>tad<PERSON>',
    value: '8',
    icon: CheckCircle,
    color: 'text-green-600',
    bgColor: 'bg-green-100',
  },
  {
    name: 'En Progreso',
    value: '3',
    icon: BookOpen,
    color: 'text-blue-600',
    bgColor: 'bg-blue-100',
  },
  {
    name: 'Guardados',
    value: '12',
    icon: Star,
    color: 'text-yellow-600',
    bgColor: 'bg-yellow-100',
  },
  {
    name: '<PERSON><PERSON>',
    value: '15',
    icon: Zap,
    color: 'text-purple-600',
    bgColor: 'bg-purple-100',
  },
];

const recentCourses = [
  {
    id: '1',
    title: 'DevOps Fundamentals',
    progress: 75,
    category: 'Tecnología',
    lastAccessed: '2 horas',
  },
  {
    id: '2',
    title: 'Introducción a la IA',
    progress: 45,
    category: 'Tecnología',
    lastAccessed: '1 día',
  },
  {
    id: '3',
    title: 'Prompt Engineering',
    progress: 90,
    category: 'Tecnología',
    lastAccessed: '3 días',
  },
];

const recommendedCourses = [
  {
    id: '4',
    title: 'Python para Principiantes',
    category: 'Tecnología',
    rating: 4.8,
    students: 312,
  },
  {
    id: '5',
    title: 'Ciencia de Datos',
    category: 'Tecnología',
    rating: 4.6,
    students: 187,
  },
  {
    id: '6',
    title: 'English B2 Intermediate',
    category: 'Idiomas',
    rating: 4.9,
    students: 456,
  },
];

export default function HomePage() {
  const { user } = useAuthStore();

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Welcome Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900">
          ¡Bienvenido de vuelta, {user?.firstName || 'Usuario'}!
        </h1>
        <p className="text-gray-600 mt-2">
          Continúa tu progreso de aprendizaje
        </p>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {stats.map((stat) => {
          const Icon = stat.icon;
          return (
            <Card key={stat.name}>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <div className={`flex-shrink-0 p-3 rounded-lg ${stat.bgColor}`}>
                    <Icon className={`h-6 w-6 ${stat.color}`} />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-500 truncate">
                      {stat.name}
                    </p>
                    <p className="text-2xl font-semibold text-gray-900">
                      {stat.value}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Recent Courses */}
        <Card>
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900">
              Cursos Recientes
            </h3>
          </div>
          <CardContent className="p-6">
            <div className="space-y-4">
              {recentCourses.map((course) => (
                <div key={course.id} className="flex items-center justify-between">
                  <div className="flex-1">
                    <h4 className="text-sm font-medium text-gray-900">
                      {course.title}
                    </h4>
                    <p className="text-xs text-gray-500">
                      {course.category} • Último acceso: {course.lastAccessed}
                    </p>
                    <div className="mt-2">
                      <div className="flex items-center">
                        <div className="flex-1 bg-gray-200 rounded-full h-2">
                          <div
                            className="bg-blue-600 h-2 rounded-full"
                            style={{ width: `${course.progress}%` }}
                          />
                        </div>
                        <span className="ml-2 text-xs text-gray-500">
                          {course.progress}%
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Recommended Courses */}
        <Card>
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900">
              Cursos Recomendados
            </h3>
          </div>
          <CardContent className="p-6">
            <div className="space-y-4">
              {recommendedCourses.map((course) => (
                <div key={course.id} className="flex items-center justify-between">
                  <div className="flex-1">
                    <h4 className="text-sm font-medium text-gray-900">
                      {course.title}
                    </h4>
                    <p className="text-xs text-gray-500 mb-2">
                      {course.category}
                    </p>
                    <div className="flex items-center text-xs text-gray-500">
                      <Star className="w-3 h-3 mr-1 fill-current text-yellow-400" />
                      {course.rating}
                      <span className="mx-2">•</span>
                      <span>{course.students} estudiantes</span>
                    </div>
                  </div>
                  <button className="ml-4 px-3 py-1 bg-blue-600 text-white text-xs rounded hover:bg-blue-700">
                    Ver
                  </button>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <div className="mt-8">
        <h3 className="text-lg font-medium text-gray-900 mb-4">
          Acciones Rápidas
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card className="hover:shadow-md transition-shadow cursor-pointer">
            <CardContent className="p-6 text-center">
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                <BookOpen className="w-6 h-6 text-blue-600" />
              </div>
              <h4 className="text-sm font-medium text-gray-900 mb-1">
                Explorar Cursos
              </h4>
              <p className="text-xs text-gray-500">
                Descubre nuevos cursos en el marketplace
              </p>
            </CardContent>
          </Card>

          <Card className="hover:shadow-md transition-shadow cursor-pointer">
            <CardContent className="p-6 text-center">
              <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                <Zap className="w-6 h-6 text-green-600" />
              </div>
              <h4 className="text-sm font-medium text-gray-900 mb-1">
                Career Paths
              </h4>
              <p className="text-xs text-gray-500">
                Planifica tu carrera profesional
              </p>
            </CardContent>
          </Card>

          <Card className="hover:shadow-md transition-shadow cursor-pointer">
            <CardContent className="p-6 text-center">
              <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                <Star className="w-6 h-6 text-purple-600" />
              </div>
              <h4 className="text-sm font-medium text-gray-900 mb-1">
                Leaderboard
              </h4>
              <p className="text-xs text-gray-500">
                Ve tu posición en el ranking
              </p>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
