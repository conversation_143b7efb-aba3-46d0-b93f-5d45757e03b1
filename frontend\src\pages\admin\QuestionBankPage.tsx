import React, { useState } from 'react';
import { Plus, Search, Filter, Edit, Trash2, Copy, Upload, Download, Sparkles } from 'lucide-react';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Badge } from '@/components/ui/Badge';
import { DataTable, Column } from '@/components/ui/DataTable';
import { Mo<PERSON>, ModalHeader, ModalFooter } from '@/components/ui/Modal';
import { FileUpload } from '@/components/ui/FileUpload';
import { Dropdown, DropdownItem, DropdownSeparator } from '@/components/ui/Dropdown';
import { Question } from '@/services/examService';
import toast from 'react-hot-toast';

const mockQuestions: Question[] = [
  {
    id: '1',
    type: 'multiple-choice',
    title: 'Principios de DevOps',
    content: '¿Cuál de los siguientes es un principio fundamental de DevOps?',
    options: ['Automatización', 'Colaboración', 'Monitoreo continuo', 'Todas las anteriores'],
    correctAnswer: 'Todas las anteriores',
    points: 10,
    difficulty: 'medium',
    category: 'DevOps',
    tags: ['principios', 'fundamentos'],
    explanation: 'DevOps se basa en automatización, colaboración y monitoreo continuo.',
    createdBy: 'prof-garcia',
    createdAt: '2024-01-01',
    updatedAt: '2024-01-01',
  },
  {
    id: '2',
    type: 'essay',
    title: 'CI/CD Pipeline',
    content: 'Explica los beneficios de implementar un pipeline de CI/CD en un proyecto de software.',
    points: 20,
    timeLimit: 600,
    difficulty: 'hard',
    category: 'DevOps',
    tags: ['ci-cd', 'pipeline', 'automatización'],
    createdBy: 'prof-garcia',
    createdAt: '2024-01-02',
    updatedAt: '2024-01-02',
  },
  {
    id: '3',
    type: 'true-false',
    title: 'Docker Containers',
    content: 'Los contenedores Docker comparten el kernel del sistema operativo host.',
    correctAnswer: 'true',
    points: 5,
    difficulty: 'easy',
    category: 'Containerización',
    tags: ['docker', 'contenedores'],
    explanation: 'Los contenedores comparten el kernel del host, a diferencia de las VMs.',
    createdBy: 'dr-martinez',
    createdAt: '2024-01-03',
    updatedAt: '2024-01-03',
  },
];

const questionTypes = [
  { value: 'multiple-choice', label: 'Opción Múltiple' },
  { value: 'essay', label: 'Ensayo' },
  { value: 'true-false', label: 'Verdadero/Falso' },
  { value: 'fill-blank', label: 'Llenar Espacios' },
  { value: 'multimedia', label: 'Multimedia' },
];

const difficulties = [
  { value: 'easy', label: 'Fácil', color: 'green' },
  { value: 'medium', label: 'Medio', color: 'yellow' },
  { value: 'hard', label: 'Difícil', color: 'red' },
];

const categories = ['DevOps', 'Containerización', 'IA', 'Programación', 'Matemáticas', 'Idiomas'];

export default function QuestionBankPage() {
  const [questions, setQuestions] = useState<Question[]>(mockQuestions);
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isImportModalOpen, setIsImportModalOpen] = useState(false);
  const [isAIModalOpen, setIsAIModalOpen] = useState(false);
  const [selectedQuestions, setSelectedQuestions] = useState<string[]>([]);

  const [newQuestion, setNewQuestion] = useState({
    type: 'multiple-choice' as const,
    title: '',
    content: '',
    options: ['', '', '', ''],
    correctAnswer: '',
    points: 10,
    difficulty: 'medium' as const,
    category: '',
    tags: '',
    explanation: '',
  });

  const [aiRequest, setAiRequest] = useState({
    topic: '',
    difficulty: 'medium' as const,
    questionType: 'multiple-choice' as const,
    count: 5,
    language: 'español',
    additionalInstructions: '',
  });

  const handleCreateQuestion = () => {
    if (!newQuestion.title || !newQuestion.content) {
      toast.error('Por favor, completa los campos obligatorios');
      return;
    }

    const question: Question = {
      id: Date.now().toString(),
      type: newQuestion.type,
      title: newQuestion.title,
      content: newQuestion.content,
      options: newQuestion.type === 'multiple-choice' ? newQuestion.options.filter(o => o.trim()) : undefined,
      correctAnswer: newQuestion.correctAnswer,
      points: newQuestion.points,
      difficulty: newQuestion.difficulty,
      category: newQuestion.category || 'General',
      tags: newQuestion.tags.split(',').map(t => t.trim()).filter(t => t),
      explanation: newQuestion.explanation,
      createdBy: 'current-user',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    setQuestions([...questions, question]);
    setIsCreateModalOpen(false);
    setNewQuestion({
      type: 'multiple-choice',
      title: '',
      content: '',
      options: ['', '', '', ''],
      correctAnswer: '',
      points: 10,
      difficulty: 'medium',
      category: '',
      tags: '',
      explanation: '',
    });

    toast.success('Pregunta creada exitosamente');
  };

  const handleGenerateWithAI = async () => {
    if (!aiRequest.topic) {
      toast.error('Por favor, ingresa un tema');
      return;
    }

    try {
      // Simulate AI generation
      toast.loading('Generando preguntas con IA...', { duration: 2000 });
      
      setTimeout(() => {
        const generatedQuestions: Question[] = Array.from({ length: aiRequest.count }, (_, i) => ({
          id: `ai-${Date.now()}-${i}`,
          type: aiRequest.questionType,
          title: `${aiRequest.topic} - Pregunta ${i + 1}`,
          content: `Pregunta generada por IA sobre ${aiRequest.topic}`,
          options: aiRequest.questionType === 'multiple-choice' ? 
            ['Opción A', 'Opción B', 'Opción C', 'Opción D'] : undefined,
          correctAnswer: aiRequest.questionType === 'multiple-choice' ? 'Opción A' : 'Respuesta generada',
          points: 10,
          difficulty: aiRequest.difficulty,
          category: aiRequest.topic,
          tags: ['ai-generated', aiRequest.topic.toLowerCase()],
          explanation: 'Explicación generada por IA',
          createdBy: 'ai-assistant',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        }));

        setQuestions([...questions, ...generatedQuestions]);
        setIsAIModalOpen(false);
        toast.success(`${aiRequest.count} preguntas generadas exitosamente`);
      }, 2000);
    } catch (error) {
      toast.error('Error al generar preguntas con IA');
    }
  };

  const handleImportQuestions = (files: File[]) => {
    if (files.length === 0) return;
    
    toast.success(`Importando ${files.length} archivo(s)...`);
    // Simulate import process
    setTimeout(() => {
      toast.success('Preguntas importadas exitosamente');
      setIsImportModalOpen(false);
    }, 1500);
  };

  const handleExportQuestions = () => {
    if (selectedQuestions.length === 0) {
      toast.error('Selecciona al menos una pregunta para exportar');
      return;
    }
    toast.success(`Exportando ${selectedQuestions.length} preguntas...`);
  };

  const handleDeleteQuestion = (questionId: string) => {
    setQuestions(questions.filter(q => q.id !== questionId));
    toast.success('Pregunta eliminada exitosamente');
  };

  const handleDuplicateQuestion = (questionId: string) => {
    const question = questions.find(q => q.id === questionId);
    if (question) {
      const duplicated = {
        ...question,
        id: `${question.id}-copy-${Date.now()}`,
        title: `${question.title} (Copia)`,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };
      setQuestions([...questions, duplicated]);
      toast.success('Pregunta duplicada exitosamente');
    }
  };

  const getDifficultyBadge = (difficulty: Question['difficulty']) => {
    const config = difficulties.find(d => d.value === difficulty);
    return (
      <Badge variant={config?.color as any}>
        {config?.label}
      </Badge>
    );
  };

  const getTypeBadge = (type: Question['type']) => {
    const config = questionTypes.find(t => t.value === type);
    return (
      <Badge variant="outline">
        {config?.label}
      </Badge>
    );
  };

  const columns: Column<Question>[] = [
    {
      key: 'title',
      title: 'Pregunta',
      sortable: true,
      render: (_, question) => (
        <div>
          <div className="font-medium text-gray-900">{question.title}</div>
          <div className="text-sm text-gray-500 truncate max-w-xs">
            {question.content}
          </div>
        </div>
      ),
    },
    {
      key: 'type',
      title: 'Tipo',
      sortable: true,
      render: (_, question) => getTypeBadge(question.type),
    },
    {
      key: 'difficulty',
      title: 'Dificultad',
      sortable: true,
      render: (_, question) => getDifficultyBadge(question.difficulty),
    },
    {
      key: 'category',
      title: 'Categoría',
      sortable: true,
      render: (value) => (
        <Badge variant="outline">{value}</Badge>
      ),
    },
    {
      key: 'points',
      title: 'Puntos',
      sortable: true,
      align: 'center',
    },
    {
      key: 'tags',
      title: 'Etiquetas',
      render: (_, question) => (
        <div className="flex flex-wrap gap-1">
          {question.tags.slice(0, 2).map((tag, index) => (
            <Badge key={index} variant="outline" className="text-xs">
              {tag}
            </Badge>
          ))}
          {question.tags.length > 2 && (
            <Badge variant="outline" className="text-xs">
              +{question.tags.length - 2}
            </Badge>
          )}
        </div>
      ),
    },
    {
      key: 'createdAt',
      title: 'Creado',
      sortable: true,
      render: (value) => new Date(value).toLocaleDateString(),
    },
    {
      key: 'actions',
      title: 'Acciones',
      align: 'right',
      render: (_, question) => (
        <Dropdown
          trigger={
            <Button variant="ghost" size="sm">
              <Edit className="w-4 h-4" />
            </Button>
          }
          align="right"
        >
          <DropdownItem onClick={() => toast.info('Editando pregunta')}>
            <Edit className="w-4 h-4" />
            Editar
          </DropdownItem>
          <DropdownItem onClick={() => handleDuplicateQuestion(question.id)}>
            <Copy className="w-4 h-4" />
            Duplicar
          </DropdownItem>
          <DropdownSeparator />
          <DropdownItem 
            onClick={() => handleDeleteQuestion(question.id)}
            className="text-red-600"
          >
            <Trash2 className="w-4 h-4" />
            Eliminar
          </DropdownItem>
        </Dropdown>
      ),
    },
  ];

  const stats = {
    total: questions.length,
    byType: questionTypes.map(type => ({
      ...type,
      count: questions.filter(q => q.type === type.value).length,
    })),
    byDifficulty: difficulties.map(diff => ({
      ...diff,
      count: questions.filter(q => q.difficulty === diff.value).length,
    })),
  };

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Banco de Preguntas</h1>
            <p className="text-gray-600 mt-1">
              Gestiona y organiza preguntas para exámenes
            </p>
          </div>
          <div className="flex space-x-3">
            <Button
              variant="outline"
              onClick={() => setIsImportModalOpen(true)}
            >
              <Upload className="w-4 h-4 mr-2" />
              Importar
            </Button>
            <Button
              variant="outline"
              onClick={() => setIsAIModalOpen(true)}
            >
              <Sparkles className="w-4 h-4 mr-2" />
              Generar con IA
            </Button>
            <Button onClick={() => setIsCreateModalOpen(true)}>
              <Plus className="w-4 h-4 mr-2" />
              Nueva Pregunta
            </Button>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <Card>
          <CardContent className="p-6">
            <div className="text-center">
              <p className="text-2xl font-semibold text-gray-900">{stats.total}</p>
              <p className="text-sm text-gray-500">Total Preguntas</p>
            </div>
          </CardContent>
        </Card>

        {stats.byDifficulty.map((diff) => (
          <Card key={diff.value}>
            <CardContent className="p-6">
              <div className="text-center">
                <p className="text-2xl font-semibold text-gray-900">{diff.count}</p>
                <p className="text-sm text-gray-500">{diff.label}</p>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Bulk Actions */}
      {selectedQuestions.length > 0 && (
        <Card className="mb-6 border-blue-200 bg-blue-50">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <span className="text-blue-800 font-medium">
                {selectedQuestions.length} pregunta{selectedQuestions.length > 1 ? 's' : ''} seleccionada{selectedQuestions.length > 1 ? 's' : ''}
              </span>
              <div className="flex space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleExportQuestions}
                >
                  <Download className="w-4 h-4 mr-2" />
                  Exportar
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    selectedQuestions.forEach(handleDeleteQuestion);
                    setSelectedQuestions([]);
                  }}
                  className="text-red-600 border-red-300 hover:bg-red-50"
                >
                  <Trash2 className="w-4 h-4 mr-2" />
                  Eliminar
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Questions Table */}
      <Card>
        <CardHeader>
          <CardTitle>Lista de Preguntas</CardTitle>
        </CardHeader>
        <CardContent>
          <DataTable
            data={questions}
            columns={columns}
            searchable
            searchPlaceholder="Buscar preguntas..."
            exportable
            onExport={() => toast.info('Exportando todas las preguntas...')}
          />
        </CardContent>
      </Card>

      {/* Create Question Modal */}
      <Modal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        title="Nueva Pregunta"
        size="lg"
      >
        <div className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Tipo de Pregunta *
              </label>
              <select
                value={newQuestion.type}
                onChange={(e) => setNewQuestion({ ...newQuestion, type: e.target.value as any })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                {questionTypes.map(type => (
                  <option key={type.value} value={type.value}>
                    {type.label}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Dificultad *
              </label>
              <select
                value={newQuestion.difficulty}
                onChange={(e) => setNewQuestion({ ...newQuestion, difficulty: e.target.value as any })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                {difficulties.map(diff => (
                  <option key={diff.value} value={diff.value}>
                    {diff.label}
                  </option>
                ))}
              </select>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Título *
            </label>
            <Input
              placeholder="Título de la pregunta"
              value={newQuestion.title}
              onChange={(e) => setNewQuestion({ ...newQuestion, title: e.target.value })}
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Contenido *
            </label>
            <textarea
              rows={3}
              placeholder="Escribe la pregunta aquí..."
              value={newQuestion.content}
              onChange={(e) => setNewQuestion({ ...newQuestion, content: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          {newQuestion.type === 'multiple-choice' && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Opciones
              </label>
              <div className="space-y-2">
                {newQuestion.options.map((option, index) => (
                  <Input
                    key={index}
                    placeholder={`Opción ${index + 1}`}
                    value={option}
                    onChange={(e) => {
                      const newOptions = [...newQuestion.options];
                      newOptions[index] = e.target.value;
                      setNewQuestion({ ...newQuestion, options: newOptions });
                    }}
                  />
                ))}
              </div>
            </div>
          )}

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Respuesta Correcta *
              </label>
              <Input
                placeholder="Respuesta correcta"
                value={newQuestion.correctAnswer}
                onChange={(e) => setNewQuestion({ ...newQuestion, correctAnswer: e.target.value })}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Puntos *
              </label>
              <Input
                type="number"
                min="1"
                value={newQuestion.points}
                onChange={(e) => setNewQuestion({ ...newQuestion, points: parseInt(e.target.value) })}
              />
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Categoría
              </label>
              <select
                value={newQuestion.category}
                onChange={(e) => setNewQuestion({ ...newQuestion, category: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">Seleccionar categoría</option>
                {categories.map(category => (
                  <option key={category} value={category}>
                    {category}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Etiquetas
              </label>
              <Input
                placeholder="etiqueta1, etiqueta2, etiqueta3"
                value={newQuestion.tags}
                onChange={(e) => setNewQuestion({ ...newQuestion, tags: e.target.value })}
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Explicación
            </label>
            <textarea
              rows={2}
              placeholder="Explicación de la respuesta correcta (opcional)"
              value={newQuestion.explanation}
              onChange={(e) => setNewQuestion({ ...newQuestion, explanation: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
        </div>

        <ModalFooter>
          <Button
            variant="outline"
            onClick={() => setIsCreateModalOpen(false)}
          >
            Cancelar
          </Button>
          <Button onClick={handleCreateQuestion}>
            Crear Pregunta
          </Button>
        </ModalFooter>
      </Modal>

      {/* Import Modal */}
      <Modal
        isOpen={isImportModalOpen}
        onClose={() => setIsImportModalOpen(false)}
        title="Importar Preguntas"
        size="md"
      >
        <div className="space-y-4">
          <p className="text-sm text-gray-600">
            Sube archivos en formato CSV, JSON o QTI para importar preguntas al banco.
          </p>
          
          <FileUpload
            onFilesChange={handleImportQuestions}
            accept={{
              'text/csv': ['.csv'],
              'application/json': ['.json'],
              'application/xml': ['.xml'],
            }}
            maxFiles={5}
            multiple
          />
        </div>

        <ModalFooter>
          <Button
            variant="outline"
            onClick={() => setIsImportModalOpen(false)}
          >
            Cancelar
          </Button>
        </ModalFooter>
      </Modal>

      {/* AI Generation Modal */}
      <Modal
        isOpen={isAIModalOpen}
        onClose={() => setIsAIModalOpen(false)}
        title="Generar Preguntas con IA"
        size="md"
      >
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Tema *
            </label>
            <Input
              placeholder="Ej: DevOps, Programación en Python, Historia de México"
              value={aiRequest.topic}
              onChange={(e) => setAiRequest({ ...aiRequest, topic: e.target.value })}
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Tipo de Pregunta
              </label>
              <select
                value={aiRequest.questionType}
                onChange={(e) => setAiRequest({ ...aiRequest, questionType: e.target.value as any })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="multiple-choice">Opción Múltiple</option>
                <option value="essay">Ensayo</option>
                <option value="true-false">Verdadero/Falso</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Dificultad
              </label>
              <select
                value={aiRequest.difficulty}
                onChange={(e) => setAiRequest({ ...aiRequest, difficulty: e.target.value as any })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="easy">Fácil</option>
                <option value="medium">Medio</option>
                <option value="hard">Difícil</option>
              </select>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Cantidad
              </label>
              <Input
                type="number"
                min="1"
                max="20"
                value={aiRequest.count}
                onChange={(e) => setAiRequest({ ...aiRequest, count: parseInt(e.target.value) })}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Idioma
              </label>
              <select
                value={aiRequest.language}
                onChange={(e) => setAiRequest({ ...aiRequest, language: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="español">Español</option>
                <option value="english">English</option>
                <option value="français">Français</option>
              </select>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Instrucciones Adicionales
            </label>
            <textarea
              rows={3}
              placeholder="Instrucciones específicas para la IA (opcional)"
              value={aiRequest.additionalInstructions}
              onChange={(e) => setAiRequest({ ...aiRequest, additionalInstructions: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
            <p className="text-sm text-blue-800">
              <strong>Costo estimado:</strong> ~{aiRequest.count * 50} tokens IA
            </p>
          </div>
        </div>

        <ModalFooter>
          <Button
            variant="outline"
            onClick={() => setIsAIModalOpen(false)}
          >
            Cancelar
          </Button>
          <Button onClick={handleGenerateWithAI}>
            <Sparkles className="w-4 h-4 mr-2" />
            Generar Preguntas
          </Button>
        </ModalFooter>
      </Modal>
    </div>
  );
}
