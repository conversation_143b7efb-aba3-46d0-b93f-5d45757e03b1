# Grafana Datasources Configuration
# This file configures the data sources that <PERSON><PERSON> will use

apiVersion: 1

datasources:
  # Prometheus - Metrics Database
  - name: Prometheus
    type: prometheus
    access: proxy
    url: http://prometheus:9090
    isDefault: true
    editable: true
    jsonData:
      timeInterval: "5s"
      queryTimeout: "60s"
      httpMethod: "POST"
    secureJsonData: {}

  # Loki - Logs Database  
  - name: Loki
    type: loki
    access: proxy
    url: http://loki:3100
    editable: true
    jsonData:
      maxLines: 1000
      derivedFields:
        - datasourceUid: prometheus
          matcherRegex: "trace_id=(\\w+)"
          name: "TraceID"
          url: "$${__value.raw}"

  # PostgreSQL - Direct database access for business metrics
  - name: PostgreSQL
    type: postgres
    access: proxy
    url: postgres:5432
    database: arroyo_university
    user: postgres
    editable: true
    secureJsonData:
      password: postgres123
    jsonData:
      sslmode: disable
      maxOpenConns: 10
      maxIdleConns: 2
      connMaxLifetime: 14400
