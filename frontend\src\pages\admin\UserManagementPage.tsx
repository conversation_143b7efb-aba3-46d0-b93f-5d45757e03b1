import React, { useState } from 'react';
import { Plus, Upload, Search, Filter, Mail, Key, UserX, Edit } from 'lucide-react';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Badge } from '@/components/ui/Badge';
import { <PERSON><PERSON>, <PERSON><PERSON>Header, ModalFooter } from '@/components/ui/Modal';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/Avatar';
import { generateInitials } from '@/lib/utils';
import toast from 'react-hot-toast';

interface User {
  id: string;
  email: string;
  firstName?: string;
  lastName?: string;
  avatar?: string;
  status: 'active' | 'inactive' | 'pending' | 'suspended';
  role: string;
  lastLogin?: string;
  createdAt: string;
  createdBy: 'manual' | 'csv' | 'invitation';
}

const mockUsers: User[] = [
  {
    id: '1',
    email: '<EMAIL>',
    firstName: 'María',
    lastName: 'García',
    status: 'active',
    role: 'Instructor',
    lastLogin: '2024-01-15',
    createdAt: '2024-01-01',
    createdBy: 'manual',
  },
  {
    id: '2',
    email: '<EMAIL>',
    firstName: 'Carlos',
    lastName: 'López',
    status: 'pending',
    role: 'Estudiante',
    createdAt: '2024-01-10',
    createdBy: 'csv',
  },
  {
    id: '3',
    email: '<EMAIL>',
    status: 'inactive',
    role: 'Estudiante',
    lastLogin: '2024-01-05',
    createdAt: '2024-01-05',
    createdBy: 'manual',
  },
];

const roles = ['Administrador', 'Instructor', 'Estudiante', 'Calificador'];

export default function UserManagementPage() {
  const [users, setUsers] = useState<User[]>(mockUsers);
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isBulkCreateModalOpen, setIsBulkCreateModalOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [roleFilter, setRoleFilter] = useState<string>('all');

  const [newUser, setNewUser] = useState({
    email: '',
    role: 'Estudiante',
  });

  const [bulkEmails, setBulkEmails] = useState('');
  const [bulkRole, setBulkRole] = useState('Estudiante');

  const stats = {
    total: users.length,
    active: users.filter(u => u.status === 'active').length,
    pending: users.filter(u => u.status === 'pending').length,
    inactive: users.filter(u => u.status === 'inactive').length,
  };

  const filteredUsers = users.filter(user => {
    const matchesSearch = user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (user.firstName?.toLowerCase().includes(searchTerm.toLowerCase())) ||
                         (user.lastName?.toLowerCase().includes(searchTerm.toLowerCase()));
    const matchesStatus = statusFilter === 'all' || user.status === statusFilter;
    const matchesRole = roleFilter === 'all' || user.role === roleFilter;
    
    return matchesSearch && matchesStatus && matchesRole;
  });

  const handleCreateUser = () => {
    if (!newUser.email) {
      toast.error('Por favor, ingresa un email válido');
      return;
    }

    // Check if email already exists
    if (users.some(u => u.email === newUser.email)) {
      toast.error('Este email ya está registrado');
      return;
    }

    const user: User = {
      id: Date.now().toString(),
      email: newUser.email,
      status: 'pending',
      role: newUser.role,
      createdAt: new Date().toISOString().split('T')[0],
      createdBy: 'manual',
    };

    setUsers([...users, user]);
    setIsCreateModalOpen(false);
    setNewUser({ email: '', role: 'Estudiante' });

    toast.success(
      `Usuario creado exitosamente!\n\n` +
      `Email: ${newUser.email}\n` +
      `Rol: ${newUser.role}\n\n` +
      `Se ha enviado un email con la contraseña temporal.`
    );
  };

  const handleBulkCreate = () => {
    if (!bulkEmails.trim()) {
      toast.error('Por favor, ingresa al menos un email');
      return;
    }

    const emails = bulkEmails
      .split('\n')
      .map(email => email.trim())
      .filter(email => email && email.includes('@'));

    if (emails.length === 0) {
      toast.error('No se encontraron emails válidos');
      return;
    }

    // Check for duplicates
    const existingEmails = users.map(u => u.email);
    const duplicates = emails.filter(email => existingEmails.includes(email));
    
    if (duplicates.length > 0) {
      toast.error(`Los siguientes emails ya existen: ${duplicates.join(', ')}`);
      return;
    }

    const newUsers: User[] = emails.map(email => ({
      id: Date.now().toString() + Math.random(),
      email,
      status: 'pending' as const,
      role: bulkRole,
      createdAt: new Date().toISOString().split('T')[0],
      createdBy: 'manual' as const,
    }));

    setUsers([...users, ...newUsers]);
    setIsBulkCreateModalOpen(false);
    setBulkEmails('');
    setBulkRole('Estudiante');

    toast.success(
      `${emails.length} usuarios creados exitosamente!\n\n` +
      `Rol asignado: ${bulkRole}\n\n` +
      `Se han enviado emails con contraseñas temporales.`
    );
  };

  const handleResendPassword = (userId: string) => {
    const user = users.find(u => u.id === userId);
    if (user) {
      toast.success(`Nueva contraseña enviada a ${user.email}`);
    }
  };

  const handleToggleStatus = (userId: string) => {
    setUsers(users.map(user => 
      user.id === userId 
        ? { ...user, status: user.status === 'active' ? 'inactive' : 'active' }
        : user
    ));
    toast.success('Estado del usuario actualizado');
  };

  const getStatusBadge = (status: User['status']) => {
    const variants = {
      active: 'green',
      pending: 'yellow',
      inactive: 'gray',
      suspended: 'red',
    } as const;

    const labels = {
      active: 'Activo',
      pending: 'Pendiente',
      inactive: 'Inactivo',
      suspended: 'Suspendido',
    };

    return (
      <Badge variant={variants[status]}>
        {labels[status]}
      </Badge>
    );
  };

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Gestión de Usuarios</h1>
            <p className="text-gray-600 mt-1">
              Administra usuarios del tenant y sus permisos
            </p>
          </div>
          <div className="flex space-x-3">
            <Button
              variant="outline"
              onClick={() => setIsBulkCreateModalOpen(true)}
            >
              <Upload className="w-4 h-4 mr-2" />
              Crear en Lote
            </Button>
            <Button onClick={() => setIsCreateModalOpen(true)}>
              <Plus className="w-4 h-4 mr-2" />
              Crear Usuario
            </Button>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0 p-3 rounded-lg bg-blue-100">
                <Mail className="h-6 w-6 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Total Usuarios</p>
                <p className="text-2xl font-semibold text-gray-900">{stats.total}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0 p-3 rounded-lg bg-green-100">
                <Key className="h-6 w-6 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Activos</p>
                <p className="text-2xl font-semibold text-gray-900">{stats.active}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0 p-3 rounded-lg bg-yellow-100">
                <Mail className="h-6 w-6 text-yellow-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Pendientes</p>
                <p className="text-2xl font-semibold text-gray-900">{stats.pending}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0 p-3 rounded-lg bg-gray-100">
                <UserX className="h-6 w-6 text-gray-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Inactivos</p>
                <p className="text-2xl font-semibold text-gray-900">{stats.inactive}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card className="mb-6">
        <CardContent className="p-6">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
            <div className="flex items-center space-x-4">
              <div className="relative">
                <Search className="absolute left-3 top-2.5 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Buscar usuarios..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 w-64"
                />
              </div>
              
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">Todos los estados</option>
                <option value="active">Activos</option>
                <option value="pending">Pendientes</option>
                <option value="inactive">Inactivos</option>
                <option value="suspended">Suspendidos</option>
              </select>

              <select
                value={roleFilter}
                onChange={(e) => setRoleFilter(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">Todos los roles</option>
                {roles.map(role => (
                  <option key={role} value={role}>
                    {role}
                  </option>
                ))}
              </select>
            </div>

            <div className="text-sm text-gray-500">
              {filteredUsers.length} de {users.length} usuarios
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Users Table */}
      <Card>
        <CardHeader>
          <CardTitle>Lista de Usuarios</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Usuario
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Rol
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Estado
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Último Acceso
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Creado
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Acciones
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredUsers.map((user) => (
                  <tr key={user.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <Avatar className="w-10 h-10 mr-3">
                          <AvatarImage src={user.avatar} alt={user.firstName} />
                          <AvatarFallback className="bg-blue-500 text-white text-sm font-medium">
                            {generateInitials(user.firstName, user.lastName) || user.email[0].toUpperCase()}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <div className="text-sm font-medium text-gray-900">
                            {user.firstName && user.lastName 
                              ? `${user.firstName} ${user.lastName}`
                              : 'Sin nombre'
                            }
                          </div>
                          <div className="text-sm text-gray-500">{user.email}</div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <Badge variant="outline">{user.role}</Badge>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {getStatusBadge(user.status)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {user.lastLogin ? new Date(user.lastLogin).toLocaleDateString() : 'Nunca'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {new Date(user.createdAt).toLocaleDateString()}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex items-center justify-end space-x-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleResendPassword(user.id)}
                        >
                          <Key className="w-4 h-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleToggleStatus(user.id)}
                        >
                          <Edit className="w-4 h-4" />
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>

      {/* Create User Modal */}
      <Modal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        title="Crear Nuevo Usuario"
        size="md"
      >
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Email *
            </label>
            <Input
              type="email"
              placeholder="<EMAIL>"
              value={newUser.email}
              onChange={(e) => setNewUser({ ...newUser, email: e.target.value })}
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Rol *
            </label>
            <select
              value={newUser.role}
              onChange={(e) => setNewUser({ ...newUser, role: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              {roles.map(role => (
                <option key={role} value={role}>
                  {role}
                </option>
              ))}
            </select>
          </div>

          <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
            <p className="text-sm text-blue-800">
              <strong>Nota:</strong> Se generará una contraseña temporal que será enviada al email del usuario.
              El usuario deberá cambiar la contraseña en su primer inicio de sesión.
            </p>
          </div>
        </div>

        <ModalFooter>
          <Button
            variant="outline"
            onClick={() => setIsCreateModalOpen(false)}
          >
            Cancelar
          </Button>
          <Button onClick={handleCreateUser}>
            Crear Usuario
          </Button>
        </ModalFooter>
      </Modal>

      {/* Bulk Create Modal */}
      <Modal
        isOpen={isBulkCreateModalOpen}
        onClose={() => setIsBulkCreateModalOpen(false)}
        title="Crear Usuarios en Lote"
        size="lg"
      >
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Lista de Emails *
            </label>
            <textarea
              rows={8}
              placeholder="<EMAIL>&#10;<EMAIL>&#10;<EMAIL>"
              value={bulkEmails}
              onChange={(e) => setBulkEmails(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            <p className="mt-1 text-xs text-gray-500">
              Ingresa un email por línea. También puedes subir un archivo CSV.
            </p>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Rol para todos los usuarios *
            </label>
            <select
              value={bulkRole}
              onChange={(e) => setBulkRole(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              {roles.map(role => (
                <option key={role} value={role}>
                  {role}
                </option>
              ))}
            </select>
          </div>

          <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4">
            <p className="text-sm text-yellow-800">
              <strong>Importante:</strong> Se crearán todos los usuarios con el mismo rol.
              Se generarán contraseñas temporales únicas para cada usuario y se enviarán por email.
            </p>
          </div>
        </div>

        <ModalFooter>
          <Button
            variant="outline"
            onClick={() => setIsBulkCreateModalOpen(false)}
          >
            Cancelar
          </Button>
          <Button onClick={handleBulkCreate}>
            Crear Usuarios
          </Button>
        </ModalFooter>
      </Modal>
    </div>
  );
}
