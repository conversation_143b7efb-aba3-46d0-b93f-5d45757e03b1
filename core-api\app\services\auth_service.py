"""
Authentication service for user login, registration, and token management
"""

import secrets
import hashlib
from datetime import datetime, timed<PERSON><PERSON>
from typing import Optional, <PERSON><PERSON>
from uuid import UUID

import bcrypt
import jwt
from sqlmodel import Session, select

from ..core.database import get_session
from ..core.config import settings
from ..models.user import (
    User, UserCreate, UserLogin, UserSession, UserVerificationToken,
    UserPasswordReset, UserPasswordResetConfirm, UserEmailVerification
)
from ..models.tenant import Tenant
from ..models.role import Role, UserRole
from ..services.notification_service import NotificationService


class AuthService:
    """Authentication service"""
    
    def __init__(self, db: Session):
        self.db = db
        self.notification_service = NotificationService(db)
    
    async def authenticate_user(self, email: str, password: str, tenant_id: UUID) -> Optional[User]:
        """Authenticate user with email and password"""
        # Get user by email and tenant
        stmt = select(User).where(
            User.email == email,
            User.tenant_id == tenant_id,
            User.is_active == True,
            User.is_deleted == False
        )
        user = self.db.exec(stmt).first()
        
        if not user:
            return None
        
        # Check if account is locked
        if user.locked_until and user.locked_until > datetime.utcnow():
            raise ValueError("Account is temporarily locked due to too many failed login attempts")
        
        # Verify password
        if not self._verify_password(password, user.password_hash):
            # Increment failed attempts
            user.failed_login_attempts += 1
            
            # Lock account if too many attempts
            if user.failed_login_attempts >= settings.MAX_LOGIN_ATTEMPTS:
                user.locked_until = datetime.utcnow() + timedelta(minutes=30)
            
            self.db.add(user)
            self.db.commit()
            return None
        
        # Reset failed attempts on successful login
        user.failed_login_attempts = 0
        user.locked_until = None
        user.last_login_at = datetime.utcnow()
        user.login_count += 1
        
        self.db.add(user)
        self.db.commit()
        self.db.refresh(user)
        
        return user
    
    async def create_session(self, user: User, ip_address: str = None, user_agent: str = None) -> Tuple[str, str]:
        """Create user session and return access and refresh tokens"""
        # Generate tokens
        access_token = self._generate_access_token(user)
        refresh_token = self._generate_refresh_token()
        
        # Create session record
        session = UserSession(
            user_id=user.user_id,
            tenant_id=user.tenant_id,
            token_hash=self._hash_token(access_token),
            refresh_token_hash=self._hash_token(refresh_token),
            expires_at=datetime.utcnow() + timedelta(hours=settings.ACCESS_TOKEN_EXPIRE_HOURS),
            ip_address=ip_address,
            user_agent=user_agent
        )
        
        self.db.add(session)
        self.db.commit()
        
        return access_token, refresh_token
    
    async def refresh_token(self, refresh_token: str) -> Tuple[str, str]:
        """Refresh access token using refresh token"""
        token_hash = self._hash_token(refresh_token)
        
        # Find active session
        stmt = select(UserSession).where(
            UserSession.refresh_token_hash == token_hash,
            UserSession.is_active == True,
            UserSession.expires_at > datetime.utcnow()
        )
        session = self.db.exec(stmt).first()
        
        if not session:
            raise ValueError("Invalid or expired refresh token")
        
        # Get user
        user = self.db.get(User, session.user_id)
        if not user or not user.is_active:
            raise ValueError("User not found or inactive")
        
        # Generate new tokens
        new_access_token = self._generate_access_token(user)
        new_refresh_token = self._generate_refresh_token()
        
        # Update session
        session.token_hash = self._hash_token(new_access_token)
        session.refresh_token_hash = self._hash_token(new_refresh_token)
        session.expires_at = datetime.utcnow() + timedelta(hours=settings.ACCESS_TOKEN_EXPIRE_HOURS)
        session.last_used_at = datetime.utcnow()
        
        self.db.add(session)
        self.db.commit()
        
        return new_access_token, new_refresh_token
    
    async def logout(self, access_token: str) -> bool:
        """Logout user by invalidating session"""
        token_hash = self._hash_token(access_token)
        
        stmt = select(UserSession).where(
            UserSession.token_hash == token_hash,
            UserSession.is_active == True
        )
        session = self.db.exec(stmt).first()
        
        if session:
            session.is_active = False
            self.db.add(session)
            self.db.commit()
            return True
        
        return False
    
    async def verify_token(self, token: str) -> Optional[User]:
        """Verify access token and return user"""
        try:
            payload = jwt.decode(token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM])
            user_id = payload.get("sub")
            tenant_id = payload.get("tenant_id")
            
            if not user_id or not tenant_id:
                return None
            
            # Check if session is still active
            token_hash = self._hash_token(token)
            stmt = select(UserSession).where(
                UserSession.token_hash == token_hash,
                UserSession.is_active == True,
                UserSession.expires_at > datetime.utcnow()
            )
            session = self.db.exec(stmt).first()
            
            if not session:
                return None
            
            # Get user
            user = self.db.get(User, UUID(user_id))
            if not user or not user.is_active or user.tenant_id != UUID(tenant_id):
                return None
            
            # Update last used
            session.last_used_at = datetime.utcnow()
            self.db.add(session)
            self.db.commit()
            
            return user
            
        except jwt.PyJWTError:
            return None
    
    async def request_password_reset(self, email: str, tenant_id: UUID) -> bool:
        """Request password reset for user"""
        # Find user
        stmt = select(User).where(
            User.email == email,
            User.tenant_id == tenant_id,
            User.is_active == True
        )
        user = self.db.exec(stmt).first()
        
        if not user:
            # Don't reveal if email exists
            return True
        
        # Generate reset token
        token = secrets.token_urlsafe(32)
        token_hash = self._hash_token(token)
        
        # Create verification token
        verification_token = UserVerificationToken(
            user_id=user.user_id,
            tenant_id=user.tenant_id,
            token_hash=token_hash,
            token_type="password_reset",
            expires_at=datetime.utcnow() + timedelta(hours=24)
        )
        
        self.db.add(verification_token)
        self.db.commit()
        
        # Send reset email
        await self.notification_service.send_password_reset_email(user, token)
        
        return True
    
    async def reset_password(self, token: str, new_password: str) -> bool:
        """Reset password using reset token"""
        token_hash = self._hash_token(token)
        
        # Find valid token
        stmt = select(UserVerificationToken).where(
            UserVerificationToken.token_hash == token_hash,
            UserVerificationToken.token_type == "password_reset",
            UserVerificationToken.is_used == False,
            UserVerificationToken.expires_at > datetime.utcnow()
        )
        verification_token = self.db.exec(stmt).first()
        
        if not verification_token:
            raise ValueError("Invalid or expired reset token")
        
        # Get user
        user = self.db.get(User, verification_token.user_id)
        if not user:
            raise ValueError("User not found")
        
        # Update password
        user.password_hash = self._hash_password(new_password)
        
        # Mark token as used
        verification_token.is_used = True
        verification_token.used_at = datetime.utcnow()
        
        # Invalidate all user sessions
        stmt = select(UserSession).where(
            UserSession.user_id == user.user_id,
            UserSession.is_active == True
        )
        sessions = self.db.exec(stmt).all()
        for session in sessions:
            session.is_active = False
            self.db.add(session)
        
        self.db.add(user)
        self.db.add(verification_token)
        self.db.commit()
        
        return True
    
    async def verify_email(self, token: str) -> bool:
        """Verify user email using verification token"""
        token_hash = self._hash_token(token)
        
        # Find valid token
        stmt = select(UserVerificationToken).where(
            UserVerificationToken.token_hash == token_hash,
            UserVerificationToken.token_type == "email_verification",
            UserVerificationToken.is_used == False,
            UserVerificationToken.expires_at > datetime.utcnow()
        )
        verification_token = self.db.exec(stmt).first()
        
        if not verification_token:
            raise ValueError("Invalid or expired verification token")
        
        # Get user
        user = self.db.get(User, verification_token.user_id)
        if not user:
            raise ValueError("User not found")
        
        # Verify email
        user.is_verified = True
        user.email_verified_at = datetime.utcnow()
        
        # Mark token as used
        verification_token.is_used = True
        verification_token.used_at = datetime.utcnow()
        
        self.db.add(user)
        self.db.add(verification_token)
        self.db.commit()
        
        return True
    
    async def resend_verification_email(self, email: str, tenant_id: UUID) -> bool:
        """Resend email verification"""
        # Find user
        stmt = select(User).where(
            User.email == email,
            User.tenant_id == tenant_id,
            User.is_active == True,
            User.is_verified == False
        )
        user = self.db.exec(stmt).first()
        
        if not user:
            return True  # Don't reveal if email exists
        
        # Generate verification token
        token = secrets.token_urlsafe(32)
        token_hash = self._hash_token(token)
        
        # Create verification token
        verification_token = UserVerificationToken(
            user_id=user.user_id,
            tenant_id=user.tenant_id,
            token_hash=token_hash,
            token_type="email_verification",
            expires_at=datetime.utcnow() + timedelta(hours=24)
        )
        
        self.db.add(verification_token)
        self.db.commit()
        
        # Send verification email
        await self.notification_service.send_verification_email(user, token)
        
        return True
    
    def _hash_password(self, password: str) -> str:
        """Hash password using bcrypt"""
        salt = bcrypt.gensalt()
        return bcrypt.hashpw(password.encode('utf-8'), salt).decode('utf-8')
    
    def _verify_password(self, password: str, hashed: str) -> bool:
        """Verify password against hash"""
        return bcrypt.checkpw(password.encode('utf-8'), hashed.encode('utf-8'))
    
    def _generate_access_token(self, user: User) -> str:
        """Generate JWT access token"""
        payload = {
            "sub": str(user.user_id),
            "tenant_id": str(user.tenant_id),
            "email": user.email,
            "exp": datetime.utcnow() + timedelta(hours=settings.ACCESS_TOKEN_EXPIRE_HOURS),
            "iat": datetime.utcnow(),
            "type": "access"
        }
        return jwt.encode(payload, settings.SECRET_KEY, algorithm=settings.ALGORITHM)
    
    def _generate_refresh_token(self) -> str:
        """Generate refresh token"""
        return secrets.token_urlsafe(32)
    
    def _hash_token(self, token: str) -> str:
        """Hash token for storage"""
        return hashlib.sha256(token.encode()).hexdigest()
