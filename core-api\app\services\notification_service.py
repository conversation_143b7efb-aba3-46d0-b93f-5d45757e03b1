"""
Notification service for email and notifications
"""

import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MI<PERSON><PERSON>ultipart
from datetime import datetime, timedelta
from typing import Optional, List, Dict, Any
from uuid import UUID

from sqlmodel import Session, select

from ..core.config import settings, EmailConfig
from ..models.user import User
from ..models.notification import (
    Notification, NotificationCreate, EmailTemplate, EmailQueue, EmailQueueCreate
)


class NotificationService:
    """Notification service for email and notifications"""
    
    def __init__(self, db: Session):
        self.db = db
        self.email_config = EmailConfig.get_smtp_config()
        self.from_config = EmailConfig.get_from_config()
    
    async def send_verification_email(self, user: User, token: str) -> bool:
        """Send email verification email"""
        try:
            subject = "Verify your email address"
            verification_url = f"{settings.FRONTEND_URL}/verify-email/{token}"
            
            html_content = f"""
            <html>
                <body>
                    <h2>Welcome to Arroyo University!</h2>
                    <p>Hello {user.first_name},</p>
                    <p>Thank you for registering with Arroyo University. Please verify your email address by clicking the link below:</p>
                    <p><a href="{verification_url}" style="background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Verify Email Address</a></p>
                    <p>If the button doesn't work, you can copy and paste this link into your browser:</p>
                    <p>{verification_url}</p>
                    <p>This link will expire in 24 hours.</p>
                    <p>If you didn't create an account with us, please ignore this email.</p>
                    <p>Best regards,<br>The Arroyo University Team</p>
                </body>
            </html>
            """
            
            text_content = f"""
            Welcome to Arroyo University!
            
            Hello {user.first_name},
            
            Thank you for registering with Arroyo University. Please verify your email address by visiting this link:
            
            {verification_url}
            
            This link will expire in 24 hours.
            
            If you didn't create an account with us, please ignore this email.
            
            Best regards,
            The Arroyo University Team
            """
            
            return await self._send_email(
                to_email=user.email,
                to_name=f"{user.first_name} {user.last_name}",
                subject=subject,
                html_content=html_content,
                text_content=text_content
            )
            
        except Exception as e:
            print(f"Failed to send verification email: {e}")
            return False
    
    async def send_password_reset_email(self, user: User, token: str) -> bool:
        """Send password reset email"""
        try:
            subject = "Reset your password"
            reset_url = f"{settings.FRONTEND_URL}/reset-password/{token}"
            
            html_content = f"""
            <html>
                <body>
                    <h2>Password Reset Request</h2>
                    <p>Hello {user.first_name},</p>
                    <p>We received a request to reset your password for your Arroyo University account.</p>
                    <p>Click the link below to reset your password:</p>
                    <p><a href="{reset_url}" style="background-color: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Reset Password</a></p>
                    <p>If the button doesn't work, you can copy and paste this link into your browser:</p>
                    <p>{reset_url}</p>
                    <p>This link will expire in 24 hours.</p>
                    <p>If you didn't request a password reset, please ignore this email. Your password will remain unchanged.</p>
                    <p>Best regards,<br>The Arroyo University Team</p>
                </body>
            </html>
            """
            
            text_content = f"""
            Password Reset Request
            
            Hello {user.first_name},
            
            We received a request to reset your password for your Arroyo University account.
            
            Visit this link to reset your password:
            {reset_url}
            
            This link will expire in 24 hours.
            
            If you didn't request a password reset, please ignore this email. Your password will remain unchanged.
            
            Best regards,
            The Arroyo University Team
            """
            
            return await self._send_email(
                to_email=user.email,
                to_name=f"{user.first_name} {user.last_name}",
                subject=subject,
                html_content=html_content,
                text_content=text_content
            )
            
        except Exception as e:
            print(f"Failed to send password reset email: {e}")
            return False
    
    async def send_welcome_email(self, user: User, temporary_password: str = None) -> bool:
        """Send welcome email to new user"""
        try:
            subject = "Welcome to Arroyo University!"
            
            password_info = ""
            if temporary_password:
                password_info = f"""
                <p><strong>Your temporary password is:</strong> {temporary_password}</p>
                <p>Please log in and change your password as soon as possible.</p>
                """
            
            html_content = f"""
            <html>
                <body>
                    <h2>Welcome to Arroyo University!</h2>
                    <p>Hello {user.first_name},</p>
                    <p>Your account has been created successfully. You can now access the learning platform.</p>
                    {password_info}
                    <p><a href="{settings.FRONTEND_URL}/login" style="background-color: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Login to Platform</a></p>
                    <p>If you have any questions, please don't hesitate to contact our support team.</p>
                    <p>Best regards,<br>The Arroyo University Team</p>
                </body>
            </html>
            """
            
            return await self._send_email(
                to_email=user.email,
                to_name=f"{user.first_name} {user.last_name}",
                subject=subject,
                html_content=html_content
            )
            
        except Exception as e:
            print(f"Failed to send welcome email: {e}")
            return False
    
    async def create_notification(self, notification_data: NotificationCreate) -> Notification:
        """Create a new notification"""
        notification = Notification(
            user_id=notification_data.user_id,
            tenant_id=notification_data.user_id,  # This should be properly set
            title=notification_data.title,
            message=notification_data.message,
            notification_type=notification_data.notification_type,
            priority=notification_data.priority,
            related_id=notification_data.related_id,
            related_type=notification_data.related_type,
            action_url=notification_data.action_url,
            data=notification_data.data or {}
        )
        
        self.db.add(notification)
        self.db.commit()
        self.db.refresh(notification)
        
        return notification
    
    async def get_user_notifications(
        self, 
        user_id: UUID, 
        tenant_id: UUID,
        unread_only: bool = False,
        limit: int = 20
    ) -> List[Notification]:
        """Get user notifications"""
        stmt = select(Notification).where(
            Notification.user_id == user_id,
            Notification.tenant_id == tenant_id
        )
        
        if unread_only:
            stmt = stmt.where(Notification.status == "unread")
        
        stmt = stmt.order_by(Notification.created_at.desc()).limit(limit)
        
        return self.db.exec(stmt).all()
    
    async def mark_notification_read(self, notification_id: UUID, user_id: UUID) -> bool:
        """Mark notification as read"""
        notification = self.db.get(Notification, notification_id)
        
        if not notification or notification.user_id != user_id:
            return False
        
        notification.status = "read"
        notification.read_at = datetime.utcnow()
        
        self.db.add(notification)
        self.db.commit()
        
        return True
    
    async def _send_email(
        self,
        to_email: str,
        to_name: str,
        subject: str,
        html_content: str,
        text_content: str = None
    ) -> bool:
        """Send email using SMTP"""
        try:
            # If SMTP is not configured, just log and return True
            if not self.email_config.get("hostname"):
                print(f"Email would be sent to {to_email}: {subject}")
                return True
            
            # Create message
            msg = MIMEMultipart("alternative")
            msg["Subject"] = subject
            msg["From"] = f"{self.from_config['name']} <{self.from_config['email']}>"
            msg["To"] = f"{to_name} <{to_email}>"
            
            # Add text and HTML parts
            if text_content:
                text_part = MIMEText(text_content, "plain")
                msg.attach(text_part)
            
            html_part = MIMEText(html_content, "html")
            msg.attach(html_part)
            
            # Send email
            with smtplib.SMTP(self.email_config["hostname"], self.email_config["port"]) as server:
                if self.email_config.get("use_tls"):
                    server.starttls()
                
                if self.email_config.get("username"):
                    server.login(
                        self.email_config["username"],
                        self.email_config["password"]
                    )
                
                server.send_message(msg)
            
            return True
            
        except Exception as e:
            print(f"Failed to send email: {e}")
            return False
