import React from 'react';
import { <PERSON>, Users, Clock, Star } from 'lucide-react';
import { Course } from '@/types';
import { Button } from '@/components/ui/Button';
import { Badge } from '@/components/ui/Badge';
import { Card, CardContent } from '@/components/ui/Card';
import { 
  getGradientByCategory, 
  getBadgeColorByCategory, 
  getBadgeColorByLevel,
  formatDuration,
  formatStudentCount,
  formatRating
} from '@/lib/utils';

interface CourseCardProps {
  course: Course;
  onEnroll?: (courseId: string) => void;
  onSave?: (courseId: string) => void;
  isSaved?: boolean;
  isEnrolled?: boolean;
  variant?: 'default' | 'compact';
}

export function CourseCard({ 
  course, 
  onEnroll, 
  onSave, 
  isSaved = false, 
  isEnrolled = false,
  variant = 'default'
}: CourseCardProps) {
  const handleEnroll = () => {
    onEnroll?.(course.id);
  };

  const handleSave = () => {
    onSave?.(course.id);
  };

  if (variant === 'compact') {
    return (
      <Card className="border border-gray-200 hover:bg-gray-50 transition-colors">
        <CardContent className="p-4">
          <div className="flex items-center justify-between mb-2">
            <Badge variant={getBadgeColorByCategory(course.category) as any}>
              {course.category}
            </Badge>
            <div className="flex items-center text-xs text-gray-500">
              <Star className="w-3 h-3 mr-1 fill-current text-yellow-400" />
              {formatRating(course.rating)}
            </div>
          </div>
          
          <h4 className="text-sm font-medium text-gray-900 mb-1 line-clamp-2">
            {course.title}
          </h4>
          
          <p className="text-xs text-gray-600 mb-3 line-clamp-2">
            {course.description}
          </p>
          
          <div className="flex items-center justify-between text-xs text-gray-500 mb-3">
            <span>👨‍🏫 {course.creator.firstName}</span>
            <span>👥 {formatStudentCount(course.studentCount)}</span>
          </div>
          
          <Button 
            size="sm" 
            className="w-full text-xs"
            onClick={handleEnroll}
            disabled={isEnrolled}
          >
            {isEnrolled ? 'Inscrito' : 'Inscribirse'}
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="course-card">
      {/* Course Header with Gradient */}
      <div className={`course-card-header bg-gradient-to-br ${getGradientByCategory(course.category)}`}>
        <div className="text-center text-white">
          <div className="w-16 h-16 mx-auto mb-3 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
            <span className="text-2xl font-bold">
              {course.title.charAt(0)}
            </span>
          </div>
          <h4 className="text-xl font-bold">{course.title}</h4>
        </div>
      </div>

      {/* Course Content */}
      <div className="course-card-content">
        {/* Badges */}
        <div className="flex items-center justify-between mb-3">
          <Badge variant={getBadgeColorByCategory(course.category) as any}>
            {course.category}
          </Badge>
          <Badge variant={getBadgeColorByLevel(course.level) as any}>
            {course.level === 'beginner' ? 'Principiante' : 
             course.level === 'intermediate' ? 'Intermedio' : 'Avanzado'}
          </Badge>
        </div>

        {/* Title and Description */}
        <h4 className="text-lg font-medium text-gray-900 mb-2">
          {course.title}
        </h4>
        <p className="text-gray-600 text-sm mb-4 line-clamp-3">
          {course.description}
        </p>

        {/* Course Stats */}
        <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
          <div className="flex items-center">
            <span>👨‍🏫 {course.creator.firstName}</span>
          </div>
          <div className="flex items-center space-x-3">
            <div className="flex items-center">
              <Clock className="w-4 h-4 mr-1" />
              {formatDuration(course.duration)}
            </div>
            <div className="flex items-center">
              <Users className="w-4 h-4 mr-1" />
              {formatStudentCount(course.studentCount)}
            </div>
            <div className="flex items-center">
              <Star className="w-4 h-4 mr-1 fill-current text-yellow-400" />
              {formatRating(course.rating)}
            </div>
          </div>
        </div>

        {/* Actions */}
        <div className="flex items-center space-x-2">
          <Button 
            className="flex-1"
            onClick={handleEnroll}
            disabled={isEnrolled}
          >
            {isEnrolled ? 'Inscrito' : 'Inscribirse'}
          </Button>
          
          <Button
            variant="outline"
            size="icon"
            onClick={handleSave}
            className={isSaved ? 'text-red-500 border-red-500' : ''}
          >
            <Heart className={`w-4 h-4 ${isSaved ? 'fill-current' : ''}`} />
          </Button>
        </div>
      </div>
    </Card>
  );
}
