import { apiClient } from './apiClient';

export interface Question {
  id: string;
  type: 'multiple-choice' | 'essay' | 'true-false' | 'fill-blank' | 'multimedia';
  title: string;
  content: string;
  options?: string[];
  correctAnswer?: string | string[];
  points: number;
  timeLimit?: number;
  difficulty: 'easy' | 'medium' | 'hard';
  category: string;
  tags: string[];
  multimedia?: {
    type: 'image' | 'audio' | 'video';
    url: string;
    description?: string;
  };
  explanation?: string;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
}

export interface Exam {
  id: string;
  title: string;
  description?: string;
  courseId: string;
  questions: Question[];
  timeLimit: number;
  passingScore: number;
  attempts: number;
  shuffleQuestions: boolean;
  shuffleOptions: boolean;
  showResults: 'immediate' | 'after-submission' | 'manual';
  allowReview: boolean;
  status: 'draft' | 'published' | 'archived';
  scheduledStart?: string;
  scheduledEnd?: string;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
}

export interface ExamAttempt {
  id: string;
  examId: string;
  userId: string;
  startedAt: string;
  submittedAt?: string;
  timeSpent: number;
  score?: number;
  passed?: boolean;
  answers: ExamAnswer[];
  status: 'in-progress' | 'submitted' | 'graded' | 'expired';
}

export interface ExamAnswer {
  questionId: string;
  answer: string | string[];
  timeSpent: number;
  isCorrect?: boolean;
  points?: number;
  feedback?: string;
}

export interface ExamResult {
  attempt: ExamAttempt;
  exam: Exam;
  totalQuestions: number;
  correctAnswers: number;
  score: number;
  percentage: number;
  passed: boolean;
  timeSpent: number;
  feedback?: string;
}

export interface CreateExamRequest {
  title: string;
  description?: string;
  courseId: string;
  questionIds: string[];
  timeLimit: number;
  passingScore: number;
  attempts: number;
  shuffleQuestions?: boolean;
  shuffleOptions?: boolean;
  showResults?: 'immediate' | 'after-submission' | 'manual';
  allowReview?: boolean;
  scheduledStart?: string;
  scheduledEnd?: string;
}

export interface UpdateExamRequest {
  title?: string;
  description?: string;
  questionIds?: string[];
  timeLimit?: number;
  passingScore?: number;
  attempts?: number;
  shuffleQuestions?: boolean;
  shuffleOptions?: boolean;
  showResults?: 'immediate' | 'after-submission' | 'manual';
  allowReview?: boolean;
  scheduledStart?: string;
  scheduledEnd?: string;
  status?: 'draft' | 'published' | 'archived';
}

export interface ExamFilters {
  courseId?: string;
  status?: 'draft' | 'published' | 'archived';
  createdBy?: string;
  search?: string;
}

export const examService = {
  // Get all exams with optional filters
  async getExams(filters?: ExamFilters): Promise<Exam[]> {
    const params = new URLSearchParams();
    if (filters?.courseId) params.append('courseId', filters.courseId);
    if (filters?.status) params.append('status', filters.status);
    if (filters?.createdBy) params.append('createdBy', filters.createdBy);
    if (filters?.search) params.append('search', filters.search);

    const response = await apiClient.get(`/exams?${params.toString()}`);
    return response.data;
  },

  // Get exam by ID
  async getExam(id: string): Promise<Exam> {
    const response = await apiClient.get(`/exams/${id}`);
    return response.data;
  },

  // Create new exam
  async createExam(data: CreateExamRequest): Promise<Exam> {
    const response = await apiClient.post('/exams', data);
    return response.data;
  },

  // Update exam
  async updateExam(id: string, data: UpdateExamRequest): Promise<Exam> {
    const response = await apiClient.put(`/exams/${id}`, data);
    return response.data;
  },

  // Delete exam
  async deleteExam(id: string): Promise<void> {
    await apiClient.delete(`/exams/${id}`);
  },

  // Start exam attempt
  async startExam(examId: string): Promise<ExamAttempt> {
    const response = await apiClient.post(`/exams/${examId}/start`);
    return response.data;
  },

  // Submit exam answer
  async submitAnswer(attemptId: string, questionId: string, answer: string | string[]): Promise<void> {
    await apiClient.post(`/exam-attempts/${attemptId}/answers`, {
      questionId,
      answer,
    });
  },

  // Submit exam attempt
  async submitExam(attemptId: string): Promise<ExamResult> {
    const response = await apiClient.post(`/exam-attempts/${attemptId}/submit`);
    return response.data;
  },

  // Get exam attempt
  async getExamAttempt(attemptId: string): Promise<ExamAttempt> {
    const response = await apiClient.get(`/exam-attempts/${attemptId}`);
    return response.data;
  },

  // Get user's exam attempts
  async getUserExamAttempts(examId?: string): Promise<ExamAttempt[]> {
    const params = examId ? `?examId=${examId}` : '';
    const response = await apiClient.get(`/exam-attempts${params}`);
    return response.data;
  },

  // Get exam results
  async getExamResults(examId: string): Promise<ExamResult[]> {
    const response = await apiClient.get(`/exams/${examId}/results`);
    return response.data;
  },

  // Grade exam attempt (instructor only)
  async gradeExamAttempt(attemptId: string, grades: { questionId: string; points: number; feedback?: string }[]): Promise<ExamResult> {
    const response = await apiClient.post(`/exam-attempts/${attemptId}/grade`, { grades });
    return response.data;
  },

  // Get exam statistics (instructor only)
  async getExamStatistics(examId: string): Promise<{
    totalAttempts: number;
    averageScore: number;
    passRate: number;
    averageTime: number;
    questionStatistics: Array<{
      questionId: string;
      correctRate: number;
      averageTime: number;
    }>;
  }> {
    const response = await apiClient.get(`/exams/${examId}/statistics`);
    return response.data;
  },

  // Duplicate exam
  async duplicateExam(examId: string, title: string): Promise<Exam> {
    const response = await apiClient.post(`/exams/${examId}/duplicate`, { title });
    return response.data;
  },

  // Export exam results
  async exportExamResults(examId: string, format: 'csv' | 'xlsx' = 'csv'): Promise<Blob> {
    const response = await apiClient.get(`/exams/${examId}/export?format=${format}`, {
      responseType: 'blob',
    });
    return response.data;
  },

  // Get exam preview (without starting attempt)
  async getExamPreview(examId: string): Promise<Omit<Exam, 'questions'> & { questionCount: number }> {
    const response = await apiClient.get(`/exams/${examId}/preview`);
    return response.data;
  },
};
