-- Career Paths System Tables
-- This script creates tables for skills, career paths, and user progress tracking

-- Create skills table
CREATE TABLE skills (
    skill_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL,
    description TEXT,
    area VARCHAR(50) NOT NULL, -- programming, devops, data, security, management, design, marketing
    color_class VARCHAR(50), -- CSS class for visual representation
    prerequisites JSONB, -- array of prerequisite skill IDs
    difficulty_level VARCHAR(20) DEFAULT 'intermediate', -- beginner, intermediate, advanced
    estimated_learning_hours INTEGER DEFAULT 0,
    verification_methods JSONB DEFAULT '[]', -- array of verification methods
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(name, area)
);

-- Create career_paths table
CREATE TABLE career_paths (
    path_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(200) NOT NULL,
    description TEXT,
    from_position VARCHAR(100), -- starting position/role
    to_position VARCHAR(100), -- target position/role
    estimated_duration_months INTEGER,
    difficulty_level VARCHAR(20) DEFAULT 'intermediate',
    is_template BOOLEAN DEFAULT FALSE, -- true for predefined paths, false for custom
    created_by UUID REFERENCES users(user_id),
    is_public BOOLEAN DEFAULT TRUE,
    rating_avg DECIMAL(3,2) DEFAULT 0.00,
    users_count INTEGER DEFAULT 0,
    skills_count INTEGER DEFAULT 0,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create career_path_skills table
CREATE TABLE career_path_skills (
    path_skill_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    path_id UUID NOT NULL REFERENCES career_paths(path_id) ON DELETE CASCADE,
    skill_id UUID NOT NULL REFERENCES skills(skill_id) ON DELETE CASCADE,
    position_x INTEGER NOT NULL, -- X coordinate in diagram
    position_y INTEGER NOT NULL, -- Y coordinate in diagram
    is_starting_skill BOOLEAN DEFAULT FALSE,
    is_target_skill BOOLEAN DEFAULT FALSE,
    order_sequence INTEGER, -- suggested learning order
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(path_id, skill_id)
);

-- Create career_path_connections table
CREATE TABLE career_path_connections (
    connection_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    path_id UUID NOT NULL REFERENCES career_paths(path_id) ON DELETE CASCADE,
    from_skill_id UUID NOT NULL REFERENCES skills(skill_id) ON DELETE CASCADE,
    to_skill_id UUID NOT NULL REFERENCES skills(skill_id) ON DELETE CASCADE,
    connection_type VARCHAR(20) DEFAULT 'prerequisite', -- prerequisite, related, progression
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(path_id, from_skill_id, to_skill_id)
);

-- Create user_career_paths table
CREATE TABLE user_career_paths (
    user_path_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
    path_id UUID NOT NULL REFERENCES career_paths(path_id) ON DELETE CASCADE,
    status VARCHAR(20) DEFAULT 'active', -- active, completed, paused, abandoned
    progress_percentage DECIMAL(5,2) DEFAULT 0.00,
    skills_completed INTEGER DEFAULT 0,
    skills_total INTEGER NOT NULL,
    started_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP WITH TIME ZONE,
    last_activity_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    metadata JSONB DEFAULT '{}',
    UNIQUE(user_id, path_id)
);

-- Create user_skill_progress table
CREATE TABLE user_skill_progress (
    progress_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
    skill_id UUID NOT NULL REFERENCES skills(skill_id) ON DELETE CASCADE,
    path_id UUID REFERENCES career_paths(path_id), -- null if skill learned outside path
    status VARCHAR(20) DEFAULT 'not_started', -- not_started, in_progress, completed, verified
    completion_date TIMESTAMP WITH TIME ZONE,
    verification_method VARCHAR(50), -- course_completion, certification, manual, expert_review
    verified_by UUID REFERENCES users(user_id), -- expert or admin who verified
    notes TEXT,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, skill_id)
);

-- Create career_path_ratings table
CREATE TABLE career_path_ratings (
    rating_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    path_id UUID NOT NULL REFERENCES career_paths(path_id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
    rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
    review_text TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(path_id, user_id)
);

-- Create skill_course_mappings table
CREATE TABLE skill_course_mappings (
    mapping_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    skill_id UUID NOT NULL REFERENCES skills(skill_id) ON DELETE CASCADE,
    course_id UUID NOT NULL REFERENCES courses(course_id) ON DELETE CASCADE,
    relevance_score DECIMAL(3,2) DEFAULT 1.00, -- how relevant the course is to the skill (0.0-1.0)
    is_primary BOOLEAN DEFAULT FALSE, -- primary course for learning this skill
    created_by UUID REFERENCES users(user_id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(skill_id, course_id)
);

-- Create career_path_recommendations table
CREATE TABLE career_path_recommendations (
    recommendation_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
    path_id UUID NOT NULL REFERENCES career_paths(path_id) ON DELETE CASCADE,
    recommendation_score DECIMAL(3,2) NOT NULL, -- 0.0-1.0 confidence score
    recommendation_reason JSONB, -- why this path was recommended
    is_viewed BOOLEAN DEFAULT FALSE,
    is_dismissed BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP WITH TIME ZONE,
    UNIQUE(user_id, path_id)
);

-- Add indexes for performance
CREATE INDEX idx_skills_area ON skills(area);
CREATE INDEX idx_skills_difficulty_level ON skills(difficulty_level);
CREATE INDEX idx_career_paths_created_by ON career_paths(created_by);
CREATE INDEX idx_career_paths_is_template ON career_paths(is_template);
CREATE INDEX idx_career_paths_is_public ON career_paths(is_public);
CREATE INDEX idx_career_paths_rating_avg ON career_paths(rating_avg);
CREATE INDEX idx_career_path_skills_path_id ON career_path_skills(path_id);
CREATE INDEX idx_career_path_skills_skill_id ON career_path_skills(skill_id);
CREATE INDEX idx_career_path_connections_path_id ON career_path_connections(path_id);
CREATE INDEX idx_user_career_paths_user_id ON user_career_paths(user_id);
CREATE INDEX idx_user_career_paths_path_id ON user_career_paths(path_id);
CREATE INDEX idx_user_career_paths_status ON user_career_paths(status);
CREATE INDEX idx_user_skill_progress_user_id ON user_skill_progress(user_id);
CREATE INDEX idx_user_skill_progress_skill_id ON user_skill_progress(skill_id);
CREATE INDEX idx_user_skill_progress_path_id ON user_skill_progress(path_id);
CREATE INDEX idx_user_skill_progress_status ON user_skill_progress(status);
CREATE INDEX idx_career_path_ratings_path_id ON career_path_ratings(path_id);
CREATE INDEX idx_career_path_ratings_user_id ON career_path_ratings(user_id);
CREATE INDEX idx_skill_course_mappings_skill_id ON skill_course_mappings(skill_id);
CREATE INDEX idx_skill_course_mappings_course_id ON skill_course_mappings(course_id);
CREATE INDEX idx_career_path_recommendations_user_id ON career_path_recommendations(user_id);
CREATE INDEX idx_career_path_recommendations_path_id ON career_path_recommendations(path_id);

-- Add updated_at triggers
CREATE TRIGGER update_skills_updated_at BEFORE UPDATE ON skills
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_career_paths_updated_at BEFORE UPDATE ON career_paths
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_skill_progress_updated_at BEFORE UPDATE ON user_skill_progress
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_career_path_ratings_updated_at BEFORE UPDATE ON career_path_ratings
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Enable RLS for career path tables
ALTER TABLE career_paths ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_career_paths ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_skill_progress ENABLE ROW LEVEL SECURITY;
ALTER TABLE career_path_ratings ENABLE ROW LEVEL SECURITY;
ALTER TABLE career_path_recommendations ENABLE ROW LEVEL SECURITY;

-- Function to update career path statistics
CREATE OR REPLACE FUNCTION update_career_path_stats()
RETURNS TRIGGER AS $$
BEGIN
    -- Update skills count
    UPDATE career_paths 
    SET skills_count = (
        SELECT COUNT(*) 
        FROM career_path_skills 
        WHERE path_id = COALESCE(NEW.path_id, OLD.path_id)
    )
    WHERE path_id = COALESCE(NEW.path_id, OLD.path_id);
    
    -- Update users count
    UPDATE career_paths 
    SET users_count = (
        SELECT COUNT(*) 
        FROM user_career_paths 
        WHERE path_id = COALESCE(NEW.path_id, OLD.path_id) 
        AND status IN ('active', 'completed')
    )
    WHERE path_id = COALESCE(NEW.path_id, OLD.path_id);
    
    -- Update average rating
    UPDATE career_paths 
    SET rating_avg = (
        SELECT COALESCE(AVG(rating), 0.0)
        FROM career_path_ratings 
        WHERE path_id = COALESCE(NEW.path_id, OLD.path_id)
    )
    WHERE path_id = COALESCE(NEW.path_id, OLD.path_id);
    
    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Create triggers to update career path statistics
CREATE TRIGGER update_career_path_stats_on_skills
    AFTER INSERT OR UPDATE OR DELETE ON career_path_skills
    FOR EACH ROW EXECUTE FUNCTION update_career_path_stats();

CREATE TRIGGER update_career_path_stats_on_users
    AFTER INSERT OR UPDATE OR DELETE ON user_career_paths
    FOR EACH ROW EXECUTE FUNCTION update_career_path_stats();

CREATE TRIGGER update_career_path_stats_on_ratings
    AFTER INSERT OR UPDATE OR DELETE ON career_path_ratings
    FOR EACH ROW EXECUTE FUNCTION update_career_path_stats();
