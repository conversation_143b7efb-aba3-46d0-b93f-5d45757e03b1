import React, { useState } from 'react';
import { Trophy, Medal, Award, TrendingUp, Users, BookOpen } from 'lucide-react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Badge } from '@/components/ui/Badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/Avatar';
import { generateInitials } from '@/lib/utils';

const leaderboardData = {
  creators: [
    {
      id: '1',
      user: {
        id: '1',
        firstName: '<PERSON>',
        lastName: '<PERSON>',
        avatar: '',
      },
      score: 2450,
      rank: 1,
      coursesCreated: 8,
      totalStudents: 1234,
      avgRating: 4.8,
    },
    {
      id: '2',
      user: {
        id: '2',
        firstName: 'Carlos',
        lastName: 'López',
        avatar: '',
      },
      score: 2180,
      rank: 2,
      coursesCreated: 6,
      totalStudents: 987,
      avgRating: 4.6,
    },
    {
      id: '3',
      user: {
        id: '3',
        firstName: 'Ana',
        lastName: '<PERSON>',
        avatar: '',
      },
      score: 1950,
      rank: 3,
      coursesCreated: 5,
      totalStudents: 756,
      avgRating: 4.9,
    },
    {
      id: '4',
      user: {
        id: '4',
        firstName: 'Luis',
        lastName: 'Silva',
        avatar: '',
      },
      score: 1720,
      rank: 4,
      coursesCreated: 4,
      totalStudents: 543,
      avgRating: 4.5,
    },
    {
      id: '5',
      user: {
        id: '5',
        firstName: 'Elena',
        lastName: 'Chen',
        avatar: '',
      },
      score: 1580,
      rank: 5,
      coursesCreated: 3,
      totalStudents: 432,
      avgRating: 4.7,
    },
  ],
  students: [
    {
      id: '1',
      user: {
        id: '6',
        firstName: 'Pedro',
        lastName: 'Rodríguez',
        avatar: '',
      },
      score: 3200,
      rank: 1,
      coursesCompleted: 12,
      currentStreak: 45,
      totalHours: 156,
    },
    {
      id: '2',
      user: {
        id: '7',
        firstName: 'Sofia',
        lastName: 'Hernández',
        avatar: '',
      },
      score: 2890,
      rank: 2,
      coursesCompleted: 10,
      currentStreak: 32,
      totalHours: 134,
    },
    {
      id: '3',
      user: {
        id: '8',
        firstName: 'Diego',
        lastName: 'Morales',
        avatar: '',
      },
      score: 2650,
      rank: 3,
      coursesCompleted: 9,
      currentStreak: 28,
      totalHours: 121,
    },
    {
      id: '4',
      user: {
        id: '9',
        firstName: 'Carmen',
        lastName: 'Ruiz',
        avatar: '',
      },
      score: 2420,
      rank: 4,
      coursesCompleted: 8,
      currentStreak: 25,
      totalHours: 98,
    },
    {
      id: '5',
      user: {
        id: '10',
        firstName: 'Javier',
        lastName: 'Torres',
        avatar: '',
      },
      score: 2180,
      rank: 5,
      coursesCompleted: 7,
      currentStreak: 22,
      totalHours: 87,
    },
  ],
};

export default function LeaderboardPage() {
  const [activeCategory, setActiveCategory] = useState<'creators' | 'students'>('students');
  const [activePeriod, setActivePeriod] = useState<'weekly' | 'monthly' | 'total'>('total');

  const getRankIcon = (rank: number) => {
    switch (rank) {
      case 1:
        return <Trophy className="w-6 h-6 text-yellow-500" />;
      case 2:
        return <Medal className="w-6 h-6 text-gray-400" />;
      case 3:
        return <Award className="w-6 h-6 text-amber-600" />;
      default:
        return <span className="w-6 h-6 flex items-center justify-center text-sm font-bold text-gray-500">#{rank}</span>;
    }
  };

  const getRankBadgeColor = (rank: number) => {
    switch (rank) {
      case 1:
        return 'bg-yellow-100 text-yellow-800';
      case 2:
        return 'bg-gray-100 text-gray-800';
      case 3:
        return 'bg-amber-100 text-amber-800';
      default:
        return 'bg-blue-100 text-blue-800';
    }
  };

  const currentData = activeCategory === 'creators' ? leaderboardData.creators : leaderboardData.students;

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900">Leaderboard</h1>
        <p className="text-gray-600 mt-2">
          Compite con otros usuarios y ve tu posición en el ranking
        </p>
      </div>

      {/* Controls */}
      <div className="mb-8 flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        {/* Category Tabs */}
        <div className="flex space-x-1 bg-gray-100 rounded-lg p-1">
          <button
            onClick={() => setActiveCategory('students')}
            className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
              activeCategory === 'students'
                ? 'bg-white text-gray-900 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            Estudiantes
          </button>
          <button
            onClick={() => setActiveCategory('creators')}
            className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
              activeCategory === 'creators'
                ? 'bg-white text-gray-900 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            Creadores
          </button>
        </div>

        {/* Period Filter */}
        <div className="flex space-x-1 bg-gray-100 rounded-lg p-1">
          <button
            onClick={() => setActivePeriod('weekly')}
            className={`px-3 py-1 rounded text-sm font-medium transition-colors ${
              activePeriod === 'weekly'
                ? 'bg-white text-gray-900 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            Semanal
          </button>
          <button
            onClick={() => setActivePeriod('monthly')}
            className={`px-3 py-1 rounded text-sm font-medium transition-colors ${
              activePeriod === 'monthly'
                ? 'bg-white text-gray-900 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            Mensual
          </button>
          <button
            onClick={() => setActivePeriod('total')}
            className={`px-3 py-1 rounded text-sm font-medium transition-colors ${
              activePeriod === 'total'
                ? 'bg-white text-gray-900 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            Total
          </button>
        </div>
      </div>

      {/* Top 3 Podium */}
      <div className="mb-8">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {currentData.slice(0, 3).map((entry, index) => (
            <Card key={entry.id} className={`text-center ${index === 0 ? 'md:order-2 ring-2 ring-yellow-400' : index === 1 ? 'md:order-1' : 'md:order-3'}`}>
              <CardContent className="p-6">
                <div className="flex justify-center mb-4">
                  {getRankIcon(entry.rank)}
                </div>
                
                <Avatar className="w-16 h-16 mx-auto mb-4">
                  <AvatarImage src={entry.user.avatar} alt={entry.user.firstName} />
                  <AvatarFallback className="bg-blue-500 text-white text-lg font-medium">
                    {generateInitials(entry.user.firstName, entry.user.lastName)}
                  </AvatarFallback>
                </Avatar>
                
                <h3 className="text-lg font-semibold text-gray-900 mb-1">
                  {entry.user.firstName} {entry.user.lastName}
                </h3>
                
                <Badge className={`mb-3 ${getRankBadgeColor(entry.rank)}`}>
                  {entry.score.toLocaleString()} puntos
                </Badge>
                
                {activeCategory === 'creators' && 'coursesCreated' in entry && (
                  <div className="text-sm text-gray-600 space-y-1">
                    <div className="flex items-center justify-center">
                      <BookOpen className="w-4 h-4 mr-1" />
                      {entry.coursesCreated} cursos
                    </div>
                    <div className="flex items-center justify-center">
                      <Users className="w-4 h-4 mr-1" />
                      {entry.totalStudents} estudiantes
                    </div>
                  </div>
                )}
                
                {activeCategory === 'students' && 'coursesCompleted' in entry && (
                  <div className="text-sm text-gray-600 space-y-1">
                    <div className="flex items-center justify-center">
                      <BookOpen className="w-4 h-4 mr-1" />
                      {entry.coursesCompleted} completados
                    </div>
                    <div className="flex items-center justify-center">
                      <TrendingUp className="w-4 h-4 mr-1" />
                      {entry.currentStreak} días seguidos
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Full Leaderboard */}
      <Card>
        <CardHeader>
          <CardTitle>
            Ranking Completo - {activeCategory === 'creators' ? 'Creadores' : 'Estudiantes'}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {currentData.map((entry) => (
              <div key={entry.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                <div className="flex items-center space-x-4">
                  <div className="flex-shrink-0">
                    {getRankIcon(entry.rank)}
                  </div>
                  
                  <Avatar className="w-10 h-10">
                    <AvatarImage src={entry.user.avatar} alt={entry.user.firstName} />
                    <AvatarFallback className="bg-blue-500 text-white text-sm font-medium">
                      {generateInitials(entry.user.firstName, entry.user.lastName)}
                    </AvatarFallback>
                  </Avatar>
                  
                  <div>
                    <h4 className="text-sm font-medium text-gray-900">
                      {entry.user.firstName} {entry.user.lastName}
                    </h4>
                    <p className="text-xs text-gray-500">
                      {entry.score.toLocaleString()} puntos
                    </p>
                  </div>
                </div>
                
                <div className="flex items-center space-x-6 text-sm text-gray-600">
                  {activeCategory === 'creators' && 'coursesCreated' in entry && (
                    <>
                      <div className="text-center">
                        <div className="font-medium">{entry.coursesCreated}</div>
                        <div className="text-xs">Cursos</div>
                      </div>
                      <div className="text-center">
                        <div className="font-medium">{entry.totalStudents}</div>
                        <div className="text-xs">Estudiantes</div>
                      </div>
                      <div className="text-center">
                        <div className="font-medium">{entry.avgRating}</div>
                        <div className="text-xs">Rating</div>
                      </div>
                    </>
                  )}
                  
                  {activeCategory === 'students' && 'coursesCompleted' in entry && (
                    <>
                      <div className="text-center">
                        <div className="font-medium">{entry.coursesCompleted}</div>
                        <div className="text-xs">Completados</div>
                      </div>
                      <div className="text-center">
                        <div className="font-medium">{entry.currentStreak}</div>
                        <div className="text-xs">Racha</div>
                      </div>
                      <div className="text-center">
                        <div className="font-medium">{entry.totalHours}h</div>
                        <div className="text-xs">Tiempo</div>
                      </div>
                    </>
                  )}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
