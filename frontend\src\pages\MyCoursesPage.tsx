import React, { useState } from 'react';
import { <PERSON><PERSON><PERSON>, Clock, CheckCircle, Play } from 'lucide-react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Badge } from '@/components/ui/Badge';
import { Button } from '@/components/ui/Button';
import { CourseGrid } from '@/components/courses/CourseGrid';
import { Course } from '@/types';

const enrolledCourses: Course[] = [
  {
    id: 'devops-1',
    title: 'DevOps Fundamentals',
    description: 'Aprende los principios fundamentales de DevOps, CI/CD, containerización y automatización.',
    category: 'Tecnología',
    level: 'intermediate',
    duration: 8,
    studentCount: 156,
    rating: 4.8,
    creator: { id: '1', firstName: 'Prof. García', lastName: 'García' },
    status: 'published',
    tenantId: '1',
    creatorId: '1',
    metadata: { progress: 75, lastAccessed: '2024-01-15' },
    version: 1,
    createdAt: '2024-01-01',
    updatedAt: '2024-01-01',
  },
  {
    id: 'ai-basics-1',
    title: 'Introducción a la Inteligencia Artificial',
    description: 'Conceptos básicos de IA, machine learning y aplicaciones prácticas en el mundo real.',
    category: 'Tecnología',
    level: 'beginner',
    duration: 6,
    studentCount: 243,
    rating: 4.6,
    creator: { id: '2', firstName: 'Dra. Martínez', lastName: 'Martínez' },
    status: 'published',
    tenantId: '1',
    creatorId: '2',
    metadata: { progress: 45, lastAccessed: '2024-01-14' },
    version: 1,
    createdAt: '2024-01-01',
    updatedAt: '2024-01-01',
  },
  {
    id: 'prompt-eng-1',
    title: 'Prompt Engineering Avanzado',
    description: 'Domina el arte de crear prompts efectivos para modelos de IA y maximiza su potencial.',
    category: 'Tecnología',
    level: 'advanced',
    duration: 4,
    studentCount: 89,
    rating: 4.9,
    creator: { id: '3', firstName: 'Ing. López', lastName: 'López' },
    status: 'published',
    tenantId: '1',
    creatorId: '3',
    metadata: { progress: 90, lastAccessed: '2024-01-13' },
    version: 1,
    createdAt: '2024-01-01',
    updatedAt: '2024-01-01',
  },
];

const completedCourses: Course[] = [
  {
    id: 'python-1',
    title: 'Python para Principiantes',
    description: 'Fundamentos de programación en Python',
    category: 'Tecnología',
    level: 'beginner',
    duration: 10,
    studentCount: 312,
    rating: 4.8,
    creator: { id: '4', firstName: 'Prof. Silva', lastName: 'Silva' },
    status: 'published',
    tenantId: '1',
    creatorId: '4',
    metadata: { progress: 100, completedAt: '2024-01-10' },
    version: 1,
    createdAt: '2024-01-01',
    updatedAt: '2024-01-01',
  },
];

export default function MyCoursesPage() {
  const [activeTab, setActiveTab] = useState<'in-progress' | 'completed' | 'created'>('in-progress');

  const getProgressColor = (progress: number) => {
    if (progress >= 80) return 'bg-green-500';
    if (progress >= 50) return 'bg-yellow-500';
    return 'bg-blue-500';
  };

  const formatLastAccessed = (date: string) => {
    const now = new Date();
    const accessDate = new Date(date);
    const diffInDays = Math.floor((now.getTime() - accessDate.getTime()) / (1000 * 60 * 60 * 24));
    
    if (diffInDays === 0) return 'Hoy';
    if (diffInDays === 1) return 'Ayer';
    return `Hace ${diffInDays} días`;
  };

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900">Mis Cursos</h1>
        <p className="text-gray-600 mt-2">
          Gestiona tus cursos inscritos, completados y creados
        </p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0 p-3 rounded-lg bg-blue-100">
                <BookOpen className="h-6 w-6 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">En Progreso</p>
                <p className="text-2xl font-semibold text-gray-900">
                  {enrolledCourses.length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0 p-3 rounded-lg bg-green-100">
                <CheckCircle className="h-6 w-6 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Completados</p>
                <p className="text-2xl font-semibold text-gray-900">
                  {completedCourses.length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0 p-3 rounded-lg bg-purple-100">
                <Clock className="h-6 w-6 text-purple-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Horas Totales</p>
                <p className="text-2xl font-semibold text-gray-900">156</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Tabs */}
      <div className="mb-8">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8">
            <button
              onClick={() => setActiveTab('in-progress')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'in-progress'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              En Progreso ({enrolledCourses.length})
            </button>
            <button
              onClick={() => setActiveTab('completed')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'completed'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Completados ({completedCourses.length})
            </button>
            <button
              onClick={() => setActiveTab('created')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'created'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Mis Creaciones (0)
            </button>
          </nav>
        </div>
      </div>

      {/* In Progress Tab */}
      {activeTab === 'in-progress' && (
        <div className="space-y-6">
          {enrolledCourses.map((course) => (
            <Card key={course.id}>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="flex items-start justify-between mb-4">
                      <div>
                        <h3 className="text-lg font-medium text-gray-900 mb-1">
                          {course.title}
                        </h3>
                        <p className="text-gray-600 text-sm mb-2">
                          {course.description}
                        </p>
                        <div className="flex items-center space-x-4 text-sm text-gray-500">
                          <Badge variant="outline">{course.category}</Badge>
                          <span>👨‍🏫 {course.creator.firstName}</span>
                          <span>Último acceso: {formatLastAccessed(course.metadata?.lastAccessed || '')}</span>
                        </div>
                      </div>
                      <Button size="sm">
                        <Play className="w-4 h-4 mr-2" />
                        Continuar
                      </Button>
                    </div>
                    
                    {/* Progress Bar */}
                    <div className="flex items-center space-x-4">
                      <div className="flex-1">
                        <div className="flex items-center justify-between text-sm mb-1">
                          <span className="text-gray-600">Progreso</span>
                          <span className="font-medium">{course.metadata?.progress || 0}%</span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div
                            className={`h-2 rounded-full ${getProgressColor(course.metadata?.progress || 0)}`}
                            style={{ width: `${course.metadata?.progress || 0}%` }}
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Completed Tab */}
      {activeTab === 'completed' && (
        <div>
          <CourseGrid
            courses={completedCourses}
            variant="compact"
            columns={3}
          />
        </div>
      )}

      {/* Created Tab */}
      {activeTab === 'created' && (
        <div className="text-center py-12">
          <div className="w-24 h-24 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
            <BookOpen className="w-12 h-12 text-gray-400" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            No has creado cursos aún
          </h3>
          <p className="text-gray-500 mb-4">
            Comienza a crear contenido educativo para compartir tu conocimiento
          </p>
          <Button>
            Crear Primer Curso
          </Button>
        </div>
      )}
    </div>
  );
}
