import React, { useState } from 'react';
import { Plus, TrendingUp, Clock, Users, Star } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/Button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Badge } from '@/components/ui/Badge';

const careerPaths = [
  {
    id: '1',
    name: 'Full Stack Developer',
    description: 'Conviértete en un desarrollador completo con habilidades en frontend y backend',
    skills: ['JavaScript', 'React', 'Node.js', 'Databases', 'DevOps'],
    estimatedDuration: 12,
    difficulty: 'intermediate',
    studentsEnrolled: 234,
    rating: 4.8,
    isPublic: true,
    createdBy: 'Prof<PERSON> <PERSON>',
  },
  {
    id: '2',
    name: 'Data Scientist',
    description: 'Domina el análisis de datos, machine learning y visualización',
    skills: ['Python', 'Statistics', 'Machine Learning', 'SQL', 'Visualization'],
    estimatedDuration: 18,
    difficulty: 'advanced',
    studentsEnrolled: 156,
    rating: 4.9,
    isPublic: true,
    createdBy: '<PERSON><PERSON><PERSON> <PERSON>',
  },
  {
    id: '3',
    name: 'DevOps Engineer',
    description: 'Aprende automatización, CI/CD y gestión de infraestructura',
    skills: ['Docker', 'Kubernetes', 'AWS', 'CI/CD', 'Monitoring'],
    estimatedDuration: 10,
    difficulty: 'advanced',
    studentsEnrolled: 89,
    rating: 4.7,
    isPublic: true,
    createdBy: 'Ing. López',
  },
  {
    id: '4',
    name: 'UX/UI Designer',
    description: 'Diseña experiencias de usuario excepcionales',
    skills: ['Design Thinking', 'Figma', 'User Research', 'Prototyping', 'Usability'],
    estimatedDuration: 8,
    difficulty: 'beginner',
    studentsEnrolled: 312,
    rating: 4.6,
    isPublic: true,
    createdBy: 'Prof. Silva',
  },
];

const skillCategories = [
  'Tecnología',
  'Diseño',
  'Negocios',
  'Marketing',
  'Ciencias',
  'Idiomas',
];

export default function CareerPathsPage() {
  const [activeTab, setActiveTab] = useState<'explore' | 'my-paths' | 'builder'>('explore');

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'beginner':
        return 'badge-green';
      case 'intermediate':
        return 'badge-yellow';
      case 'advanced':
        return 'badge-red';
      default:
        return 'badge-gray';
    }
  };

  const getDifficultyLabel = (difficulty: string) => {
    switch (difficulty) {
      case 'beginner':
        return 'Principiante';
      case 'intermediate':
        return 'Intermedio';
      case 'advanced':
        return 'Avanzado';
      default:
        return difficulty;
    }
  };

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900">Career Paths</h1>
        <p className="text-gray-600 mt-2">
          Planifica tu carrera profesional con rutas de aprendizaje estructuradas
        </p>
      </div>

      {/* Tabs */}
      <div className="mb-8">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8">
            <button
              onClick={() => setActiveTab('explore')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'explore'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Explorar Rutas
            </button>
            <button
              onClick={() => setActiveTab('my-paths')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'my-paths'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Mis Rutas
            </button>
            <button
              onClick={() => setActiveTab('builder')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'builder'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Crear Ruta
            </button>
          </nav>
        </div>
      </div>

      {/* Explore Tab */}
      {activeTab === 'explore' && (
        <div>
          {/* Featured Career Paths */}
          <div className="mb-8">
            <h3 className="text-lg font-medium text-gray-900 mb-4">
              Rutas Destacadas
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {careerPaths.map((path) => (
                <Card key={path.id} className="hover:shadow-lg transition-shadow">
                  <CardHeader>
                    <div className="flex items-start justify-between">
                      <div>
                        <CardTitle className="text-lg">{path.name}</CardTitle>
                        <p className="text-sm text-gray-600 mt-1">
                          por {path.createdBy}
                        </p>
                      </div>
                      <Badge variant={getDifficultyColor(path.difficulty) as any}>
                        {getDifficultyLabel(path.difficulty)}
                      </Badge>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-600 mb-4">{path.description}</p>
                    
                    {/* Skills */}
                    <div className="mb-4">
                      <p className="text-sm font-medium text-gray-700 mb-2">
                        Habilidades que desarrollarás:
                      </p>
                      <div className="flex flex-wrap gap-1">
                        {path.skills.slice(0, 3).map((skill) => (
                          <Badge key={skill} variant="outline" className="text-xs">
                            {skill}
                          </Badge>
                        ))}
                        {path.skills.length > 3 && (
                          <Badge variant="outline" className="text-xs">
                            +{path.skills.length - 3} más
                          </Badge>
                        )}
                      </div>
                    </div>

                    {/* Stats */}
                    <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                      <div className="flex items-center">
                        <Clock className="w-4 h-4 mr-1" />
                        {path.estimatedDuration} meses
                      </div>
                      <div className="flex items-center">
                        <Users className="w-4 h-4 mr-1" />
                        {path.studentsEnrolled} estudiantes
                      </div>
                      <div className="flex items-center">
                        <Star className="w-4 h-4 mr-1 fill-current text-yellow-400" />
                        {path.rating}
                      </div>
                    </div>

                    <Button className="w-full">
                      Comenzar Ruta
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* My Paths Tab */}
      {activeTab === 'my-paths' && (
        <div className="text-center py-12">
          <div className="w-24 h-24 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
            <TrendingUp className="w-12 h-12 text-gray-400" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            No tienes rutas activas
          </h3>
          <p className="text-gray-500 mb-4">
            Comienza una ruta de carrera para ver tu progreso aquí
          </p>
          <Button onClick={() => setActiveTab('explore')}>
            Explorar Rutas
          </Button>
        </div>
      )}

      {/* Builder Tab */}
      {activeTab === 'builder' && (
        <div>
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Plus className="w-5 h-5 mr-2" />
                Constructor de Rutas de Carrera
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-12">
                <div className="w-24 h-24 mx-auto mb-4 bg-blue-100 rounded-full flex items-center justify-center">
                  <svg className="w-12 h-12 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                  </svg>
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  Constructor de Rutas
                </h3>
                <p className="text-gray-500 mb-4">
                  Crea rutas de carrera personalizadas conectando habilidades y cursos
                </p>
                <Button>
                  Comenzar a Construir
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
}
