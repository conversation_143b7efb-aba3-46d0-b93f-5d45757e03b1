# Modelo de Datos - Arroyo University

## Introducción

Este documento describe el modelo de datos relacional de Arroyo University, incluyendo todas las entidades, relaciones, constraints y optimizaciones. El modelo está diseñado para soportar multi-tenancy, escalabilidad y extensibilidad futura.

---

## 1. Principios de Diseño

### 1.1 Multi-Tenancy
- **Shared Database, Shared Schema**: Todas las tablas incluyen `tenant_id`
- **Row-Level Security (RLS)**: Aislamiento automático por tenant
- **Índices compuestos**: Optimización de queries por tenant

### 1.2 Escalabilidad
- **UUIDs**: Claves primarias distribuidas
- **Particionamiento**: Preparado para sharding futuro
- **JSONB**: Flexibilidad para metadata sin cambios de schema

### 1.3 Auditoría y Compliance
- **Timestamps**: created_at, updated_at en todas las entidades
- **Soft Deletes**: deleted_at para cumplimiento GDPR
- **Audit Logs**: Trazabilidad completa de cambios

---

## 2. Entidades Fundamentales

### 2.1 Gestión Multi-Tenant

#### Tenants
```sql
CREATE TABLE tenants (
    tenant_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL,
    description TEXT,
    plan VARCHAR(30) DEFAULT 'free',
    ia_tokens_used BIGINT DEFAULT 0,
    settings JSONB,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP
);
```

**Propósito**: Entidad raíz para aislamiento multi-tenant
**Campos clave**:
- `plan`: free, basic, premium, enterprise
- `ia_tokens_used`: Control de cuota de IA
- `settings`: Configuración personalizable por tenant

#### GeneralConfigurations
```sql
CREATE TABLE general_configurations (
    config_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID REFERENCES tenants(tenant_id),
    config_key VARCHAR(100) NOT NULL,
    config_value TEXT NOT NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

**Propósito**: Configuración flexible por tenant
**Ejemplos**: branding, límites, integraciones

### 2.2 Usuarios y Seguridad

#### Users
```sql
CREATE TABLE users (
    user_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID REFERENCES tenants(tenant_id),
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash TEXT NOT NULL,
    verified BOOLEAN DEFAULT FALSE,
    mfa_secret VARCHAR(64),
    is_active BOOLEAN DEFAULT TRUE,
    last_login TIMESTAMP,
    preferences JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

**Propósito**: Gestión de usuarios del sistema
**Seguridad**: 
- `password_hash`: bcrypt con salt
- `mfa_secret`: TOTP secret para 2FA
- `verified`: Email verification required

#### Roles y Permissions
```sql
CREATE TABLE roles (
    role_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID REFERENCES tenants(tenant_id),
    name VARCHAR(50) NOT NULL,
    description TEXT,
    version INT DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE permissions (
    permission_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    category VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

**Propósito**: RBAC granular y editable
**Características**:
- Roles versionados para auditoría
- Permisos categorizados para UX tipo Discord

### 2.3 Contenido Educativo

#### Courses
```sql
CREATE TABLE courses (
    course_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID REFERENCES tenants(tenant_id),
    title VARCHAR(150) NOT NULL,
    description TEXT,
    difficulty_level VARCHAR(50), -- beginner, intermediate, advanced, expert
    estimated_hours INTEGER,
    status VARCHAR(20) DEFAULT 'draft', -- draft, published, archived
    metadata JSONB,
    version INT DEFAULT 1,
    created_by UUID REFERENCES users(user_id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

**Propósito**: Gestión de cursos y contenido educativo
**Estados**: draft → published → archived
**Metadata**: Configuración flexible (prerrequisitos, tags, etc.)

#### Course Modules
```sql
CREATE TABLE course_modules (
    module_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    course_id UUID NOT NULL REFERENCES courses(course_id) ON DELETE CASCADE,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    order_index INTEGER NOT NULL,
    estimated_hours INTEGER,
    is_required BOOLEAN DEFAULT TRUE,
    metadata JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(course_id, order_index)
);
```

**Propósito**: Módulos/secciones dentro de cada curso
**Flexibilidad**: Orden configurable y contenido opcional/requerido
**Estructura**: Permite múltiples módulos por curso con progreso independiente

#### Module Content
```sql
CREATE TABLE module_content (
    content_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    module_id UUID NOT NULL REFERENCES course_modules(module_id) ON DELETE CASCADE,
    title VARCHAR(255) NOT NULL,
    content_type VARCHAR(50) NOT NULL, -- video, document, assignment, reading, test
    content_url TEXT,
    content_data JSONB, -- metadata específico del tipo de contenido
    order_index INTEGER NOT NULL,
    estimated_minutes INTEGER,
    is_required BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(module_id, order_index)
);
```

**Propósito**: Contenido específico dentro de cada módulo
**Tipos**: Videos, documentos, assignments, tests, etc.
**Flexibilidad**: content_data permite metadata específica por tipo

#### Course Collaborators
```sql
CREATE TABLE course_collaborators (
    collaboration_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    course_id UUID NOT NULL REFERENCES courses(course_id),
    user_id UUID NOT NULL REFERENCES users(user_id),
    role VARCHAR(20) NOT NULL, -- creator, collaborator, grader
    permissions JSONB, -- specific permissions for this collaboration
    added_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    added_by UUID REFERENCES users(user_id),
    UNIQUE(course_id, user_id)
);
```

**Propósito**: Gestión de colaboradores en cursos
**Roles**: creator (creador), collaborator (puede editar y calificar), grader (solo calificar)
**Permisos**: Flexibilidad para permisos granulares por colaborador

#### Course Ratings
```sql
CREATE TABLE course_ratings (
    rating_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    course_id UUID NOT NULL REFERENCES courses(course_id),
    user_id UUID NOT NULL REFERENCES users(user_id),
    rating_type VARCHAR(10) NOT NULL, -- like, dislike
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(course_id, user_id)
);
```

**Propósito**: Sistema de likes/dislikes para cursos
**Constraint**: Un usuario solo puede dar un like o dislike por curso
**Ordenamiento**: Permite ordenar cursos por popularidad

#### Course Reports
```sql
CREATE TABLE course_reports (
    report_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    course_id UUID NOT NULL REFERENCES courses(course_id),
    reported_by UUID NOT NULL REFERENCES users(user_id),
    reason VARCHAR(50) NOT NULL,
    comment TEXT NOT NULL,
    status VARCHAR(20) DEFAULT 'pending', -- pending, reviewed, resolved, dismissed
    reviewed_by UUID REFERENCES users(user_id),
    reviewed_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

**Propósito**: Sistema de reportes para cursos problemáticos
**Estados**: pending → reviewed → resolved/dismissed
**Moderación**: Tracking de quién revisa y resuelve reportes

#### Expert Areas
```sql
CREATE TABLE expert_areas (
    expert_area_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(user_id),
    area_name VARCHAR(100) NOT NULL, -- DevOps, AI, Data Science, etc.
    assigned_by UUID NOT NULL REFERENCES users(user_id), -- Admin Tenant who assigned
    assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE,
    UNIQUE(user_id, area_name)
);
```

**Propósito**: Gestión de usuarios expertos por área de conocimiento
**Asignación**: Solo Admin Tenant puede asignar roles de experto
**Áreas**: Flexibles, definidas por Admin Tenant según necesidades

#### Expert Reviews
```sql
CREATE TABLE expert_reviews (
    review_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    course_id UUID NOT NULL REFERENCES courses(course_id),
    expert_user_id UUID NOT NULL REFERENCES users(user_id),
    expert_area VARCHAR(100) NOT NULL,
    rating VARCHAR(20) NOT NULL, -- muy_malo, malo, neutral, bueno, excelente
    title VARCHAR(200) NOT NULL,
    review_text TEXT NOT NULL,
    is_verified BOOLEAN DEFAULT FALSE,
    verified_by UUID REFERENCES users(user_id), -- Admin Tenant who verified
    verified_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(course_id, expert_user_id)
);
```

**Propósito**: Reviews de expertos para cursos con calificación tipo Steam
**Ratings**: muy_malo, malo, neutral, bueno, excelente
**Verificación**: Admin Tenant debe verificar reviews antes de mostrar

#### Forum Posts
```sql
CREATE TABLE forum_posts (
    post_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    course_id UUID NOT NULL REFERENCES courses(course_id),
    user_id UUID NOT NULL REFERENCES users(user_id),
    title VARCHAR(200) NOT NULL,
    content TEXT NOT NULL,
    category VARCHAR(50) DEFAULT 'general', -- general, technical, resources, discussion
    is_highlighted BOOLEAN DEFAULT FALSE, -- for expert highlighted messages
    highlighted_by UUID REFERENCES users(user_id), -- expert who highlighted
    highlighted_at TIMESTAMP,
    likes_count INTEGER DEFAULT 0,
    replies_count INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

**Propósito**: Sistema de foros por curso
**Destacados**: Expertos pueden destacar mensajes importantes
**Categorías**: Organización de posts por tipo de contenido

#### Forum Replies
```sql
CREATE TABLE forum_replies (
    reply_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    post_id UUID NOT NULL REFERENCES forum_posts(post_id),
    user_id UUID NOT NULL REFERENCES users(user_id),
    content TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

**Propósito**: Respuestas a posts del foro
**Jerarquía**: Estructura simple de post → replies

#### Forum Likes
```sql
CREATE TABLE forum_likes (
    like_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    post_id UUID REFERENCES forum_posts(post_id),
    reply_id UUID REFERENCES forum_replies(reply_id),
    user_id UUID NOT NULL REFERENCES users(user_id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(post_id, user_id),
    UNIQUE(reply_id, user_id),
    CHECK ((post_id IS NOT NULL AND reply_id IS NULL) OR (post_id IS NULL AND reply_id IS NOT NULL))
);
```

**Propósito**: Sistema de likes para posts y replies
**Constraint**: Un usuario solo puede dar like a un post o reply específico
**Flexibilidad**: Permite likes tanto en posts como en replies

#### Skills Repository
```sql
CREATE TABLE skills (
    skill_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL,
    description TEXT,
    area VARCHAR(50) NOT NULL, -- programming, devops, data, security, management, design, marketing
    color_class VARCHAR(50), -- CSS class for visual representation
    prerequisites JSONB, -- array of prerequisite skill IDs
    difficulty_level VARCHAR(20) DEFAULT 'intermediate', -- beginner, intermediate, advanced
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(name, area)
);
```

**Propósito**: Repositorio central de skills para career paths
**Áreas**: Categorización por dominio de conocimiento
**Prerequisites**: Relaciones entre skills para crear dependencias
**Visual**: Color coding para identificación rápida en diagramas

#### Career Paths
```sql
CREATE TABLE career_paths (
    path_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(200) NOT NULL,
    description TEXT,
    from_position VARCHAR(100), -- starting position/role
    to_position VARCHAR(100), -- target position/role
    estimated_duration_months INTEGER,
    difficulty_level VARCHAR(20) DEFAULT 'intermediate',
    is_template BOOLEAN DEFAULT FALSE, -- true for predefined paths, false for custom
    created_by UUID REFERENCES users(user_id),
    is_public BOOLEAN DEFAULT TRUE,
    rating_avg DECIMAL(3,2) DEFAULT 0.00,
    users_count INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

**Propósito**: Definición de rutas de carrera profesional
**Templates**: Paths predefinidos vs personalizados por usuarios
**Métricas**: Rating y conteo de usuarios para popularidad
**Flexibilidad**: Público/privado según preferencias del creador

#### Career Path Skills
```sql
CREATE TABLE career_path_skills (
    path_skill_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    path_id UUID NOT NULL REFERENCES career_paths(path_id),
    skill_id UUID NOT NULL REFERENCES skills(skill_id),
    position_x INTEGER NOT NULL, -- X coordinate in diagram
    position_y INTEGER NOT NULL, -- Y coordinate in diagram
    is_starting_skill BOOLEAN DEFAULT FALSE,
    is_target_skill BOOLEAN DEFAULT FALSE,
    order_sequence INTEGER, -- suggested learning order
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(path_id, skill_id)
);
```

**Propósito**: Skills incluidas en cada career path con posicionamiento
**Coordinates**: Posición en el diagrama visual tipo UML
**Markers**: Identificación de skills de inicio y objetivo
**Sequence**: Orden sugerido de aprendizaje

#### Career Path Connections
```sql
CREATE TABLE career_path_connections (
    connection_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    path_id UUID NOT NULL REFERENCES career_paths(path_id),
    from_skill_id UUID NOT NULL REFERENCES skills(skill_id),
    to_skill_id UUID NOT NULL REFERENCES skills(skill_id),
    connection_type VARCHAR(20) DEFAULT 'prerequisite', -- prerequisite, related, progression
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(path_id, from_skill_id, to_skill_id)
);
```

**Propósito**: Conexiones entre skills en career paths
**Types**: Diferentes tipos de relaciones (prerequisito, relacionado, progresión)
**Visualization**: Base para dibujar líneas conectoras en diagramas

#### User Career Paths
```sql
CREATE TABLE user_career_paths (
    user_path_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(user_id),
    path_id UUID NOT NULL REFERENCES career_paths(path_id),
    status VARCHAR(20) DEFAULT 'active', -- active, completed, paused, abandoned
    progress_percentage DECIMAL(5,2) DEFAULT 0.00,
    skills_completed INTEGER DEFAULT 0,
    skills_total INTEGER NOT NULL,
    started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP,
    last_activity_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, path_id)
);
```

**Propósito**: Tracking de progreso de usuarios en career paths
**Progress**: Porcentaje y conteo de skills completadas
**States**: Estados del career path para el usuario
**Activity**: Tracking de última actividad para recomendaciones

#### User Skill Progress
```sql
CREATE TABLE user_skill_progress (
    progress_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(user_id),
    skill_id UUID NOT NULL REFERENCES skills(skill_id),
    path_id UUID REFERENCES career_paths(path_id), -- null if skill learned outside path
    status VARCHAR(20) DEFAULT 'not_started', -- not_started, in_progress, completed, verified
    completion_date TIMESTAMP,
    verification_method VARCHAR(50), -- course_completion, certification, manual, expert_review
    verified_by UUID REFERENCES users(user_id), -- expert or admin who verified
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, skill_id)
);
```

**Propósito**: Progreso individual de usuarios en skills específicas
**Verification**: Diferentes métodos de validación de competencia
**Tracking**: Historial completo de aprendizaje por skill
**Context**: Relación con career path donde se aprendió la skill

#### User Scores
```sql
CREATE TABLE user_scores (
    score_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(user_id),
    score_type VARCHAR(20) NOT NULL, -- student, creator
    period_type VARCHAR(20) NOT NULL, -- weekly, monthly, alltime
    period_start DATE NOT NULL,
    period_end DATE,
    total_points INTEGER DEFAULT 0,
    position_rank INTEGER,
    courses_completed INTEGER DEFAULT 0,
    courses_created INTEGER DEFAULT 0,
    forum_posts INTEGER DEFAULT 0,
    forum_likes_received INTEGER DEFAULT 0,
    course_ratings_given INTEGER DEFAULT 0,
    career_path_bonus_points INTEGER DEFAULT 0,
    expert_validation_bonus INTEGER DEFAULT 0,
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, score_type, period_type, period_start)
);
```

**Propósito**: Sistema de puntuación y ranking para gamificación
**Types**: Separación entre puntos de estudiante y creador
**Periods**: Tracking semanal, mensual y total histórico
**Metrics**: Desglose detallado de fuentes de puntos
**Ranking**: Posición calculada dentro de cada período

#### Score Transactions
```sql
CREATE TABLE score_transactions (
    transaction_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(user_id),
    score_type VARCHAR(20) NOT NULL, -- student, creator
    action_type VARCHAR(50) NOT NULL, -- course_completion, course_creation, forum_post, etc.
    points_awarded INTEGER NOT NULL,
    bonus_multiplier DECIMAL(3,2) DEFAULT 1.00,
    final_points INTEGER NOT NULL,
    reference_id UUID, -- ID of course, post, etc. that generated points
    reference_type VARCHAR(50), -- course, forum_post, course_rating, etc.
    metadata JSONB, -- additional context (difficulty level, expert validation, etc.)
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

**Propósito**: Historial detallado de todas las transacciones de puntos
**Audit Trail**: Tracking completo para transparencia y debugging
**Bonus System**: Multiplicadores por career paths, validaciones de expertos, etc.
**References**: Conexión con entidades que generaron los puntos
**Metadata**: Información adicional para cálculos complejos

#### Leaderboard Cache
```sql
CREATE TABLE leaderboard_cache (
    cache_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    score_type VARCHAR(20) NOT NULL, -- student, creator
    period_type VARCHAR(20) NOT NULL, -- weekly, monthly, alltime
    period_start DATE NOT NULL,
    period_end DATE,
    user_id UUID NOT NULL REFERENCES users(user_id),
    total_points INTEGER NOT NULL,
    position_rank INTEGER NOT NULL,
    previous_rank INTEGER,
    rank_change INTEGER, -- positive = moved up, negative = moved down
    percentile DECIMAL(5,2), -- user's percentile in this leaderboard
    generated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(score_type, period_type, period_start, user_id)
);
```

**Propósito**: Cache optimizado para consultas rápidas de leaderboard
**Performance**: Evita cálculos complejos en tiempo real
**Trends**: Tracking de cambios de posición entre períodos
**Percentiles**: Información estadística adicional para usuarios
**Regeneration**: Timestamp para invalidación y regeneración de cache

#### Expert Validations
```sql
CREATE TABLE expert_validations (
    validation_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    expert_id UUID NOT NULL REFERENCES users(user_id),
    target_type VARCHAR(50) NOT NULL, -- course, skill_completion, career_path
    target_id UUID NOT NULL,
    validation_type VARCHAR(50) NOT NULL, -- difficulty_assessment, quality_review, skill_verification
    validation_result VARCHAR(20) NOT NULL, -- approved, rejected, needs_revision
    difficulty_level VARCHAR(20), -- beginner, intermediate, advanced (for courses)
    quality_rating INTEGER CHECK (quality_rating >= 1 AND quality_rating <= 5),
    utility_rating INTEGER CHECK (utility_rating >= 1 AND utility_rating <= 5),
    comments TEXT,
    points_awarded INTEGER DEFAULT 0, -- bonus points for creator if validation is positive
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

**Propósito**: Sistema de validación por expertos para bonificaciones
**Target Types**: Validación de cursos, skills, career paths
**Quality Control**: Ratings de calidad y utilidad
**Bonus System**: Puntos adicionales para creadores con validación positiva
**Expert Tracking**: Historial de validaciones por experto

#### Course Enrollments
```sql
CREATE TABLE course_enrollments (
    enrollment_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    course_id UUID NOT NULL REFERENCES courses(course_id),
    user_id UUID NOT NULL REFERENCES users(user_id),
    status VARCHAR(20) DEFAULT 'active', -- active, completed, dropped, suspended
    progress_percentage DECIMAL(5,2) DEFAULT 0.00,
    enrolled_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP,
    last_accessed_at TIMESTAMP,
    UNIQUE(course_id, user_id)
);
```

**Propósito**: Tracking de inscripciones y progreso en cursos
**Estados**: active → completed/dropped
**Progreso**: Porcentaje calculado basado en módulos completados

#### Module Progress
```sql
CREATE TABLE module_progress (
    progress_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    enrollment_id UUID NOT NULL REFERENCES course_enrollments(enrollment_id),
    module_id UUID NOT NULL REFERENCES course_modules(module_id),
    status VARCHAR(20) DEFAULT 'not_started', -- not_started, in_progress, completed
    progress_percentage DECIMAL(5,2) DEFAULT 0.00,
    started_at TIMESTAMP,
    completed_at TIMESTAMP,
    time_spent_minutes INTEGER DEFAULT 0,
    UNIQUE(enrollment_id, module_id)
);
```

**Propósito**: Progreso detallado por módulo
**Granularidad**: Tracking a nivel de módulo individual
**Métricas**: Tiempo invertido y porcentaje de completitud

#### TestItems (Banco de Preguntas)
```sql
CREATE TABLE test_items (
    test_item_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID REFERENCES tenants(tenant_id),
    item_type VARCHAR(20) NOT NULL, -- WRITING, LISTENING, SPEAKING, MCQ
    prompt TEXT NOT NULL,
    content_type VARCHAR(50),
    metadata JSONB, -- {script, audio_url, options, rubric}
    version INT DEFAULT 1,
    is_archived BOOLEAN DEFAULT FALSE,
    ai_prompt TEXT,
    llm_model VARCHAR(50),
    difficulty VARCHAR(10), -- A1, A2, B1, B2, C1, C2
    status VARCHAR(20) DEFAULT 'READY', -- READY, PENDING, FLAGGED
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT chk_item_type_valid 
    CHECK (item_type IN ('WRITING','LISTENING','SPEAKING','MCQ','CODE'))
);
```

**Propósito**: Banco centralizado de preguntas
**Flexibilidad**: JSONB metadata para diferentes tipos
**IA Integration**: Tracking de prompts y modelos usados

### 2.4 Evaluaciones y Resultados

#### ExamAttempts
```sql
CREATE TABLE exam_attempts (
    attempt_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    exam_id UUID REFERENCES exams(exam_id),
    user_id UUID REFERENCES users(user_id),
    status VARCHAR(20) DEFAULT 'IN_PROGRESS',
    started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    finished_at TIMESTAMP,
    time_spent_sec INT,
    total_score DECIMAL(5,2),
    cefr_level VARCHAR(10),
    metadata JSONB, -- {browser_info, ip_address, flags}
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

**Propósito**: Tracking de intentos de examen
**Proctoring**: Metadata para detección de anomalías
**Scoring**: Tanto automático como manual

#### ExamAnswers
```sql
CREATE TABLE exam_answers (
    answer_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    attempt_id UUID REFERENCES exam_attempts(attempt_id),
    test_item_id UUID REFERENCES test_items(test_item_id),
    answer_type VARCHAR(20) NOT NULL,
    answer_data JSONB NOT NULL, -- Estructura flexible por tipo
    ai_score DECIMAL(4,2),
    manual_score DECIMAL(4,2),
    rubric_json JSONB,
    plagiarism_pct DECIMAL(4,2),
    flagged BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

**Propósito**: Almacenamiento de respuestas y scoring
**Flexibilidad**: answer_data adaptable por tipo de pregunta
**Auditoría**: Scoring tanto automático como manual

---

## 3. Relaciones y Constraints

### 3.1 Relaciones N:M

#### User-Role Assignment
```sql
CREATE TABLE user_roles (
    user_id UUID REFERENCES users(user_id) ON DELETE CASCADE,
    role_id UUID REFERENCES roles(role_id) ON DELETE CASCADE,
    assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    assigned_by UUID REFERENCES users(user_id),
    expires_at TIMESTAMP,
    PRIMARY KEY (user_id, role_id)
);
```

#### Role-Permission Assignment
```sql
CREATE TABLE role_permissions (
    role_id UUID REFERENCES roles(role_id) ON DELETE CASCADE,
    permission_id UUID REFERENCES permissions(permission_id),
    granted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (role_id, permission_id)
);
```

### 3.2 Grupos y Membresías
```sql
CREATE TABLE groups (
    group_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID REFERENCES tenants(tenant_id),
    name VARCHAR(100) NOT NULL,
    description TEXT,
    metadata JSONB
);

CREATE TABLE group_users (
    group_id UUID REFERENCES groups(group_id),
    user_id UUID REFERENCES users(user_id),
    role VARCHAR(50) DEFAULT 'member', -- member, admin, viewer
    joined_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (group_id, user_id)
);
```

---

## 4. Optimizaciones y Performance

### 4.1 Índices Estratégicos

```sql
-- Multi-tenant optimization
CREATE INDEX idx_users_tenant_email ON users(tenant_id, email);
CREATE INDEX idx_courses_tenant_status ON courses(tenant_id, status);
CREATE INDEX idx_test_items_tenant_type ON test_items(tenant_id, item_type);

-- Performance critical queries
CREATE INDEX idx_exam_attempts_user_status ON exam_attempts(user_id, status);
CREATE INDEX idx_exam_answers_attempt_item ON exam_answers(attempt_id, test_item_id);

-- Full-text search
CREATE INDEX idx_test_items_prompt_gin ON test_items USING gin(to_tsvector('english', prompt));
```

### 4.2 Particionamiento

```sql
-- Preparación para particionamiento por tenant (futuro)
CREATE TABLE audit_logs (
    log_id UUID DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL,
    user_id UUID,
    entity VARCHAR(50),
    action VARCHAR(20),
    diff JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
) PARTITION BY HASH (tenant_id);
```

### 4.3 Row-Level Security

```sql
-- Ejemplo de RLS policy
ALTER TABLE users ENABLE ROW LEVEL SECURITY;

CREATE POLICY tenant_isolation ON users
    FOR ALL TO application_role
    USING (tenant_id = current_setting('app.current_tenant_id')::UUID);
```

---

## 5. Extensibilidad y Metadata

### 5.1 Campos JSONB Estratégicos

#### TestItems.metadata
```json
{
  "listening": {
    "script": "Conversation text...",
    "audio_url": "https://...",
    "speakers": ["A", "B"],
    "duration_sec": 45
  },
  "mcq": {
    "options": [
      {"id": "a", "text": "Option A", "is_correct": false},
      {"id": "b", "text": "Option B", "is_correct": true}
    ]
  },
  "rubric": {
    "grammar": {"weight": 0.3, "max_score": 5},
    "coherence": {"weight": 0.4, "max_score": 5},
    "pronunciation": {"weight": 0.3, "max_score": 5}
  }
}
```

#### ExamAnswers.answer_data
```json
{
  "writing": {
    "text": "Student response...",
    "word_count": 150,
    "time_spent_sec": 300
  },
  "speaking": {
    "audio_url": "https://...",
    "transcript": "Transcribed text...",
    "duration_sec": 60
  },
  "mcq": {
    "selected_option": "b",
    "time_spent_sec": 15
  }
}
```

### 5.2 Versionado de Schema

```sql
CREATE TABLE schema_migrations (
    version VARCHAR(50) PRIMARY KEY,
    applied_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    description TEXT
);
```

---

## 6. Seguridad de Datos

### 6.1 Encriptación
- **At Rest**: PostgreSQL TDE para datos sensibles
- **In Transit**: TLS 1.3 para todas las conexiones
- **Application Level**: Campos sensibles con encriptación adicional

### 6.2 Backup y Recovery
```sql
-- Backup strategy
CREATE TABLE backups (
    backup_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID REFERENCES tenants(tenant_id),
    backup_type VARCHAR(20), -- full, incremental, differential
    backup_location TEXT,
    size_bytes BIGINT,
    checksum VARCHAR(64),
    status VARCHAR(20) DEFAULT 'SUCCESS',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 6.3 Auditoría Completa
```sql
CREATE TABLE audit_logs (
    log_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID REFERENCES tenants(tenant_id),
    user_id UUID REFERENCES users(user_id),
    entity VARCHAR(50),
    entity_id UUID,
    action VARCHAR(20), -- CREATE, UPDATE, DELETE, VIEW
    old_values JSONB,
    new_values JSONB,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

---

## 7. Consideraciones de Compliance

### 7.1 GDPR/CCPA
- **Right to be Forgotten**: Soft deletes con `deleted_at`
- **Data Portability**: Exportación completa de datos de usuario
- **Consent Management**: Tracking de consentimientos

### 7.2 FERPA (Educational Records)
- **Access Logging**: Auditoría de acceso a registros educativos
- **Retention Policies**: Políticas automáticas de retención
- **Parent Consent**: Para usuarios menores de edad

---

## Conclusión

El modelo de datos de Arroyo University está diseñado para balancear flexibilidad, performance y compliance. La combinación de estructura relacional sólida con flexibilidad JSONB permite evolución sin breaking changes, mientras que las optimizaciones multi-tenant aseguran escalabilidad horizontal.
