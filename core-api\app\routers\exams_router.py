"""
Exams router for exam management
"""

from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlmodel import Session
from typing import Optional, List
from uuid import UUID

from ..core.database import get_session
from ..core.security import get_current_user, Permissions, require_permissions
from ..services.exam_service import ExamService
from ..models.exam import (
    ExamCreate, ExamUpdate, ExamResponse, ExamStatus,
    ExamSubmissionCreate, SubmissionAnswerCreate, ExamResult
)
from ..models.base import SuccessResponse

router = APIRouter()


def get_exam_service(db: Session = Depends(get_session)) -> ExamService:
    """Get exam service"""
    return ExamService(db)


@router.get("/", response_model=List[ExamResponse])
async def list_exams(
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    course_id: Optional[UUID] = Query(None),
    status: Optional[ExamStatus] = Query(None),
    current_user = Depends(get_current_user),
    exam_service: ExamService = Depends(get_exam_service)
):
    """List exams"""
    # This would be implemented in the exam service
    return []


@router.get("/{exam_id}", response_model=ExamResponse)
async def get_exam(
    exam_id: UUID,
    current_user = Depends(get_current_user),
    exam_service: ExamService = Depends(get_exam_service)
):
    """Get exam by ID"""
    try:
        exam = await exam_service.get_exam(exam_id, current_user.tenant_id)
        
        if not exam:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Exam not found"
            )
        
        return exam
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve exam"
        )


@router.post("/", response_model=ExamResponse)
@require_permissions([Permissions.EXAM_CREATE])
async def create_exam(
    exam_data: ExamCreate,
    current_user = Depends(get_current_user),
    exam_service: ExamService = Depends(get_exam_service)
):
    """Create a new exam"""
    try:
        exam = await exam_service.create_exam(
            exam_data=exam_data,
            created_by=current_user.user_id,
            tenant_id=current_user.tenant_id
        )
        
        return await exam_service.get_exam(exam.exam_id, current_user.tenant_id)
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.put("/{exam_id}", response_model=ExamResponse)
@require_permissions([Permissions.EXAM_UPDATE])
async def update_exam(
    exam_id: UUID,
    exam_data: ExamUpdate,
    current_user = Depends(get_current_user),
    exam_service: ExamService = Depends(get_exam_service)
):
    """Update exam"""
    try:
        exam = await exam_service.update_exam(
            exam_id=exam_id,
            exam_data=exam_data,
            tenant_id=current_user.tenant_id
        )
        
        if not exam:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Exam not found"
            )
        
        return await exam_service.get_exam(exam.exam_id, current_user.tenant_id)
        
    except HTTPException:
        raise
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.post("/{exam_id}/publish")
@require_permissions([Permissions.EXAM_UPDATE])
async def publish_exam(
    exam_id: UUID,
    current_user = Depends(get_current_user),
    exam_service: ExamService = Depends(get_exam_service)
):
    """Publish exam"""
    try:
        success = await exam_service.publish_exam(
            exam_id=exam_id,
            tenant_id=current_user.tenant_id
        )
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Exam not found"
            )
        
        return SuccessResponse(message="Exam published successfully")
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to publish exam"
        )


@router.post("/{exam_id}/start")
async def start_exam(
    exam_id: UUID,
    current_user = Depends(get_current_user),
    exam_service: ExamService = Depends(get_exam_service)
):
    """Start exam attempt"""
    try:
        submission = await exam_service.start_exam(
            exam_id=exam_id,
            user_id=current_user.user_id,
            tenant_id=current_user.tenant_id
        )
        
        if not submission:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Exam not found"
            )
        
        return {
            "success": True,
            "message": "Exam started successfully",
            "data": {
                "submission_id": submission.submission_id,
                "started_at": submission.started_at
            }
        }
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.get("/{exam_id}/questions")
async def get_exam_questions(
    exam_id: UUID,
    current_user = Depends(get_current_user),
    exam_service: ExamService = Depends(get_exam_service)
):
    """Get exam questions for user"""
    try:
        questions = await exam_service.get_exam_questions(
            exam_id=exam_id,
            user_id=current_user.user_id,
            shuffle=True
        )
        
        return {
            "success": True,
            "message": "Questions retrieved successfully",
            "data": questions
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve questions"
        )


@router.post("/submissions/{submission_id}/answers")
async def submit_answer(
    submission_id: UUID,
    answer_data: SubmissionAnswerCreate,
    current_user = Depends(get_current_user),
    exam_service: ExamService = Depends(get_exam_service)
):
    """Submit answer for a question"""
    try:
        answer = await exam_service.submit_answer(
            submission_id=submission_id,
            answer_data=answer_data,
            tenant_id=current_user.tenant_id
        )
        
        return {
            "success": True,
            "message": "Answer submitted successfully",
            "data": answer
        }
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.post("/submissions/{submission_id}/submit")
async def submit_exam(
    submission_id: UUID,
    current_user = Depends(get_current_user),
    exam_service: ExamService = Depends(get_exam_service)
):
    """Submit exam for grading"""
    try:
        result = await exam_service.submit_exam(
            submission_id=submission_id,
            user_id=current_user.user_id
        )
        
        return {
            "success": True,
            "message": "Exam submitted successfully",
            "data": result
        }
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.get("/submissions/{submission_id}/result")
async def get_exam_result(
    submission_id: UUID,
    current_user = Depends(get_current_user),
    exam_service: ExamService = Depends(get_exam_service)
):
    """Get exam result"""
    try:
        result = await exam_service.get_exam_result(
            submission_id=submission_id,
            user_id=current_user.user_id
        )
        
        return {
            "success": True,
            "message": "Result retrieved successfully",
            "data": result
        }
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
