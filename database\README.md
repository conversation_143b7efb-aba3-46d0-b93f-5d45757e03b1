# Database Structure - Arroyo University

## Overview

This directory contains the complete database initialization scripts for the Arroyo University learning management system. The database is designed with PostgreSQL and includes multi-tenant architecture, comprehensive RBAC, career paths, gamification, and all core LMS functionality.

## File Structure

### Initialization Scripts (Execute in Order)

1. **`01-init.sql`** - Core Foundation
   - PostgreSQL extensions (uuid-ossp, citext, pgcrypto, pg_trgm)
   - Custom types and enums
   - Core tables: tenants, users, roles, groups, courses
   - Basic RBAC and multi-tenant setup
   - Row Level Security (RLS) policies
   - Expert system foundation

2. **`02-content-structure.sql`** - Content Management
   - Course modules and content items
   - Multimedia file management
   - Course enrollments and user progress
   - Forum system (categories, posts, replies, likes)
   - Notification system
   - Content delivery infrastructure

3. **`03-assessments.sql`** - Assessment System
   - Question banks and questions
   - Exams and submissions
   - AI question generation
   - Analytics and performance tracking
   - Auto-grading capabilities
   - Question and exam analytics

4. **`04-career-paths.sql`** - Career Path System
   - Skills repository with prerequisites
   - Career path definitions and connections
   - User progress tracking
   - Skill-course mappings
   - Recommendation engine
   - Visual diagram positioning

5. **`05-gamification.sql`** - Gamification System
   - User scoring (student/creator points)
   - Leaderboard cache and rankings
   - Expert validation system
   - Achievement system
   - Point multipliers and bonuses
   - Scoring configuration

6. **`06-sample-data.sql`** - Development Data
   - Sample tenant and users
   - Example courses and content
   - Career paths with skills
   - Forum posts and interactions
   - Scoring transactions
   - Complete test dataset

7. **`07-additional-tables.sql`** - Supporting Features
   - Course tagging system
   - User preferences and sessions
   - Audit logging
   - Email templates and queue
   - Webhook system
   - API key management
   - Rate limiting

## Key Features

### 🏢 Multi-Tenant Architecture
- Complete tenant isolation using Row Level Security (RLS)
- Tenant-specific configurations and settings
- Scalable architecture for multiple organizations

### 👥 Advanced RBAC System
- Flexible role-based access control
- Custom permissions per tenant
- Group management with leaders
- Expert role assignments by area

### 📚 Comprehensive LMS
- Modular course structure
- Rich content types (text, video, audio, interactive)
- Progress tracking and analytics
- Forum system with expert highlighting
- Multimedia file management

### 🎯 Assessment Engine
- Multiple question types (multiple choice, essay, code)
- AI-powered question generation
- Auto-grading capabilities
- Detailed analytics and performance tracking
- Flexible exam configurations

### 🗺️ Career Path System
- Visual skill mapping with UML-like diagrams
- Prerequisite tracking and validation
- Progress monitoring across career paths
- Skill-course automatic mapping
- Personalized recommendations

### 🏆 Gamification System
- Dual scoring (student/creator points)
- Competitive leaderboards (weekly/monthly/all-time)
- Expert validation bonuses
- Achievement system
- Career path bonus multipliers

### 🔧 Enterprise Features
- Comprehensive audit logging
- Email template system
- Webhook integrations
- API key management
- Rate limiting
- File upload management

## Database Schema Highlights

### Core Entities
```sql
-- Multi-tenant foundation
tenants → users → roles → permissions
         ↓
      courses → modules → content_items
         ↓
      enrollments → progress → completions
```

### Career Path Flow
```sql
skills → career_paths → career_path_skills → connections
   ↓           ↓              ↓
user_skill_progress ← user_career_paths
```

### Gamification Flow
```sql
actions → score_transactions → user_scores → leaderboard_cache
   ↓              ↓               ↓
expert_validations → bonuses → achievements
```

## Performance Optimizations

### Indexing Strategy
- Comprehensive indexing on all foreign keys
- Performance indexes on frequently queried columns
- Composite indexes for complex queries
- GIN indexes for JSONB and array columns

### Caching
- Leaderboard cache for fast ranking queries
- Pre-calculated statistics and aggregations
- Materialized views for complex analytics

### Scalability Features
- Partitioning ready for large datasets
- Efficient pagination support
- Optimized queries with proper joins
- Connection pooling ready

## Security Features

### Row Level Security (RLS)
- Tenant isolation at database level
- User-specific data access controls
- Automatic policy enforcement

### Data Protection
- Password hashing with pgcrypto
- Secure session management
- API key encryption
- Audit trail for all changes

### Access Control
- Granular permission system
- Role-based access control
- Expert validation system
- Rate limiting protection

## Development Setup

### Prerequisites
- PostgreSQL 14+ with extensions
- UUID generation capability
- JSONB support
- Full-text search (pg_trgm)

### Initialization
```bash
# Execute scripts in order
psql -d your_database -f 01-init.sql
psql -d your_database -f 02-content-structure.sql
psql -d your_database -f 03-assessments.sql
psql -d your_database -f 04-career-paths.sql
psql -d your_database -f 05-gamification.sql
psql -d your_database -f 06-sample-data.sql
psql -d your_database -f 07-additional-tables.sql
```

### Environment Variables
```bash
# Set tenant context for RLS
SET app.current_tenant_id = '550e8400-e29b-41d4-a716-************';
```

## Maintenance

### Regular Tasks
- Leaderboard cache regeneration (automated)
- Analytics calculation (scheduled)
- Audit log cleanup (retention policy)
- Session cleanup (expired sessions)

### Monitoring
- Query performance monitoring
- Index usage analysis
- Storage growth tracking
- Connection pool monitoring

## Migration Strategy

### Version Control
- Each script is versioned and idempotent
- Migration scripts for schema changes
- Rollback procedures documented
- Data migration utilities

### Deployment
- Blue-green deployment ready
- Zero-downtime migration support
- Backup and restore procedures
- Environment-specific configurations

## API Integration

### Prepared Statements
- Optimized queries for common operations
- Parameterized queries for security
- Connection pooling optimization
- Transaction management

### Real-time Features
- WebSocket support for live updates
- Event triggers for notifications
- Change data capture ready
- Pub/sub integration points

This database structure provides a solid foundation for a comprehensive learning management system with advanced features like career paths, gamification, and expert validation systems.
