# 📊 Arroyo University Monitoring Stack Guide

## 🎯 **MONITORING STACK OVERVIEW**

The Arroyo University platform uses a comprehensive monitoring stack with three core components that work together to provide complete observability.

## 🔍 **THE 3 MONITORING COMPONENTS EXPLAINED**

### **1. 📈 Prometheus - Metrics Collection Engine**

**Purpose:** Time-series database that collects and stores numerical metrics

**What it does:**
- **Scrapes metrics** from all services every 15 seconds
- **Stores time-series data** with timestamps and labels
- **Provides PromQL** (Prometheus Query Language) for data analysis
- **Triggers alerts** based on metric thresholds
- **Monitors service health** and performance indicators

**For Arroyo University:**
- API request rates and response times
- Database connection pools and query performance
- AI service usage, costs, and processing times
- User activity and exam completion rates
- System resource utilization (CPU, memory, disk)
- Business metrics (enrollments, course completions)

**Access:** http://localhost:9090

### **2. 📊 Grafana - Visualization & Dashboards**

**Purpose:** Creates beautiful dashboards and visualizations from metrics data

**What it does:**
- **Visualizes metrics** in charts, graphs, and tables
- **Creates dashboards** for different audiences (developers, ops, business)
- **Sets up alerts** with notifications (email, Slack, etc.)
- **Provides drill-down capabilities** for troubleshooting
- **Supports multiple data sources** (Prometheus, PostgreSQL, Loki)

**For Arroyo University:**
- Real-time platform health dashboards
- Business metrics (user engagement, course completion)
- AI usage analytics and cost tracking
- Performance monitoring and SLA tracking
- Tenant-specific analytics and reporting

**Access:** http://localhost:3001 (admin/admin123)

### **3. 📝 Loki - Log Aggregation System**

**Purpose:** Centralized logging system that collects and indexes log data

**What it does:**
- **Aggregates logs** from all services in one place
- **Indexes logs efficiently** using labels (not full-text)
- **Provides log search** and filtering capabilities
- **Correlates logs with metrics** for better debugging
- **Stores structured and unstructured logs**

**For Arroyo University:**
- Application logs from all services
- Error tracking and debugging information
- Audit logs for compliance and security
- User activity logs and behavior tracking
- Security event monitoring and alerting

**Access:** http://localhost:3100

## 🔄 **HOW THEY WORK TOGETHER**

```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│   Services  │    │ Prometheus  │    │   Grafana   │
│             │───▶│  (Metrics)  │───▶│(Dashboards) │
│ Core API    │    │             │    │             │
│ Notification│    └─────────────┘    └─────────────┘
│ Frontend    │           │                   ▲
└─────────────┘           │                   │
       │                  ▼                   │
       │            ┌─────────────┐           │
       └───────────▶│    Loki     │───────────┘
                    │   (Logs)    │
                    └─────────────┘
```

- **Prometheus** collects "what happened" (numbers and metrics)
- **Loki** collects "why it happened" (logs and events)
- **Grafana** shows "how to understand it" (visualizations and alerts)

## 📊 **GRAFANA DASHBOARDS INCLUDED**

### **🏗️ Infrastructure Dashboards**

#### **System Overview**
- **URL:** `/d/arroyo-system-overview`
- **Purpose:** High-level infrastructure health
- **Metrics:**
  - CPU and Memory usage
  - Service health status (Core API, Notification, DB, Redis)
  - API request rates across services
  - System resource utilization

#### **API Performance**
- **URL:** `/d/arroyo-api-performance`
- **Purpose:** Detailed API performance monitoring
- **Metrics:**
  - Response time percentiles (50th, 95th, 99th)
  - Request rate by status code (2xx, 4xx, 5xx)
  - Database connection pool status
  - Database query performance

### **🤖 AI Services Dashboards**

#### **AI Overview**
- **URL:** `/d/arroyo-ai-overview`
- **Purpose:** Comprehensive AI services monitoring
- **Metrics:**
  - Daily quota usage percentage
  - AI costs in real-time
  - Task failure counts
  - Average processing times
  - Request rates by AI service type

### **💼 Business Metrics Dashboards**

#### **Platform Business Metrics**
- **URL:** `/d/arroyo-business-metrics`
- **Purpose:** Key business performance indicators
- **Metrics:**
  - Active users count
  - Published courses count
  - Daily exam submissions
  - Course completion rates
  - User activity trends

### **🔒 Security Dashboards**

#### **Security Overview**
- **URL:** `/d/arroyo-security-overview`
- **Purpose:** Security monitoring and compliance
- **Metrics:**
  - Failed login attempts
  - Rate limit violations
  - Content moderation violations
  - Suspicious activities
  - Error log frequency
  - Security events tracking

## 🚀 **GETTING STARTED**

### **1. Start the Monitoring Stack**
```bash
# Start all services including monitoring
docker-compose up -d

# Check monitoring services status
docker-compose ps prometheus grafana loki
```

### **2. Access Dashboards**
- **Grafana:** http://localhost:3001
  - Username: `admin`
  - Password: `admin123`
- **Prometheus:** http://localhost:9090
- **Loki:** http://localhost:3100

### **3. Navigate Dashboards**
1. **Login to Grafana**
2. **Browse by Folder:**
   - Infrastructure → System health and performance
   - Application → API and service performance
   - Business → Platform metrics and KPIs
   - AI Services → AI usage and costs
   - Security → Security and compliance

## 📈 **KEY METRICS TO MONITOR**

### **🚨 Critical Alerts**
- **Service Down:** Any service health check fails
- **High Error Rate:** >5% 5xx errors in 5 minutes
- **Slow Response:** >2s 95th percentile response time
- **AI Quota Exceeded:** >90% daily quota usage
- **Security Breach:** >10 failed logins from same IP

### **📊 Performance Metrics**
- **Response Time:** Target <500ms 95th percentile
- **Throughput:** Monitor request rate trends
- **Error Rate:** Keep <1% overall error rate
- **Database Performance:** Query time <100ms average
- **AI Processing:** <10s average processing time

### **💰 Cost Metrics**
- **AI Daily Cost:** Monitor against $100 daily limit
- **AI Monthly Cost:** Track against $1000 monthly limit
- **Resource Utilization:** Optimize for cost efficiency
- **User Growth:** Track cost per active user

## 🔧 **CUSTOMIZATION**

### **Adding Custom Metrics**
1. **Instrument your code** with Prometheus metrics
2. **Add metric endpoints** to your services
3. **Update Prometheus config** to scrape new endpoints
4. **Create Grafana panels** for visualization

### **Creating Custom Dashboards**
1. **Access Grafana** at http://localhost:3001
2. **Create new dashboard** or copy existing
3. **Add panels** with PromQL queries
4. **Configure alerts** for important metrics
5. **Export JSON** and save to repository

### **Setting Up Alerts**
1. **Configure notification channels** (email, Slack)
2. **Define alert rules** in Grafana
3. **Set appropriate thresholds** for your SLAs
4. **Test alert delivery** before production

## 🎯 **MONITORING BEST PRACTICES**

### **📊 Dashboard Design**
- **Use consistent time ranges** across related panels
- **Group related metrics** in logical sections
- **Include context** with annotations and descriptions
- **Set appropriate refresh rates** (5s for real-time, 1m for trends)

### **🚨 Alerting Strategy**
- **Alert on symptoms, not causes** (user impact first)
- **Use multiple severity levels** (warning, critical)
- **Avoid alert fatigue** with proper thresholds
- **Include runbooks** in alert descriptions

### **📈 Performance Monitoring**
- **Monitor the 4 Golden Signals:**
  - Latency (response time)
  - Traffic (request rate)
  - Errors (error rate)
  - Saturation (resource utilization)

## 🔍 **TROUBLESHOOTING**

### **Common Issues**
- **No data in Grafana:** Check Prometheus targets
- **Missing metrics:** Verify service instrumentation
- **Slow dashboards:** Optimize PromQL queries
- **Alert not firing:** Check alert rule conditions

### **Useful Commands**
```bash
# Check Prometheus targets
curl http://localhost:9090/api/v1/targets

# View Grafana logs
docker logs arroyo-grafana

# Check Loki status
curl http://localhost:3100/ready

# Restart monitoring stack
docker-compose restart prometheus grafana loki
```

## 🎉 **MONITORING STACK READY**

Your Arroyo University monitoring stack is now fully configured with:

✅ **Complete observability** across all services  
✅ **Business metrics** for platform insights  
✅ **AI monitoring** for cost and performance tracking  
✅ **Security monitoring** for compliance and safety  
✅ **Performance dashboards** for SLA monitoring  
✅ **Custom alerting** for proactive issue detection  

**The monitoring stack provides comprehensive visibility into your platform's health, performance, and business metrics!** 📊
