import React, { useState } from 'react';
import { use<PERSON>ara<PERSON>, useNavigate } from 'react-router-dom';
import { 
  ArrowLeft, 
  Users, 
  Crown, 
  UserPlus, 
  MessageSquare, 
  Calendar,
  Settings,
  MoreVertical,
  Mail,
  Globe,
  Lock
} from 'lucide-react';
import { But<PERSON> } from '@/components/ui/Button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Badge } from '@/components/ui/Badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/Avatar';
import { Modal, ModalHeader, ModalFooter } from '@/components/ui/Modal';
import { Input } from '@/components/ui/Input';
import { Dropdown, DropdownItem, DropdownSeparator } from '@/components/ui/Dropdown';
import { generateInitials } from '@/lib/utils';
import toast from 'react-hot-toast';

interface GroupMember {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  role: 'leader' | 'member';
  joinedAt: string;
  lastActive: string;
  coursesCompleted: number;
}

interface GroupActivity {
  id: string;
  type: 'member_joined' | 'member_left' | 'discussion_created' | 'course_completed';
  user: {
    name: string;
    avatar?: string;
  };
  description: string;
  timestamp: string;
}

const mockGroup = {
  id: 'group-1',
  name: 'Grupo de Práctica IA',
  description: 'Comunidad para practicar y discutir conceptos de Inteligencia Artificial. Compartimos recursos, resolvemos dudas y trabajamos en proyectos colaborativos.',
  category: 'Tecnología',
  type: 'public' as const,
  memberCount: 45,
  leader: {
    id: '1',
    name: 'Dra. Ana Martínez',
    email: '<EMAIL>',
    avatar: '',
  },
  createdAt: '2023-10-15',
  isJoined: true,
  isLeader: false,
};

const mockMembers: GroupMember[] = [
  {
    id: '1',
    name: 'Dra. Ana Martínez',
    email: '<EMAIL>',
    role: 'leader',
    joinedAt: '2023-10-15',
    lastActive: '2024-01-15',
    coursesCompleted: 12,
  },
  {
    id: '2',
    name: 'Carlos López',
    email: '<EMAIL>',
    role: 'member',
    joinedAt: '2023-10-20',
    lastActive: '2024-01-14',
    coursesCompleted: 8,
  },
  {
    id: '3',
    name: 'María García',
    email: '<EMAIL>',
    role: 'member',
    joinedAt: '2023-11-02',
    lastActive: '2024-01-13',
    coursesCompleted: 15,
  },
  {
    id: '4',
    name: 'Pedro Silva',
    email: '<EMAIL>',
    role: 'member',
    joinedAt: '2023-11-15',
    lastActive: '2024-01-12',
    coursesCompleted: 6,
  },
];

const mockActivities: GroupActivity[] = [
  {
    id: '1',
    type: 'member_joined',
    user: { name: 'Luis Rodríguez' },
    description: 'se unió al grupo',
    timestamp: '2024-01-15T10:30:00Z',
  },
  {
    id: '2',
    type: 'course_completed',
    user: { name: 'María García' },
    description: 'completó el curso "Machine Learning Básico"',
    timestamp: '2024-01-14T16:45:00Z',
  },
  {
    id: '3',
    type: 'discussion_created',
    user: { name: 'Carlos López' },
    description: 'creó una nueva discusión: "Redes Neuronales vs Árboles de Decisión"',
    timestamp: '2024-01-14T09:20:00Z',
  },
];

export default function GroupDetailPage() {
  const { groupId } = useParams();
  const navigate = useNavigate();
  
  const [group, setGroup] = useState(mockGroup);
  const [members, setMembers] = useState<GroupMember[]>(mockMembers);
  const [activities] = useState<GroupActivity[]>(mockActivities);
  const [isInviteModalOpen, setIsInviteModalOpen] = useState(false);
  const [inviteEmails, setInviteEmails] = useState('');
  const [activeTab, setActiveTab] = useState<'members' | 'activity' | 'discussions'>('members');

  const handleJoinGroup = () => {
    setGroup({ ...group, isJoined: true, memberCount: group.memberCount + 1 });
    toast.success('¡Te has unido al grupo exitosamente!');
  };

  const handleLeaveGroup = () => {
    setGroup({ ...group, isJoined: false, memberCount: group.memberCount - 1 });
    toast.success('Has salido del grupo');
  };

  const handleInviteMembers = () => {
    if (!inviteEmails.trim()) {
      toast.error('Por favor, ingresa al menos un email');
      return;
    }

    const emails = inviteEmails
      .split('\n')
      .map(email => email.trim())
      .filter(email => email && email.includes('@'));

    if (emails.length === 0) {
      toast.error('No se encontraron emails válidos');
      return;
    }

    toast.success(`Invitaciones enviadas a ${emails.length} persona${emails.length > 1 ? 's' : ''}`);
    setIsInviteModalOpen(false);
    setInviteEmails('');
  };

  const handleRemoveMember = (memberId: string) => {
    const member = members.find(m => m.id === memberId);
    if (member?.role === 'leader') {
      toast.error('No se puede remover al líder del grupo');
      return;
    }

    setMembers(members.filter(m => m.id !== memberId));
    setGroup({ ...group, memberCount: group.memberCount - 1 });
    toast.success('Miembro removido del grupo');
  };

  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) return 'Hace menos de 1 hora';
    if (diffInHours < 24) return `Hace ${diffInHours} hora${diffInHours > 1 ? 's' : ''}`;
    
    const diffInDays = Math.floor(diffInHours / 24);
    return `Hace ${diffInDays} día${diffInDays > 1 ? 's' : ''}`;
  };

  const getActivityIcon = (type: GroupActivity['type']) => {
    switch (type) {
      case 'member_joined':
        return <UserPlus className="w-4 h-4 text-green-600" />;
      case 'member_left':
        return <Users className="w-4 h-4 text-red-600" />;
      case 'discussion_created':
        return <MessageSquare className="w-4 h-4 text-blue-600" />;
      case 'course_completed':
        return <Calendar className="w-4 h-4 text-purple-600" />;
      default:
        return <Users className="w-4 h-4 text-gray-600" />;
    }
  };

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center space-x-4 mb-6">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => navigate(-1)}
          >
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div className="flex-1">
            <div className="flex items-center space-x-3 mb-2">
              <h1 className="text-2xl font-bold text-gray-900">{group.name}</h1>
              <Badge variant={group.type === 'public' ? 'green' : 'purple'}>
                {group.type === 'public' ? (
                  <>
                    <Globe className="w-3 h-3 mr-1" />
                    Público
                  </>
                ) : (
                  <>
                    <Lock className="w-3 h-3 mr-1" />
                    Privado
                  </>
                )}
              </Badge>
              <Badge variant="outline">{group.category}</Badge>
            </div>
            <p className="text-gray-600">{group.description}</p>
          </div>
        </div>

        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-6 text-sm text-gray-600">
            <div className="flex items-center">
              <Users className="w-4 h-4 mr-1" />
              {group.memberCount} miembros
            </div>
            <div className="flex items-center">
              <Calendar className="w-4 h-4 mr-1" />
              Creado el {new Date(group.createdAt).toLocaleDateString()}
            </div>
            <div className="flex items-center space-x-2">
              <Avatar className="w-6 h-6">
                <AvatarImage src={group.leader.avatar} />
                <AvatarFallback className="bg-blue-500 text-white text-xs">
                  {generateInitials(group.leader.name)}
                </AvatarFallback>
              </Avatar>
              <span>Líder: {group.leader.name}</span>
            </div>
          </div>

          <div className="flex items-center space-x-3">
            {group.isJoined ? (
              <>
                {group.isLeader && (
                  <Button
                    variant="outline"
                    onClick={() => setIsInviteModalOpen(true)}
                  >
                    <UserPlus className="w-4 h-4 mr-2" />
                    Invitar Miembros
                  </Button>
                )}
                <Button
                  variant="outline"
                  onClick={handleLeaveGroup}
                  className="text-red-600 border-red-300 hover:bg-red-50"
                >
                  Salir del Grupo
                </Button>
                {group.isLeader && (
                  <Button variant="outline">
                    <Settings className="w-4 h-4 mr-2" />
                    Configurar
                  </Button>
                )}
              </>
            ) : (
              <Button onClick={handleJoinGroup}>
                <UserPlus className="w-4 h-4 mr-2" />
                Unirse al Grupo
              </Button>
            )}
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200 mb-8">
        <nav className="-mb-px flex space-x-8">
          {[
            { id: 'members', label: 'Miembros', count: members.length },
            { id: 'activity', label: 'Actividad', count: activities.length },
            { id: 'discussions', label: 'Discusiones', count: 12 },
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === tab.id
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              {tab.label} ({tab.count})
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      {activeTab === 'members' && (
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Members List */}
          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <CardTitle>Miembros del Grupo</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {members.map((member) => (
                    <div
                      key={member.id}
                      className="flex items-center justify-between p-4 border border-gray-200 rounded-lg"
                    >
                      <div className="flex items-center space-x-4">
                        <Avatar className="w-12 h-12">
                          <AvatarImage src={member.avatar} />
                          <AvatarFallback className="bg-blue-500 text-white">
                            {generateInitials(member.name)}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <div className="flex items-center space-x-2">
                            <h3 className="font-medium text-gray-900">{member.name}</h3>
                            {member.role === 'leader' && (
                              <Crown className="w-4 h-4 text-yellow-500" />
                            )}
                          </div>
                          <p className="text-sm text-gray-600">{member.email}</p>
                          <div className="flex items-center space-x-4 text-xs text-gray-500 mt-1">
                            <span>Se unió: {new Date(member.joinedAt).toLocaleDateString()}</span>
                            <span>Última actividad: {formatTimeAgo(member.lastActive)}</span>
                            <span>{member.coursesCompleted} cursos completados</span>
                          </div>
                        </div>
                      </div>

                      {group.isLeader && member.role !== 'leader' && (
                        <Dropdown
                          trigger={
                            <Button variant="ghost" size="sm">
                              <MoreVertical className="w-4 h-4" />
                            </Button>
                          }
                          align="right"
                        >
                          <DropdownItem onClick={() => toast.info('Enviando mensaje...')}>
                            <Mail className="w-4 h-4" />
                            Enviar Mensaje
                          </DropdownItem>
                          <DropdownSeparator />
                          <DropdownItem 
                            onClick={() => handleRemoveMember(member.id)}
                            className="text-red-600"
                          >
                            <Users className="w-4 h-4" />
                            Remover del Grupo
                          </DropdownItem>
                        </Dropdown>
                      )}
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Group Stats */}
          <div className="lg:col-span-1">
            <Card>
              <CardHeader>
                <CardTitle>Estadísticas del Grupo</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="text-center p-4 bg-blue-50 rounded-lg">
                  <div className="text-2xl font-bold text-blue-600">{group.memberCount}</div>
                  <div className="text-sm text-blue-800">Miembros Totales</div>
                </div>
                
                <div className="text-center p-4 bg-green-50 rounded-lg">
                  <div className="text-2xl font-bold text-green-600">
                    {Math.round(members.reduce((sum, m) => sum + m.coursesCompleted, 0) / members.length)}
                  </div>
                  <div className="text-sm text-green-800">Cursos Promedio</div>
                </div>
                
                <div className="text-center p-4 bg-purple-50 rounded-lg">
                  <div className="text-2xl font-bold text-purple-600">
                    {members.filter(m => {
                      const lastActive = new Date(m.lastActive);
                      const weekAgo = new Date();
                      weekAgo.setDate(weekAgo.getDate() - 7);
                      return lastActive > weekAgo;
                    }).length}
                  </div>
                  <div className="text-sm text-purple-800">Activos esta semana</div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      )}

      {activeTab === 'activity' && (
        <Card>
          <CardHeader>
            <CardTitle>Actividad Reciente</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {activities.map((activity) => (
                <div key={activity.id} className="flex items-start space-x-4 p-4 border border-gray-200 rounded-lg">
                  <div className="flex-shrink-0 p-2 rounded-full bg-gray-100">
                    {getActivityIcon(activity.type)}
                  </div>
                  <div className="flex-1">
                    <p className="text-sm text-gray-900">
                      <span className="font-medium">{activity.user.name}</span>{' '}
                      {activity.description}
                    </p>
                    <p className="text-xs text-gray-500 mt-1">
                      {formatTimeAgo(activity.timestamp)}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {activeTab === 'discussions' && (
        <Card>
          <CardHeader>
            <CardTitle>Discusiones del Grupo</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-center py-12">
              <MessageSquare className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Próximamente
              </h3>
              <p className="text-gray-600">
                Las discusiones del grupo estarán disponibles pronto
              </p>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Invite Members Modal */}
      <Modal
        isOpen={isInviteModalOpen}
        onClose={() => setIsInviteModalOpen(false)}
        title="Invitar Miembros"
        size="md"
      >
        <div className="space-y-4">
          <p className="text-sm text-gray-600">
            Invita nuevos miembros al grupo enviando invitaciones por email.
          </p>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Emails (uno por línea)
            </label>
            <textarea
              rows={6}
              placeholder="<EMAIL>&#10;<EMAIL>&#10;<EMAIL>"
              value={inviteEmails}
              onChange={(e) => setInviteEmails(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
            <p className="text-sm text-blue-800">
              Los invitados recibirán un email con un enlace para unirse al grupo.
            </p>
          </div>
        </div>

        <ModalFooter>
          <Button
            variant="outline"
            onClick={() => setIsInviteModalOpen(false)}
          >
            Cancelar
          </Button>
          <Button onClick={handleInviteMembers}>
            <Mail className="w-4 h-4 mr-2" />
            Enviar Invitaciones
          </Button>
        </ModalFooter>
      </Modal>
    </div>
  );
}
