"""
User models for authentication and user management
"""

from datetime import datetime
from typing import Optional, List
from uuid import UUID

from sqlmodel import SQLModel, Field, Relationship

from .base import TimestampMixin, TenantMixin, MetadataMixin, SoftDeleteMixin


class UserBase(SQLModel):
    """Base user model"""
    email: str = Field(max_length=255, unique=True, index=True)
    first_name: str = Field(max_length=100)
    last_name: str = Field(max_length=100)
    is_active: bool = Field(default=True)
    is_verified: bool = Field(default=False)
    avatar_url: Optional[str] = Field(default=None, max_length=500)
    bio: Optional[str] = Field(default=None, max_length=1000)
    timezone: str = Field(default="UTC", max_length=50)
    language: str = Field(default="en", max_length=10)


class User(UserBase, TenantMixin, TimestampMixin, MetadataMixin, SoftDeleteMixin, table=True):
    """User table model"""
    __tablename__ = "users"
    
    user_id: UUID = Field(primary_key=True)
    password_hash: str = Field(max_length=255)
    email_verified_at: Optional[datetime] = None
    last_login_at: Optional[datetime] = None
    login_count: int = Field(default=0)
    failed_login_attempts: int = Field(default=0)
    locked_until: Optional[datetime] = None
    preferences: Optional[dict] = Field(default_factory=dict, sa_column_kwargs={"type_": "JSONB"})


class UserCreate(UserBase):
    """User creation model"""
    password: str = Field(min_length=8)
    role_ids: Optional[List[UUID]] = Field(default_factory=list)
    send_welcome_email: bool = Field(default=True)


class UserUpdate(SQLModel):
    """User update model"""
    email: Optional[str] = Field(default=None, max_length=255)
    first_name: Optional[str] = Field(default=None, max_length=100)
    last_name: Optional[str] = Field(default=None, max_length=100)
    is_active: Optional[bool] = None
    avatar_url: Optional[str] = Field(default=None, max_length=500)
    bio: Optional[str] = Field(default=None, max_length=1000)
    timezone: Optional[str] = Field(default=None, max_length=50)
    language: Optional[str] = Field(default=None, max_length=10)
    preferences: Optional[dict] = None


class UserResponse(UserBase):
    """User response model"""
    user_id: UUID
    created_at: datetime
    updated_at: datetime
    email_verified_at: Optional[datetime] = None
    last_login_at: Optional[datetime] = None
    login_count: int
    roles: Optional[List[str]] = Field(default_factory=list)
    groups: Optional[List[str]] = Field(default_factory=list)


class UserProfile(UserResponse):
    """Extended user profile model"""
    courses_enrolled: int = 0
    courses_completed: int = 0
    total_points: int = 0
    current_streak: int = 0
    achievements: Optional[List[dict]] = Field(default_factory=list)
    learning_stats: Optional[dict] = Field(default_factory=dict)


class UserLogin(SQLModel):
    """User login model"""
    email: str = Field(max_length=255)
    password: str


class UserPasswordReset(SQLModel):
    """Password reset request model"""
    email: str = Field(max_length=255)


class UserPasswordResetConfirm(SQLModel):
    """Password reset confirmation model"""
    token: str
    new_password: str = Field(min_length=8)


class UserEmailVerification(SQLModel):
    """Email verification model"""
    token: str


class UserChangePassword(SQLModel):
    """Change password model"""
    current_password: str
    new_password: str = Field(min_length=8)


class UserSession(SQLModel, table=True):
    """User session model"""
    __tablename__ = "user_sessions"
    
    session_id: UUID = Field(primary_key=True)
    user_id: UUID = Field(foreign_key="users.user_id", index=True)
    tenant_id: UUID = Field(foreign_key="tenants.tenant_id", index=True)
    token_hash: str = Field(max_length=255)
    refresh_token_hash: Optional[str] = Field(default=None, max_length=255)
    expires_at: datetime
    created_at: datetime = Field(default_factory=datetime.utcnow)
    last_used_at: datetime = Field(default_factory=datetime.utcnow)
    ip_address: Optional[str] = Field(default=None, max_length=45)
    user_agent: Optional[str] = Field(default=None, max_length=500)
    is_active: bool = Field(default=True)


class UserVerificationToken(SQLModel, table=True):
    """User verification token model"""
    __tablename__ = "user_verification_tokens"
    
    token_id: UUID = Field(primary_key=True)
    user_id: UUID = Field(foreign_key="users.user_id", index=True)
    tenant_id: UUID = Field(foreign_key="tenants.tenant_id", index=True)
    token_hash: str = Field(max_length=255)
    token_type: str = Field(max_length=50)  # 'email_verification', 'password_reset'
    expires_at: datetime
    created_at: datetime = Field(default_factory=datetime.utcnow)
    used_at: Optional[datetime] = None
    is_used: bool = Field(default=False)
