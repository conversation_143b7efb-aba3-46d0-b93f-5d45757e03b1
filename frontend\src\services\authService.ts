import { apiClient } from './api';
import { User, LoginForm, RegisterForm, ApiResponse } from '@/types';

export interface LoginResponse {
  user: User;
  token: string;
  refreshToken: string;
}

export interface RegisterResponse {
  user: User;
  message: string;
}

export const authService = {
  // Login user
  login: async (credentials: LoginForm): Promise<LoginResponse> => {
    return apiClient.post<ApiResponse<LoginResponse>>('/auth/login', credentials)
      .then(response => response.data);
  },

  // Register user
  register: async (userData: RegisterForm): Promise<RegisterResponse> => {
    return apiClient.post<ApiResponse<RegisterResponse>>('/auth/register', userData)
      .then(response => response.data);
  },

  // Logout user
  logout: async (): Promise<void> => {
    return apiClient.post('/auth/logout');
  },

  // Refresh token
  refreshToken: async (refreshToken: string): Promise<LoginResponse> => {
    return apiClient.post<ApiResponse<LoginResponse>>('/auth/refresh', { refreshToken })
      .then(response => response.data);
  },

  // Get current user profile
  getProfile: async (): Promise<User> => {
    return apiClient.get<ApiResponse<User>>('/auth/profile')
      .then(response => response.data);
  },

  // Update user profile
  updateProfile: async (userData: Partial<User>): Promise<User> => {
    return apiClient.patch<ApiResponse<User>>('/auth/profile', userData)
      .then(response => response.data);
  },

  // Change password
  changePassword: async (currentPassword: string, newPassword: string): Promise<void> => {
    return apiClient.post('/auth/change-password', {
      currentPassword,
      newPassword,
    });
  },

  // Request password reset
  requestPasswordReset: async (email: string): Promise<void> => {
    return apiClient.post('/auth/forgot-password', { email });
  },

  // Reset password
  resetPassword: async (token: string, newPassword: string): Promise<void> => {
    return apiClient.post('/auth/reset-password', {
      token,
      newPassword,
    });
  },

  // Verify email
  verifyEmail: async (token: string): Promise<void> => {
    return apiClient.post('/auth/verify-email', { token });
  },

  // Resend verification email
  resendVerificationEmail: async (): Promise<void> => {
    return apiClient.post('/auth/resend-verification');
  },
};
