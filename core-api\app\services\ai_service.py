"""
AI service for question generation, scoring, and content processing
"""

import json
import time
import asyncio
import aiohttp
import hashlib
from datetime import datetime
from typing import Optional, List, Dict, Any
from uuid import UUID, uuid4

import openai
from sqlmodel import Session, select

from ..core.config import settings
from ..models.ai import (
    AITask, AITaskCreate, AITaskType, AITaskStatus,
    QuestionGenerationRequest, QuestionGenerationResponse, GeneratedQuestion,
    ScoringRequest, ScoringResponse, ScoringResult,
    ModerationRequest, ModerationResponse, ModerationResult, ModerationFlag,
    SpeechSynthesisRequest, SpeechSynthesisResponse,
    SpeechRecognitionRequest, SpeechRecognitionResponse,
    PlagiarismCheckRequest, PlagiarismResponse, PlagiarismResult,
    AIQuotaLimit, QuestionType, DifficultyLevel
)


class AIService:
    """AI service for all AI operations"""
    
    def __init__(self, db: Session):
        self.db = db
        self.openai_client = openai.AsyncOpenAI(api_key=settings.OPENAI_API_KEY)
    
    async def generate_questions(
        self, 
        request: QuestionGenerationRequest, 
        user_id: UUID, 
        tenant_id: UUID
    ) -> QuestionGenerationResponse:
        """Generate questions using AI"""
        # Check quota limits
        await self._check_quota_limits(tenant_id, AITaskType.QUESTION_GENERATION)
        
        # Create AI task
        task = await self._create_ai_task(
            task_type=AITaskType.QUESTION_GENERATION,
            input_data=request.dict(),
            user_id=user_id,
            tenant_id=tenant_id
        )
        
        try:
            start_time = time.time()
            
            # Generate questions using OpenAI
            questions = await self._generate_questions_with_openai(request)
            
            processing_time = time.time() - start_time
            
            # Calculate cost (approximate)
            tokens_used = sum(len(q.content.split()) * 1.3 for q in questions)  # Rough estimate
            cost_usd = tokens_used * 0.00003  # GPT-4 pricing estimate
            
            # Update task
            await self._update_ai_task(
                task=task,
                status=AITaskStatus.COMPLETED,
                output_data={
                    "questions": [q.dict() for q in questions],
                    "count": len(questions)
                },
                processing_time=processing_time,
                tokens_used=int(tokens_used),
                cost_usd=cost_usd
            )
            
            # Update quota usage
            await self._update_quota_usage(tenant_id, cost_usd, 1)
            
            return QuestionGenerationResponse(
                task_id=task.task_id,
                questions=questions,
                generation_time_seconds=processing_time,
                model_used=settings.OPENAI_MODEL,
                tokens_used=int(tokens_used),
                cost_usd=cost_usd
            )
            
        except Exception as e:
            await self._update_ai_task(
                task=task,
                status=AITaskStatus.FAILED,
                error_message=str(e)
            )
            raise
    
    async def score_content(
        self, 
        request: ScoringRequest, 
        user_id: UUID, 
        tenant_id: UUID
    ) -> ScoringResponse:
        """Score content using AI"""
        # Check quota limits
        await self._check_quota_limits(tenant_id, AITaskType.CONTENT_SCORING)
        
        # Create AI task
        task = await self._create_ai_task(
            task_type=AITaskType.CONTENT_SCORING,
            input_data=request.dict(),
            user_id=user_id,
            tenant_id=tenant_id
        )
        
        try:
            start_time = time.time()
            
            # Score content using OpenAI
            result = await self._score_content_with_openai(request)
            
            processing_time = time.time() - start_time
            
            # Calculate cost
            tokens_used = len(request.content.split()) * 1.5  # Rough estimate
            cost_usd = tokens_used * 0.00003
            
            # Update task
            await self._update_ai_task(
                task=task,
                status=AITaskStatus.COMPLETED,
                output_data=result.dict(),
                processing_time=processing_time,
                tokens_used=int(tokens_used),
                cost_usd=cost_usd
            )
            
            # Update quota usage
            await self._update_quota_usage(tenant_id, cost_usd, 1)
            
            return ScoringResponse(
                task_id=task.task_id,
                result=result,
                processing_time_seconds=processing_time,
                model_used=settings.SCORING_MODEL,
                tokens_used=int(tokens_used),
                cost_usd=cost_usd
            )
            
        except Exception as e:
            await self._update_ai_task(
                task=task,
                status=AITaskStatus.FAILED,
                error_message=str(e)
            )
            raise
    
    async def moderate_content(
        self, 
        request: ModerationRequest, 
        user_id: UUID, 
        tenant_id: UUID
    ) -> ModerationResponse:
        """Moderate content using AI"""
        # Create AI task
        task = await self._create_ai_task(
            task_type=AITaskType.CONTENT_MODERATION,
            input_data=request.dict(),
            user_id=user_id,
            tenant_id=tenant_id
        )
        
        try:
            start_time = time.time()
            
            # Moderate content using OpenAI
            result = await self._moderate_content_with_openai(request)
            
            processing_time = time.time() - start_time
            
            # Update task
            await self._update_ai_task(
                task=task,
                status=AITaskStatus.COMPLETED,
                output_data=result.dict(),
                processing_time=processing_time
            )
            
            return ModerationResponse(
                task_id=task.task_id,
                result=result,
                processing_time_seconds=processing_time
            )
            
        except Exception as e:
            await self._update_ai_task(
                task=task,
                status=AITaskStatus.FAILED,
                error_message=str(e)
            )
            raise

    async def synthesize_speech(
        self,
        request: SpeechSynthesisRequest,
        user_id: UUID,
        tenant_id: UUID
    ) -> SpeechSynthesisResponse:
        """Convert text to speech using Azure Speech Services"""
        # Create AI task
        task = await self._create_ai_task(
            task_type=AITaskType.SPEECH_SYNTHESIS,
            input_data=request.model_dump(),
            user_id=user_id,
            tenant_id=tenant_id
        )

        try:
            start_time = time.time()

            # This would integrate with Azure Speech Services
            # For now, return a mock response
            audio_url = f"https://storage.example.com/audio/{task.task_id}.{request.output_format}"
            duration_seconds = len(request.text.split()) * 0.5  # Rough estimate
            file_size_bytes = int(duration_seconds * 32000)  # Rough estimate for 32kbps

            processing_time = time.time() - start_time

            # Update task
            await self._update_ai_task(
                task=task,
                status=AITaskStatus.COMPLETED,
                output_data={
                    "audio_url": audio_url,
                    "duration_seconds": duration_seconds,
                    "file_size_bytes": file_size_bytes
                },
                processing_time=processing_time
            )

            return SpeechSynthesisResponse(
                task_id=task.task_id,
                audio_url=audio_url,
                duration_seconds=duration_seconds,
                file_size_bytes=file_size_bytes,
                processing_time_seconds=processing_time
            )

        except Exception as e:
            await self._update_ai_task(
                task=task,
                status=AITaskStatus.FAILED,
                error_message=str(e)
            )
            raise

    async def recognize_speech(
        self,
        request: SpeechRecognitionRequest,
        user_id: UUID,
        tenant_id: UUID
    ) -> SpeechRecognitionResponse:
        """Convert speech to text using Azure Speech Services"""
        # Create AI task
        task = await self._create_ai_task(
            task_type=AITaskType.SPEECH_RECOGNITION,
            input_data=request.model_dump(),
            user_id=user_id,
            tenant_id=tenant_id
        )

        try:
            start_time = time.time()

            # This would integrate with Azure Speech Services
            # For now, return a mock response
            transcript = "This is a sample transcript of the provided audio."
            confidence = 0.95
            duration_seconds = 30.0  # Would be determined from audio file

            processing_time = time.time() - start_time

            # Update task
            await self._update_ai_task(
                task=task,
                status=AITaskStatus.COMPLETED,
                output_data={
                    "transcript": transcript,
                    "confidence": confidence,
                    "duration_seconds": duration_seconds
                },
                processing_time=processing_time
            )

            return SpeechRecognitionResponse(
                task_id=task.task_id,
                transcript=transcript,
                confidence=confidence,
                duration_seconds=duration_seconds,
                processing_time_seconds=processing_time
            )

        except Exception as e:
            await self._update_ai_task(
                task=task,
                status=AITaskStatus.FAILED,
                error_message=str(e)
            )
            raise

    async def check_plagiarism(
        self,
        request: PlagiarismCheckRequest,
        user_id: UUID,
        tenant_id: UUID
    ) -> PlagiarismResponse:
        """Check content for plagiarism"""
        # Create AI task
        task = await self._create_ai_task(
            task_type=AITaskType.PLAGIARISM_DETECTION,
            input_data=request.model_dump(),
            user_id=user_id,
            tenant_id=tenant_id
        )

        try:
            start_time = time.time()

            # This would integrate with Turnitin or similar service
            # For now, return a mock response
            result = await self._check_plagiarism_mock(request)

            processing_time = time.time() - start_time

            # Update task
            await self._update_ai_task(
                task=task,
                status=AITaskStatus.COMPLETED,
                output_data=result.model_dump(),
                processing_time=processing_time
            )

            return PlagiarismResponse(
                task_id=task.task_id,
                result=result,
                processing_time_seconds=processing_time
            )

        except Exception as e:
            await self._update_ai_task(
                task=task,
                status=AITaskStatus.FAILED,
                error_message=str(e)
            )
            raise

    async def _check_plagiarism_mock(self, request: PlagiarismCheckRequest) -> PlagiarismResult:
        """Mock plagiarism detection (would be replaced with real service)"""
        # Simple hash-based similarity check for demo
        content_hash = hashlib.md5(request.content.encode()).hexdigest()

        # Mock similarity percentage based on content length and hash
        similarity = min(len(request.content) % 30, 25)  # 0-25% similarity

        matches = []
        if similarity > 15:
            matches.append({
                "source_title": "Sample Academic Paper",
                "source_url": "https://example.com/paper.pdf",
                "similarity_percentage": similarity,
                "matched_text": request.content[:100] + "...",
                "source_type": "internet"
            })

        is_plagiarized = similarity > request.similarity_threshold * 100
        risk_level = "low"
        if similarity > 70:
            risk_level = "critical"
        elif similarity > 50:
            risk_level = "high"
        elif similarity > 30:
            risk_level = "medium"

        from ..models.ai import PlagiarismResult, PlagiarismMatch

        return PlagiarismResult(
            overall_similarity=similarity,
            is_plagiarized=is_plagiarized,
            matches=[PlagiarismMatch(**match) for match in matches],
            original_percentage=100 - similarity,
            risk_level=risk_level
        )

    async def _generate_questions_with_openai(self, request: QuestionGenerationRequest) -> List[GeneratedQuestion]:
        """Generate questions using OpenAI"""
        prompt = self._build_question_generation_prompt(request)
        
        response = await self.openai_client.chat.completions.create(
            model=settings.OPENAI_MODEL,
            messages=[{"role": "user", "content": prompt}],
            temperature=settings.OPENAI_TEMPERATURE,
            max_tokens=settings.OPENAI_MAX_TOKENS
        )
        
        # Parse the response
        content = response.choices[0].message.content
        questions_data = json.loads(content)
        
        questions = []
        for q_data in questions_data.get("questions", []):
            question = GeneratedQuestion(
                title=q_data.get("title", ""),
                content=q_data.get("content", ""),
                question_type=request.question_type,
                difficulty=request.difficulty_level,
                points=q_data.get("points", 1),
                time_limit_seconds=q_data.get("time_limit_seconds"),
                explanation=q_data.get("explanation"),
                question_data=q_data.get("question_data", {}),
                tags=q_data.get("tags", []),
                estimated_time_minutes=q_data.get("estimated_time_minutes")
            )
            questions.append(question)
        
        return questions
    
    async def _score_content_with_openai(self, request: ScoringRequest) -> ScoringResult:
        """Score content using OpenAI"""
        prompt = self._build_scoring_prompt(request)
        
        response = await self.openai_client.chat.completions.create(
            model=settings.SCORING_MODEL,
            messages=[{"role": "user", "content": prompt}],
            temperature=settings.SCORING_TEMPERATURE,
            max_tokens=settings.SCORING_MAX_TOKENS
        )
        
        # Parse the response
        content = response.choices[0].message.content
        result_data = json.loads(content)
        
        return ScoringResult(
            overall_score=result_data.get("overall_score", 0),
            max_score=request.max_score,
            percentage=(result_data.get("overall_score", 0) / request.max_score) * 100,
            rubric_breakdown=result_data.get("rubric_breakdown", {}),
            feedback=result_data.get("feedback", ""),
            strengths=result_data.get("strengths", []),
            areas_for_improvement=result_data.get("areas_for_improvement", []),
            confidence=result_data.get("confidence", 0.8),
            estimated_level=result_data.get("estimated_level")
        )
    
    async def _moderate_content_with_openai(self, request: ModerationRequest) -> ModerationResult:
        """Moderate content using OpenAI"""
        response = await self.openai_client.moderations.create(input=request.content)
        
        moderation = response.results[0]
        flags = []
        
        for category, flagged in moderation.categories.dict().items():
            if category in request.check_categories:
                score = getattr(moderation.category_scores, category, 0.0)
                severity = "low"
                if score > 0.8:
                    severity = "critical"
                elif score > 0.6:
                    severity = "high"
                elif score > 0.4:
                    severity = "medium"
                
                flags.append(ModerationFlag(
                    category=category,
                    flagged=flagged,
                    score=score,
                    severity=severity
                ))
        
        overall_flagged = any(flag.flagged for flag in flags)
        overall_score = max((flag.score for flag in flags), default=0.0)
        
        return ModerationResult(
            flagged=overall_flagged,
            overall_score=overall_score,
            flags=flags,
            filtered_content=request.content if not overall_flagged else "[Content filtered]",
            reason="Content violates community guidelines" if overall_flagged else None
        )
    
    def _build_question_generation_prompt(self, request: QuestionGenerationRequest) -> str:
        """Build prompt for question generation"""
        prompt = f"""
Generate {request.count} {request.question_type.value} questions about "{request.topic}" 
at {request.difficulty_level.value} difficulty level in {request.language}.

Requirements:
- Each question should be educational and appropriate
- Include clear instructions and correct answers
- For multiple choice: provide 4 options with 1 correct answer
- For true/false: provide the statement and correct answer
- For essay: provide clear prompts and scoring criteria
- Estimate time needed to complete each question

Context: {request.context or "General educational assessment"}

Learning Objectives: {', '.join(request.learning_objectives or [])}

Additional Instructions: {request.additional_instructions or "None"}

Return the response as JSON with this structure:
{{
  "questions": [
    {{
      "title": "Question title",
      "content": "Question content/prompt",
      "points": 1,
      "time_limit_seconds": 300,
      "explanation": "Explanation of correct answer",
      "question_data": {{
        "options": ["A", "B", "C", "D"],  // for multiple choice
        "correct_answer": "A",
        "additional_info": {{}}
      }},
      "tags": ["tag1", "tag2"],
      "estimated_time_minutes": 5
    }}
  ]
}}
"""
        return prompt
    
    def _build_scoring_prompt(self, request: ScoringRequest) -> str:
        """Build prompt for content scoring"""
        criteria_text = "\n".join([
            f"- {c.criterion} (weight: {c.weight}, max: {c.max_score}): {c.description or 'No description'}"
            for c in request.scoring_criteria
        ])
        
        prompt = f"""
Score the following student response based on the given criteria.

Question/Context: {request.question_context}

Student Response: {request.content}

Scoring Criteria:
{criteria_text}

Maximum Total Score: {request.max_score}
Rubric Type: {request.rubric_type}
Language: {request.language}

Please provide:
1. Overall score (0 to {request.max_score})
2. Breakdown by criteria
3. Detailed feedback
4. Strengths and areas for improvement
5. Confidence level (0.0 to 1.0)

Return the response as JSON with this structure:
{{
  "overall_score": 85.5,
  "rubric_breakdown": {{
    "criterion1": 4.5,
    "criterion2": 3.8
  }},
  "feedback": "Detailed feedback text",
  "strengths": ["strength1", "strength2"],
  "areas_for_improvement": ["area1", "area2"],
  "confidence": 0.9,
  "estimated_level": "intermediate"
}}
"""
        return prompt
    
    async def _create_ai_task(
        self, 
        task_type: AITaskType, 
        input_data: Dict[str, Any], 
        user_id: UUID, 
        tenant_id: UUID
    ) -> AITask:
        """Create AI task record"""
        task = AITask(
            task_id=uuid4(),
            tenant_id=tenant_id,
            user_id=user_id,
            task_type=task_type,
            input_data=input_data,
            started_at=datetime.utcnow()
        )
        
        self.db.add(task)
        self.db.commit()
        self.db.refresh(task)
        
        return task
    
    async def _update_ai_task(
        self,
        task: AITask,
        status: AITaskStatus,
        output_data: Optional[Dict[str, Any]] = None,
        error_message: Optional[str] = None,
        processing_time: Optional[float] = None,
        tokens_used: Optional[int] = None,
        cost_usd: Optional[float] = None
    ):
        """Update AI task record"""
        task.status = status
        if output_data:
            task.output_data = output_data
        if error_message:
            task.error_message = error_message
        if processing_time:
            task.processing_time_seconds = processing_time
        if tokens_used:
            task.tokens_used = tokens_used
        if cost_usd:
            task.cost_usd = cost_usd
        
        if status in [AITaskStatus.COMPLETED, AITaskStatus.FAILED]:
            task.completed_at = datetime.utcnow()
        
        task.updated_at = datetime.utcnow()
        
        self.db.add(task)
        self.db.commit()
    
    async def _check_quota_limits(self, tenant_id: UUID, task_type: AITaskType):
        """Check if tenant has exceeded quota limits"""
        quota = self.db.exec(
            select(AIQuotaLimit).where(AIQuotaLimit.tenant_id == tenant_id)
        ).first()
        
        if not quota:
            # Create default quota
            quota = AIQuotaLimit(tenant_id=tenant_id)
            self.db.add(quota)
            self.db.commit()
        
        # Check daily limits
        if quota.daily_requests_used >= quota.daily_requests_limit:
            raise ValueError("Daily request limit exceeded")
        
        if quota.daily_cost_used_usd >= quota.daily_cost_limit_usd:
            raise ValueError("Daily cost limit exceeded")
    
    async def _update_quota_usage(self, tenant_id: UUID, cost_usd: float, requests: int):
        """Update quota usage"""
        quota = self.db.exec(
            select(AIQuotaLimit).where(AIQuotaLimit.tenant_id == tenant_id)
        ).first()
        
        if quota:
            quota.daily_requests_used += requests
            quota.monthly_requests_used += requests
            quota.daily_cost_used_usd += cost_usd
            quota.monthly_cost_used_usd += cost_usd
            quota.updated_at = datetime.utcnow()
            
            self.db.add(quota)
            self.db.commit()
