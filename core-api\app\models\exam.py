"""
Exam and submission models
"""

from datetime import datetime
from typing import Optional, List, Any
from uuid import UUID
from enum import Enum

from sqlmodel import SQLModel, Field

from .base import TimestampMixin, TenantMixin, MetadataMixin


class ExamStatus(str, Enum):
    """Exam status enumeration"""
    DRAFT = "draft"
    PUBLISHED = "published"
    ARCHIVED = "archived"


class SubmissionStatus(str, Enum):
    """Submission status enumeration"""
    IN_PROGRESS = "in_progress"
    SUBMITTED = "submitted"
    GRADED = "graded"
    EXPIRED = "expired"


class ShowResultsMode(str, Enum):
    """Show results mode enumeration"""
    IMMEDIATE = "immediate"
    AFTER_SUBMISSION = "after_submission"
    MANUAL = "manual"


class ExamBase(SQLModel):
    """Base exam model"""
    title: str = Field(max_length=255)
    description: Optional[str] = None
    instructions: Optional[str] = None
    time_limit_minutes: int = Field(default=60, ge=1)
    passing_score: float = Field(default=70.0, ge=0.0, le=100.0)
    max_attempts: int = Field(default=1, ge=1)
    shuffle_questions: bool = Field(default=False)
    shuffle_options: bool = Field(default=False)
    show_results: ShowResultsMode = Field(default=ShowResultsMode.IMMEDIATE)
    allow_review: bool = Field(default=True)
    is_proctored: bool = Field(default=False)


class Exam(ExamBase, TenantMixin, TimestampMixin, MetadataMixin, table=True):
    """Exam table model"""
    __tablename__ = "exams"
    
    exam_id: UUID = Field(primary_key=True)
    course_id: UUID = Field(foreign_key="courses.course_id", index=True)
    created_by: UUID = Field(foreign_key="users.user_id", index=True)
    status: ExamStatus = Field(default=ExamStatus.DRAFT)
    question_ids: List[UUID] = Field(sa_column_kwargs={"type_": "JSONB"})
    scheduled_start: Optional[datetime] = None
    scheduled_end: Optional[datetime] = None
    published_at: Optional[datetime] = None
    total_points: int = Field(default=0)
    settings: Optional[dict] = Field(default_factory=dict, sa_column_kwargs={"type_": "JSONB"})


class ExamCreate(ExamBase):
    """Exam creation model"""
    course_id: UUID
    question_ids: List[UUID]
    scheduled_start: Optional[datetime] = None
    scheduled_end: Optional[datetime] = None


class ExamUpdate(SQLModel):
    """Exam update model"""
    title: Optional[str] = Field(default=None, max_length=255)
    description: Optional[str] = None
    instructions: Optional[str] = None
    time_limit_minutes: Optional[int] = Field(default=None, ge=1)
    passing_score: Optional[float] = Field(default=None, ge=0.0, le=100.0)
    max_attempts: Optional[int] = Field(default=None, ge=1)
    shuffle_questions: Optional[bool] = None
    shuffle_options: Optional[bool] = None
    show_results: Optional[ShowResultsMode] = None
    allow_review: Optional[bool] = None
    is_proctored: Optional[bool] = None
    status: Optional[ExamStatus] = None
    question_ids: Optional[List[UUID]] = None
    scheduled_start: Optional[datetime] = None
    scheduled_end: Optional[datetime] = None


class ExamResponse(ExamBase):
    """Exam response model"""
    exam_id: UUID
    course_id: UUID
    created_by: UUID
    status: ExamStatus
    question_ids: List[UUID]
    scheduled_start: Optional[datetime] = None
    scheduled_end: Optional[datetime] = None
    published_at: Optional[datetime] = None
    total_points: int
    created_at: datetime
    updated_at: datetime
    course_title: Optional[str] = None
    creator_name: Optional[str] = None
    question_count: int = 0
    submission_count: Optional[int] = None
    average_score: Optional[float] = None


class ExamSubmissionBase(SQLModel):
    """Base exam submission model"""
    started_at: datetime = Field(default_factory=datetime.utcnow)
    submitted_at: Optional[datetime] = None
    time_spent_seconds: int = Field(default=0)
    status: SubmissionStatus = Field(default=SubmissionStatus.IN_PROGRESS)
    score: Optional[float] = Field(default=None, ge=0.0, le=100.0)
    passed: Optional[bool] = None
    attempt_number: int = Field(default=1, ge=1)


class ExamSubmission(ExamSubmissionBase, TenantMixin, TimestampMixin, table=True):
    """Exam submission table model"""
    __tablename__ = "exam_submissions"
    
    submission_id: UUID = Field(primary_key=True)
    exam_id: UUID = Field(foreign_key="exams.exam_id", index=True)
    user_id: UUID = Field(foreign_key="users.user_id", index=True)
    graded_by: Optional[UUID] = Field(foreign_key="users.user_id", default=None)
    graded_at: Optional[datetime] = None
    feedback: Optional[str] = None
    submission_data: Optional[dict] = Field(default_factory=dict, sa_column_kwargs={"type_": "JSONB"})


class ExamSubmissionCreate(SQLModel):
    """Exam submission creation model"""
    exam_id: UUID


class ExamSubmissionUpdate(SQLModel):
    """Exam submission update model"""
    status: Optional[SubmissionStatus] = None
    score: Optional[float] = Field(default=None, ge=0.0, le=100.0)
    passed: Optional[bool] = None
    feedback: Optional[str] = None


class ExamSubmissionResponse(ExamSubmissionBase):
    """Exam submission response model"""
    submission_id: UUID
    exam_id: UUID
    user_id: UUID
    graded_by: Optional[UUID] = None
    graded_at: Optional[datetime] = None
    feedback: Optional[str] = None
    created_at: datetime
    updated_at: datetime
    exam_title: Optional[str] = None
    user_name: Optional[str] = None
    grader_name: Optional[str] = None


class SubmissionAnswerBase(SQLModel):
    """Base submission answer model"""
    answer_data: dict = Field(sa_column_kwargs={"type_": "JSONB"})
    time_spent_seconds: int = Field(default=0, ge=0)
    is_correct: Optional[bool] = None
    points_earned: Optional[float] = Field(default=None, ge=0.0)
    auto_graded: bool = Field(default=False)


class SubmissionAnswer(SubmissionAnswerBase, TenantMixin, TimestampMixin, table=True):
    """Submission answer table model"""
    __tablename__ = "submission_answers"
    
    answer_id: UUID = Field(primary_key=True)
    submission_id: UUID = Field(foreign_key="exam_submissions.submission_id", index=True)
    question_id: UUID = Field(foreign_key="questions.question_id", index=True)
    graded_by: Optional[UUID] = Field(foreign_key="users.user_id", default=None)
    graded_at: Optional[datetime] = None
    feedback: Optional[str] = None


class SubmissionAnswerCreate(SQLModel):
    """Submission answer creation model"""
    question_id: UUID
    answer_data: dict


class SubmissionAnswerUpdate(SQLModel):
    """Submission answer update model"""
    answer_data: Optional[dict] = None
    is_correct: Optional[bool] = None
    points_earned: Optional[float] = Field(default=None, ge=0.0)
    feedback: Optional[str] = None


class ExamResult(SQLModel):
    """Exam result model"""
    submission: ExamSubmissionResponse
    exam: ExamResponse
    answers: List[SubmissionAnswer]
    total_questions: int
    correct_answers: int
    score: float
    percentage: float
    passed: bool
    time_spent_seconds: int
    feedback: Optional[str] = None


class ExamStatistics(SQLModel):
    """Exam statistics model"""
    exam_id: UUID
    total_submissions: int
    completed_submissions: int
    average_score: float
    pass_rate: float
    average_time_minutes: float
    question_statistics: List[dict]
    score_distribution: dict
    completion_rate: float


class ExamPreview(SQLModel):
    """Exam preview model (without questions)"""
    exam_id: UUID
    title: str
    description: Optional[str] = None
    instructions: Optional[str] = None
    time_limit_minutes: int
    passing_score: float
    max_attempts: int
    question_count: int
    total_points: int
    scheduled_start: Optional[datetime] = None
    scheduled_end: Optional[datetime] = None
    user_attempts: int = 0
    can_attempt: bool = True


class ExamGrading(SQLModel):
    """Exam grading model"""
    submission_id: UUID
    grades: List[dict]  # [{"question_id": UUID, "points": float, "feedback": str}]
    overall_feedback: Optional[str] = None
