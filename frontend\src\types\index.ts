// Core types for Arroyo University Frontend

export interface User {
  id: string;
  email: string;
  username?: string;
  firstName?: string;
  lastName?: string;
  avatar?: string;
  status: 'active' | 'inactive' | 'pending' | 'suspended';
  emailVerified: boolean;
  phone?: string;
  language: string;
  timezone: string;
  lastLogin?: string;
  metadata?: Record<string, any>;
  createdAt: string;
  updatedAt: string;
}

export interface Tenant {
  id: string;
  name: string;
  subdomain: string;
  description?: string;
  status: 'active' | 'inactive' | 'provisioning' | 'suspended';
  plan: string;
  settings: Record<string, any>;
  createdAt: string;
  updatedAt: string;
}

export interface Role {
  id: string;
  tenantId: string;
  name: string;
  description?: string;
  permissions: Record<string, any>;
  isSystemRole: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface Group {
  id: string;
  tenantId: string;
  name: string;
  description?: string;
  isPublic: boolean;
  leaderId?: string;
  leader?: User;
  memberCount: number;
  metadata?: Record<string, any>;
  createdAt: string;
  updatedAt: string;
}

export interface Course {
  id: string;
  tenantId: string;
  title: string;
  description?: string;
  creatorId: string;
  creator: User;
  status: 'draft' | 'published' | 'archived';
  category: string;
  level: 'beginner' | 'intermediate' | 'advanced';
  duration: number; // in weeks
  studentCount: number;
  rating: number;
  metadata?: Record<string, any>;
  settings?: Record<string, any>;
  version: number;
  createdAt: string;
  updatedAt: string;
  publishedAt?: string;
}

export interface CourseRating {
  id: string;
  courseId: string;
  userId: string;
  ratingType: 'like' | 'dislike';
  createdAt: string;
}

export interface ExpertReview {
  id: string;
  courseId: string;
  expertId: string;
  expert: User;
  expertArea: string;
  rating: 'muy_malo' | 'malo' | 'neutral' | 'bueno' | 'excelente';
  title?: string;
  reviewText?: string;
  isVerified: boolean;
  verifiedBy?: string;
  createdAt: string;
  verifiedAt?: string;
}

export interface Exam {
  id: string;
  courseId: string;
  title: string;
  description?: string;
  status: 'draft' | 'published' | 'archived';
  timeLimit?: number; // in minutes
  maxAttempts?: number;
  passingScore?: number;
  questions: Question[];
  createdAt: string;
  updatedAt: string;
}

export interface Question {
  id: string;
  type: 'multiple_choice' | 'essay' | 'speaking' | 'listening' | 'reading';
  title: string;
  content: string;
  options?: string[];
  correctAnswer?: string | string[];
  points: number;
  timeLimit?: number;
  metadata?: Record<string, any>;
  createdAt: string;
  updatedAt: string;
}

export interface ExamSubmission {
  id: string;
  examId: string;
  userId: string;
  status: 'pending' | 'in_progress' | 'completed' | 'graded';
  answers: Record<string, any>;
  score?: number;
  feedback?: string;
  startedAt: string;
  submittedAt?: string;
  gradedAt?: string;
}

export interface CareerPath {
  id: string;
  name: string;
  description: string;
  skills: Skill[];
  estimatedDuration: number; // in months
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  createdBy: string;
  isPublic: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface Skill {
  id: string;
  name: string;
  description?: string;
  category: string;
  prerequisites: string[];
  courses: Course[];
  estimatedHours: number;
}

export interface UserProgress {
  userId: string;
  courseId?: string;
  careerPathId?: string;
  progress: number; // 0-100
  completedAt?: string;
  lastAccessedAt: string;
}

export interface Notification {
  id: string;
  userId: string;
  type: 'email' | 'push' | 'in_app';
  title: string;
  message: string;
  data?: Record<string, any>;
  read: boolean;
  createdAt: string;
  readAt?: string;
}

export interface LeaderboardEntry {
  userId: string;
  user: User;
  score: number;
  rank: number;
  category: 'creator' | 'student';
  period: 'weekly' | 'monthly' | 'total';
}

// API Response types
export interface ApiResponse<T> {
  data: T;
  message?: string;
  success: boolean;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// Form types
export interface LoginForm {
  email: string;
  password: string;
  rememberMe?: boolean;
}

export interface RegisterForm {
  email: string;
  password: string;
  confirmPassword: string;
  firstName: string;
  lastName: string;
  acceptTerms: boolean;
}

export interface CourseForm {
  title: string;
  description: string;
  category: string;
  level: 'beginner' | 'intermediate' | 'advanced';
  duration: number;
}

// UI State types
export interface UIState {
  sidebarOpen: boolean;
  theme: 'light' | 'dark';
  language: string;
}

// Auth types
export interface AuthState {
  user: User | null;
  token: string | null;
  refreshToken: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
}

// Filter types
export interface CourseFilters {
  category?: string;
  level?: string;
  search?: string;
  sortBy?: 'title' | 'rating' | 'students' | 'created';
  sortOrder?: 'asc' | 'desc';
}

export interface GroupFilters {
  category?: string;
  isPublic?: boolean;
  search?: string;
}

// Error types
export interface ApiError {
  message: string;
  code?: string;
  details?: Record<string, any>;
}
