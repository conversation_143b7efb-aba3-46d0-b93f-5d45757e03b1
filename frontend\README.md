# Arroyo University Frontend

A modern React application built with Vite, TypeScript, and Tailwind CSS for the Arroyo University educational assessment platform.

## 🚀 Features

- **Modern React 18** with TypeScript for type safety
- **Vite** for fast development and building
- **Tailwind CSS** for utility-first styling
- **React Query** for server state management
- **Zustand** for client state management
- **React Router** for navigation
- **React Hook Form** with Zod validation
- **PWA Support** with offline capabilities
- **Dark/Light Theme** support
- **Internationalization** ready
- **Responsive Design** for all devices

## 📁 Project Structure

```
frontend/
├── public/                 # Static assets
├── src/
│   ├── components/        # Reusable UI components
│   │   ├── ui/           # Base UI components (Button, Input, etc.)
│   │   ├── layout/       # Layout components (Header, Sidebar, etc.)
│   │   └── courses/      # Course-specific components
│   ├── pages/            # Page components
│   │   ├── auth/         # Authentication pages
│   │   └── ...           # Other pages
│   ├── services/         # API services
│   ├── store/            # Zustand stores
│   ├── types/            # TypeScript type definitions
│   ├── lib/              # Utility functions
│   ├── hooks/            # Custom React hooks
│   └── assets/           # Images, icons, etc.
├── package.json
├── vite.config.ts
├── tailwind.config.js
└── tsconfig.json
```

## 🛠️ Tech Stack

### Core
- **React 18** - UI library
- **TypeScript** - Type safety
- **Vite** - Build tool and dev server

### Styling
- **Tailwind CSS** - Utility-first CSS framework
- **Radix UI** - Headless UI components
- **Lucide React** - Icon library
- **Class Variance Authority** - Component variants

### State Management
- **Zustand** - Lightweight state management
- **React Query** - Server state management
- **React Hook Form** - Form state management

### Routing & Navigation
- **React Router DOM** - Client-side routing

### Validation
- **Zod** - Schema validation
- **Hookform Resolvers** - Form validation integration

### Development
- **ESLint** - Code linting
- **Prettier** - Code formatting
- **Vitest** - Testing framework

## 🚦 Getting Started

### Prerequisites

- Node.js 18+ 
- npm 9+

### Installation

1. **Install dependencies**
   ```bash
   npm install
   ```

2. **Set up environment variables**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

3. **Start development server**
   ```bash
   npm run dev
   ```

4. **Open your browser**
   Navigate to `http://localhost:3000`

## 📜 Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint
- `npm run lint:fix` - Fix ESLint issues
- `npm run format` - Format code with Prettier
- `npm run type-check` - Run TypeScript type checking
- `npm run test` - Run tests
- `npm run test:ui` - Run tests with UI
- `npm run test:coverage` - Run tests with coverage

## 🎨 UI Components

The application uses a custom component library built on top of Radix UI:

### Base Components
- `Button` - Customizable button with variants
- `Input` - Form input with validation states
- `Card` - Container component with header/content/footer
- `Badge` - Status and category indicators
- `Avatar` - User profile images with fallbacks

### Layout Components
- `Header` - Top navigation with search and user menu
- `Sidebar` - Left navigation menu
- `Layout` - Main layout wrapper

### Course Components
- `CourseCard` - Individual course display
- `CourseGrid` - Grid layout for courses
- `CourseFilters` - Search and filter interface

## 🔧 Configuration

### Environment Variables

```env
# API Configuration
VITE_API_BASE_URL=http://localhost:80/api/v1
VITE_CORE_API_URL=http://localhost:8000
VITE_AI_SERVICE_URL=http://localhost:8001
VITE_NOTIFICATION_SERVICE_URL=http://localhost:8002

# WebSocket Configuration
VITE_WS_URL=ws://localhost:8002/ws

# Authentication
VITE_JWT_STORAGE_KEY=arroyo_access_token
VITE_REFRESH_TOKEN_KEY=arroyo_refresh_token

# File Upload
VITE_MAX_FILE_SIZE_MB=200
VITE_ALLOWED_FILE_TYPES=pdf,doc,docx,mp3,wav,mp4,avi,jpg,jpeg,png,gif

# Feature Flags
VITE_ENABLE_REGISTRATION=true
VITE_ENABLE_PWA=true
VITE_ENABLE_ANALYTICS=true
```

### Tailwind Configuration

The project uses a custom Tailwind configuration with:
- Custom color palette
- Design system tokens
- Component utilities
- Dark mode support

### TypeScript Configuration

- Strict type checking enabled
- Path mapping for clean imports
- Modern ES2020 target

## 🌐 Internationalization

The app is prepared for internationalization using react-i18next:

- English (default)
- Spanish
- French
- German

## 📱 PWA Features

- **Offline Support** - Works without internet connection
- **App-like Experience** - Can be installed on devices
- **Background Sync** - Syncs data when connection is restored
- **Push Notifications** - Real-time notifications

## 🎯 Key Features Implemented

### Authentication
- Login/logout functionality
- JWT token management with refresh
- Protected routes
- User profile management

### Course Management
- Course marketplace with filtering
- Course enrollment and progress tracking
- Saved courses functionality
- Course creation interface

### User Experience
- Responsive design for all screen sizes
- Dark/light theme switching
- Real-time notifications
- Intuitive navigation

### Analytics & Progress
- Learning analytics dashboard
- Progress tracking
- Achievement system
- Leaderboards

## 🧪 Testing

The project uses Vitest for testing:

```bash
# Run all tests
npm run test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage

# Run tests with UI
npm run test:ui
```

## 🚀 Deployment

### Build for Production

```bash
npm run build
```

This creates a `dist` folder with optimized production files.

### Docker Deployment

The project includes a multi-stage Dockerfile:

```bash
# Build and run with Docker
docker build -t arroyo-frontend .
docker run -p 3000:80 arroyo-frontend
```

## 🔍 Code Quality

### ESLint Configuration
- React hooks rules
- TypeScript rules
- Import sorting
- Accessibility rules

### Prettier Configuration
- Consistent code formatting
- Tailwind CSS class sorting
- Import organization

## 🤝 Contributing

1. Follow the existing code style
2. Write tests for new features
3. Update documentation
4. Use conventional commit messages
5. Ensure all checks pass

## 📚 Learning Resources

- [React Documentation](https://react.dev/)
- [TypeScript Handbook](https://www.typescriptlang.org/docs/)
- [Tailwind CSS Documentation](https://tailwindcss.com/docs)
- [Vite Guide](https://vitejs.dev/guide/)
- [React Query Documentation](https://tanstack.com/query/latest)

## 🐛 Troubleshooting

### Common Issues

1. **Port already in use**
   ```bash
   # Kill process on port 3000
   npx kill-port 3000
   ```

2. **Module not found errors**
   ```bash
   # Clear node_modules and reinstall
   rm -rf node_modules package-lock.json
   npm install
   ```

3. **TypeScript errors**
   ```bash
   # Check TypeScript configuration
   npm run type-check
   ```

## 📄 License

This project is part of the Arroyo University platform. See the main project LICENSE file for details.
