import React, { useState } from 'react';
import { Plus, Search, Edit, Trash2, Shield, Users, Settings } from 'lucide-react';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Badge } from '@/components/ui/Badge';
import { Switch } from '@/components/ui/Switch';
import { Modal, ModalHeader, ModalFooter } from '@/components/ui/Modal';
import { DataTable, Column } from '@/components/ui/DataTable';
import toast from 'react-hot-toast';

interface Permission {
  id: string;
  name: string;
  description: string;
  category: string;
}

interface Role {
  id: string;
  name: string;
  description: string;
  permissions: string[];
  userCount: number;
  isSystem: boolean;
  createdAt: string;
  updatedAt: string;
}

const mockPermissions: Permission[] = [
  // Course Management
  { id: '1', name: 'course.create', description: 'Crear cursos', category: 'Cursos' },
  { id: '2', name: 'course.edit', description: 'Editar cursos', category: 'Cursos' },
  { id: '3', name: 'course.delete', description: 'Eliminar cursos', category: 'Cursos' },
  { id: '4', name: 'course.view', description: 'Ver cursos', category: 'Cursos' },
  { id: '5', name: 'course.publish', description: 'Publicar cursos', category: 'Cursos' },
  
  // User Management
  { id: '6', name: 'user.create', description: 'Crear usuarios', category: 'Usuarios' },
  { id: '7', name: 'user.edit', description: 'Editar usuarios', category: 'Usuarios' },
  { id: '8', name: 'user.delete', description: 'Eliminar usuarios', category: 'Usuarios' },
  { id: '9', name: 'user.view', description: 'Ver usuarios', category: 'Usuarios' },
  
  // Assessment
  { id: '10', name: 'exam.create', description: 'Crear exámenes', category: 'Evaluaciones' },
  { id: '11', name: 'exam.grade', description: 'Calificar exámenes', category: 'Evaluaciones' },
  { id: '12', name: 'question.create', description: 'Crear preguntas', category: 'Evaluaciones' },
  
  // Groups
  { id: '13', name: 'group.create', description: 'Crear grupos', category: 'Grupos' },
  { id: '14', name: 'group.manage', description: 'Gestionar grupos', category: 'Grupos' },
  
  // Analytics
  { id: '15', name: 'analytics.view', description: 'Ver analíticas', category: 'Analíticas' },
  { id: '16', name: 'analytics.export', description: 'Exportar datos', category: 'Analíticas' },
  
  // System
  { id: '17', name: 'system.settings', description: 'Configuración del sistema', category: 'Sistema' },
  { id: '18', name: 'role.manage', description: 'Gestionar roles', category: 'Sistema' },
];

const mockRoles: Role[] = [
  {
    id: '1',
    name: 'Administrador',
    description: 'Acceso completo al sistema',
    permissions: mockPermissions.map(p => p.id),
    userCount: 3,
    isSystem: true,
    createdAt: '2024-01-01',
    updatedAt: '2024-01-01',
  },
  {
    id: '2',
    name: 'Instructor',
    description: 'Puede crear y gestionar cursos',
    permissions: ['1', '2', '4', '5', '10', '11', '12', '13', '14', '15'],
    userCount: 12,
    isSystem: false,
    createdAt: '2024-01-01',
    updatedAt: '2024-01-15',
  },
  {
    id: '3',
    name: 'Estudiante',
    description: 'Acceso básico para estudiantes',
    permissions: ['4', '14'],
    userCount: 156,
    isSystem: true,
    createdAt: '2024-01-01',
    updatedAt: '2024-01-01',
  },
  {
    id: '4',
    name: 'Calificador',
    description: 'Solo puede calificar exámenes',
    permissions: ['4', '11', '15'],
    userCount: 5,
    isSystem: false,
    createdAt: '2024-01-10',
    updatedAt: '2024-01-10',
  },
];

export default function RoleManagementPage() {
  const [roles, setRoles] = useState<Role[]>(mockRoles);
  const [permissions] = useState<Permission[]>(mockPermissions);
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [editingRole, setEditingRole] = useState<Role | null>(null);
  const [searchTerm, setSearchTerm] = useState('');

  const [newRole, setNewRole] = useState({
    name: '',
    description: '',
    permissions: [] as string[],
  });

  const permissionCategories = [...new Set(permissions.map(p => p.category))];

  const filteredRoles = roles.filter(role =>
    role.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    role.description.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleCreateRole = () => {
    if (!newRole.name || !newRole.description) {
      toast.error('Por favor, completa todos los campos obligatorios');
      return;
    }

    const role: Role = {
      id: Date.now().toString(),
      name: newRole.name,
      description: newRole.description,
      permissions: newRole.permissions,
      userCount: 0,
      isSystem: false,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    setRoles([...roles, role]);
    setIsCreateModalOpen(false);
    setNewRole({ name: '', description: '', permissions: [] });
    toast.success('Rol creado exitosamente');
  };

  const handleEditRole = (role: Role) => {
    setEditingRole(role);
    setNewRole({
      name: role.name,
      description: role.description,
      permissions: [...role.permissions],
    });
  };

  const handleUpdateRole = () => {
    if (!editingRole || !newRole.name || !newRole.description) {
      toast.error('Por favor, completa todos los campos obligatorios');
      return;
    }

    setRoles(roles.map(role =>
      role.id === editingRole.id
        ? {
            ...role,
            name: newRole.name,
            description: newRole.description,
            permissions: newRole.permissions,
            updatedAt: new Date().toISOString(),
          }
        : role
    ));

    setEditingRole(null);
    setNewRole({ name: '', description: '', permissions: [] });
    toast.success('Rol actualizado exitosamente');
  };

  const handleDeleteRole = (roleId: string) => {
    const role = roles.find(r => r.id === roleId);
    if (role?.isSystem) {
      toast.error('No se pueden eliminar roles del sistema');
      return;
    }

    if (role?.userCount > 0) {
      toast.error('No se puede eliminar un rol que tiene usuarios asignados');
      return;
    }

    setRoles(roles.filter(r => r.id !== roleId));
    toast.success('Rol eliminado exitosamente');
  };

  const handlePermissionToggle = (permissionId: string) => {
    setNewRole(prev => ({
      ...prev,
      permissions: prev.permissions.includes(permissionId)
        ? prev.permissions.filter(id => id !== permissionId)
        : [...prev.permissions, permissionId]
    }));
  };

  const getPermissionsByCategory = (category: string) => {
    return permissions.filter(p => p.category === category);
  };

  const isCategoryFullySelected = (category: string) => {
    const categoryPermissions = getPermissionsByCategory(category);
    return categoryPermissions.every(p => newRole.permissions.includes(p.id));
  };

  const handleCategoryToggle = (category: string) => {
    const categoryPermissions = getPermissionsByCategory(category);
    const isFullySelected = isCategoryFullySelected(category);

    if (isFullySelected) {
      // Remove all permissions from this category
      setNewRole(prev => ({
        ...prev,
        permissions: prev.permissions.filter(id => 
          !categoryPermissions.some(p => p.id === id)
        )
      }));
    } else {
      // Add all permissions from this category
      const newPermissions = categoryPermissions.map(p => p.id);
      setNewRole(prev => ({
        ...prev,
        permissions: [...new Set([...prev.permissions, ...newPermissions])]
      }));
    }
  };

  const columns: Column<Role>[] = [
    {
      key: 'name',
      title: 'Rol',
      sortable: true,
      render: (_, role) => (
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
            <Shield className="w-5 h-5 text-purple-600" />
          </div>
          <div>
            <div className="font-medium text-gray-900 flex items-center space-x-2">
              <span>{role.name}</span>
              {role.isSystem && (
                <Badge variant="outline" className="text-xs">
                  Sistema
                </Badge>
              )}
            </div>
            <div className="text-sm text-gray-500">{role.description}</div>
          </div>
        </div>
      ),
    },
    {
      key: 'permissions',
      title: 'Permisos',
      render: (_, role) => (
        <div>
          <span className="text-sm font-medium">{role.permissions.length} permisos</span>
          <div className="text-xs text-gray-500 mt-1">
            {permissionCategories.map(category => {
              const categoryPerms = getPermissionsByCategory(category);
              const assignedPerms = categoryPerms.filter(p => role.permissions.includes(p.id));
              if (assignedPerms.length === 0) return null;
              
              return (
                <Badge key={category} variant="outline" className="mr-1 mb-1 text-xs">
                  {category} ({assignedPerms.length})
                </Badge>
              );
            })}
          </div>
        </div>
      ),
    },
    {
      key: 'userCount',
      title: 'Usuarios',
      sortable: true,
      align: 'center',
      render: (value) => (
        <div className="flex items-center justify-center">
          <Users className="w-4 h-4 mr-1 text-gray-400" />
          {value}
        </div>
      ),
    },
    {
      key: 'updatedAt',
      title: 'Última Modificación',
      sortable: true,
      render: (value) => new Date(value).toLocaleDateString(),
    },
    {
      key: 'actions',
      title: 'Acciones',
      align: 'right',
      render: (_, role) => (
        <div className="flex items-center space-x-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleEditRole(role)}
          >
            <Edit className="w-4 h-4" />
          </Button>
          {!role.isSystem && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleDeleteRole(role.id)}
              className="text-red-600 hover:text-red-700"
            >
              <Trash2 className="w-4 h-4" />
            </Button>
          )}
        </div>
      ),
    },
  ];

  const stats = {
    total: roles.length,
    system: roles.filter(r => r.isSystem).length,
    custom: roles.filter(r => !r.isSystem).length,
    totalUsers: roles.reduce((sum, r) => sum + r.userCount, 0),
  };

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Gestión de Roles</h1>
            <p className="text-gray-600 mt-1">
              Administra roles y permisos del sistema
            </p>
          </div>
          <Button onClick={() => setIsCreateModalOpen(true)}>
            <Plus className="w-4 h-4 mr-2" />
            Crear Rol
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0 p-3 rounded-lg bg-purple-100">
                <Shield className="h-6 w-6 text-purple-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Total Roles</p>
                <p className="text-2xl font-semibold text-gray-900">{stats.total}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0 p-3 rounded-lg bg-blue-100">
                <Settings className="h-6 w-6 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Roles del Sistema</p>
                <p className="text-2xl font-semibold text-gray-900">{stats.system}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0 p-3 rounded-lg bg-green-100">
                <Edit className="h-6 w-6 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Roles Personalizados</p>
                <p className="text-2xl font-semibold text-gray-900">{stats.custom}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0 p-3 rounded-lg bg-yellow-100">
                <Users className="h-6 w-6 text-yellow-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Usuarios Asignados</p>
                <p className="text-2xl font-semibold text-gray-900">{stats.totalUsers}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Search */}
      <Card className="mb-6">
        <CardContent className="p-6">
          <div className="relative">
            <Search className="absolute left-3 top-2.5 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Buscar roles..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 w-64"
            />
          </div>
        </CardContent>
      </Card>

      {/* Roles Table */}
      <Card>
        <CardHeader>
          <CardTitle>Lista de Roles</CardTitle>
        </CardHeader>
        <CardContent>
          <DataTable
            data={filteredRoles}
            columns={columns}
            searchable={false}
          />
        </CardContent>
      </Card>

      {/* Create/Edit Role Modal */}
      <Modal
        isOpen={isCreateModalOpen || !!editingRole}
        onClose={() => {
          setIsCreateModalOpen(false);
          setEditingRole(null);
          setNewRole({ name: '', description: '', permissions: [] });
        }}
        title={editingRole ? 'Editar Rol' : 'Crear Nuevo Rol'}
        size="xl"
      >
        <div className="space-y-6">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Nombre del Rol *
              </label>
              <Input
                placeholder="Ej: Editor de Contenido"
                value={newRole.name}
                onChange={(e) => setNewRole({ ...newRole, name: e.target.value })}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Descripción *
              </label>
              <Input
                placeholder="Descripción del rol"
                value={newRole.description}
                onChange={(e) => setNewRole({ ...newRole, description: e.target.value })}
              />
            </div>
          </div>

          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-4">
              Permisos ({newRole.permissions.length} seleccionados)
            </h3>
            
            <div className="space-y-6">
              {permissionCategories.map(category => {
                const categoryPermissions = getPermissionsByCategory(category);
                const isFullySelected = isCategoryFullySelected(category);
                const selectedCount = categoryPermissions.filter(p => 
                  newRole.permissions.includes(p.id)
                ).length;

                return (
                  <div key={category} className="border rounded-lg p-4">
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center space-x-3">
                        <h4 className="font-medium text-gray-900">{category}</h4>
                        <Badge variant="outline">
                          {selectedCount}/{categoryPermissions.length}
                        </Badge>
                      </div>
                      <Switch
                        checked={isFullySelected}
                        onCheckedChange={() => handleCategoryToggle(category)}
                      />
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                      {categoryPermissions.map(permission => (
                        <label
                          key={permission.id}
                          className="flex items-center space-x-3 cursor-pointer"
                        >
                          <input
                            type="checkbox"
                            checked={newRole.permissions.includes(permission.id)}
                            onChange={() => handlePermissionToggle(permission.id)}
                            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                          />
                          <div>
                            <div className="text-sm font-medium text-gray-900">
                              {permission.name}
                            </div>
                            <div className="text-xs text-gray-500">
                              {permission.description}
                            </div>
                          </div>
                        </label>
                      ))}
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </div>

        <ModalFooter>
          <Button
            variant="outline"
            onClick={() => {
              setIsCreateModalOpen(false);
              setEditingRole(null);
              setNewRole({ name: '', description: '', permissions: [] });
            }}
          >
            Cancelar
          </Button>
          <Button onClick={editingRole ? handleUpdateRole : handleCreateRole}>
            {editingRole ? 'Actualizar Rol' : 'Crear Rol'}
          </Button>
        </ModalFooter>
      </Modal>
    </div>
  );
}
