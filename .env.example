# Arroyo University - Environment Configuration Template
# Copy this file to .env and update with your actual values

# Database Configuration
POSTGRES_DB=arroyo_university
POSTGRES_USER=postgres
POSTGRES_PASSWORD=your-secure-postgres-password

# Redis Configuration
REDIS_PASSWORD=your-secure-redis-password

# MinIO Configuration (Object Storage)
MINIO_ROOT_USER=minioadmin
MINIO_ROOT_PASSWORD=your-secure-minio-password

# Monitoring Configuration
GRAFANA_PASSWORD=your-secure-grafana-password
GRAFANA_SECRET_KEY=your-grafana-secret-key

# Environment
ENVIRONMENT=development
DEBUG=true

# Security
JWT_SECRET_KEY=your-super-secret-jwt-key-change-in-production-minimum-32-chars
ENCRYPTION_KEY=your-32-character-encryption-key-here

# External Services API Keys
OPENAI_API_KEY=sk-your-openai-api-key-here
AZURE_SPEECH_KEY=your-azure-speech-service-key
AZURE_SPEECH_REGION=your-azure-region

# Email Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-specific-password

# Application URLs
FRONTEND_URL=http://localhost:3000
API_BASE_URL=http://localhost:80
CORE_API_URL=http://localhost:8000
AI_SERVICE_URL=http://localhost:8001
NOTIFICATION_SERVICE_URL=http://localhost:8002

# File Upload Limits
MAX_FILE_SIZE_MB=200
ALLOWED_FILE_TYPES=pdf,doc,docx,mp3,wav,mp4,avi,jpg,jpeg,png,gif

# Rate Limiting
RATE_LIMIT_PER_MINUTE=60
RATE_LIMIT_BURST=10

# Logging
LOG_LEVEL=INFO
LOG_FORMAT=json

# Push Notifications (Optional)
FCM_SERVER_KEY=your-firebase-cloud-messaging-server-key
FCM_SENDER_ID=your-fcm-sender-id
APNS_KEY_ID=your-apple-push-notification-key-id
APNS_TEAM_ID=your-apple-team-id
APNS_BUNDLE_ID=com.arroyo.university

# SMS Configuration (Optional)
TWILIO_ACCOUNT_SID=your-twilio-account-sid
TWILIO_AUTH_TOKEN=your-twilio-auth-token
TWILIO_PHONE_NUMBER=+**********

# Slack Integration (Optional)
SLACK_BOT_TOKEN=xoxb-your-slack-bot-token
SLACK_SIGNING_SECRET=your-slack-signing-secret

# Analytics (Optional)
GOOGLE_ANALYTICS_ID=GA-XXXXXXXXX
HOTJAR_ID=your-hotjar-id
SENTRY_DSN=https://<EMAIL>/project-id

# Social Login (Optional)
GOOGLE_CLIENT_ID=your-google-oauth-client-id
MICROSOFT_CLIENT_ID=your-microsoft-oauth-client-id
GITHUB_CLIENT_ID=your-github-oauth-client-id

# External Services (Optional)
TURNITIN_API_KEY=your-turnitin-api-key
GOOGLE_MAPS_API_KEY=your-google-maps-api-key
STRIPE_PUBLISHABLE_KEY=pk_test_your-stripe-publishable-key
STRIPE_SECRET_KEY=sk_test_your-stripe-secret-key

# Webhook Configuration
WEBHOOK_SECRET=your-webhook-secret-for-signature-verification

# Production-specific (uncomment for production)
# SSL_CERTIFICATE_PATH=/etc/ssl/certs/cert.pem
# SSL_PRIVATE_KEY_PATH=/etc/ssl/private/key.pem
# ENABLE_HTTPS_REDIRECT=true
# CORS_ORIGINS=https://yourdomain.com,https://www.yourdomain.com
