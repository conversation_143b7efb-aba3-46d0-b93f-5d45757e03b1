import React, { useState, useEffect } from 'react';
import { Link, useParams, useNavigate } from 'react-router-dom';
import { CheckCircle, AlertCircle, Mail, Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/Button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card';
import { authService } from '@/services/authService';
import toast from 'react-hot-toast';

type VerificationStatus = 'verifying' | 'success' | 'error' | 'expired';

export default function EmailVerificationPage() {
  const { token } = useParams<{ token: string }>();
  const navigate = useNavigate();
  const [status, setStatus] = useState<VerificationStatus>('verifying');
  const [isResending, setIsResending] = useState(false);
  const [userEmail, setUserEmail] = useState('');

  useEffect(() => {
    if (token) {
      verifyEmail(token);
    } else {
      setStatus('error');
    }
  }, [token]);

  const verifyEmail = async (verificationToken: string) => {
    try {
      await authService.verifyEmail(verificationToken);
      setStatus('success');
      toast.success('¡Email verificado exitosamente!');
      
      // Redirect to login after 3 seconds
      setTimeout(() => {
        navigate('/login');
      }, 3000);
    } catch (error: any) {
      console.error('Email verification failed:', error);
      
      if (error.response?.status === 410) {
        setStatus('expired');
      } else {
        setStatus('error');
      }
      
      // Try to extract email from error response for resend functionality
      if (error.response?.data?.email) {
        setUserEmail(error.response.data.email);
      }
    }
  };

  const handleResendVerification = async () => {
    if (!userEmail) {
      toast.error('No se pudo obtener el email. Por favor, contacta al soporte.');
      return;
    }

    setIsResending(true);
    try {
      await authService.resendEmailVerification(userEmail);
      toast.success('Email de verificación reenviado exitosamente');
    } catch (error: any) {
      toast.error(error.response?.data?.message || 'Error al reenviar el email');
    } finally {
      setIsResending(false);
    }
  };

  const renderContent = () => {
    switch (status) {
      case 'verifying':
        return (
          <Card>
            <CardHeader className="text-center">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Loader2 className="w-8 h-8 text-blue-600 animate-spin" />
              </div>
              <CardTitle className="text-blue-800">Verificando Email</CardTitle>
              <CardDescription>
                Por favor espera mientras verificamos tu email...
              </CardDescription>
            </CardHeader>
            <CardContent className="text-center">
              <p className="text-sm text-gray-600">
                Este proceso puede tomar unos segundos.
              </p>
            </CardContent>
          </Card>
        );

      case 'success':
        return (
          <Card>
            <CardHeader className="text-center">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <CheckCircle className="w-8 h-8 text-green-600" />
              </div>
              <CardTitle className="text-green-800">¡Email Verificado!</CardTitle>
              <CardDescription>
                Tu email ha sido verificado exitosamente
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="text-center">
                <p className="text-sm text-gray-600 mb-4">
                  Tu cuenta está ahora completamente activada. Puedes iniciar sesión y comenzar a usar la plataforma.
                </p>
                <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                  <p className="text-sm text-green-800">
                    Serás redirigido automáticamente al login en unos segundos...
                  </p>
                </div>
              </div>

              <div className="space-y-3">
                <Link to="/login">
                  <Button className="w-full">
                    Ir al Login
                  </Button>
                </Link>
                
                <Link to="/">
                  <Button variant="outline" className="w-full">
                    Ir al Inicio
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        );

      case 'expired':
        return (
          <Card>
            <CardHeader className="text-center">
              <div className="w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <AlertCircle className="w-8 h-8 text-yellow-600" />
              </div>
              <CardTitle className="text-yellow-800">Enlace Expirado</CardTitle>
              <CardDescription>
                El enlace de verificación ha expirado
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="text-center">
                <p className="text-sm text-gray-600 mb-4">
                  Los enlaces de verificación expiran después de 24 horas por seguridad. 
                  Puedes solicitar un nuevo enlace de verificación.
                </p>
              </div>

              <div className="space-y-3">
                {userEmail ? (
                  <Button 
                    className="w-full" 
                    onClick={handleResendVerification}
                    disabled={isResending}
                  >
                    {isResending ? (
                      <>
                        <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                        Reenviando...
                      </>
                    ) : (
                      <>
                        <Mail className="w-4 h-4 mr-2" />
                        Reenviar Verificación
                      </>
                    )}
                  </Button>
                ) : (
                  <Link to="/forgot-password">
                    <Button className="w-full">
                      <Mail className="w-4 h-4 mr-2" />
                      Solicitar Nueva Verificación
                    </Button>
                  </Link>
                )}
                
                <Link to="/login">
                  <Button variant="outline" className="w-full">
                    Volver al Login
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        );

      case 'error':
      default:
        return (
          <Card>
            <CardHeader className="text-center">
              <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <AlertCircle className="w-8 h-8 text-red-600" />
              </div>
              <CardTitle className="text-red-800">Error de Verificación</CardTitle>
              <CardDescription>
                No se pudo verificar tu email
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="text-center">
                <p className="text-sm text-gray-600 mb-4">
                  El enlace de verificación es inválido o ya ha sido utilizado. 
                  Si continúas teniendo problemas, contacta al soporte técnico.
                </p>
              </div>

              <div className="space-y-3">
                {userEmail ? (
                  <Button 
                    className="w-full" 
                    onClick={handleResendVerification}
                    disabled={isResending}
                  >
                    {isResending ? (
                      <>
                        <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                        Reenviando...
                      </>
                    ) : (
                      <>
                        <Mail className="w-4 h-4 mr-2" />
                        Reenviar Verificación
                      </>
                    )}
                  </Button>
                ) : (
                  <Link to="/forgot-password">
                    <Button className="w-full">
                      <Mail className="w-4 h-4 mr-2" />
                      Solicitar Nueva Verificación
                    </Button>
                  </Link>
                )}
                
                <Link to="/login">
                  <Button variant="outline" className="w-full">
                    Volver al Login
                  </Button>
                </Link>
              </div>

              <div className="text-center">
                <p className="text-xs text-gray-500">
                  ¿Necesitas ayuda? Contacta a tu administrador del sistema
                </p>
              </div>
            </CardContent>
          </Card>
        );
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        {/* Logo and Title */}
        <div className="text-center">
          <div className="w-16 h-16 bg-blue-600 rounded-lg flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path d="M10.394 2.08a1 1 0 00-.788 0l-7 3a1 1 0 000 1.84L5.25 8.051a.999.999 0 01.356-.257l4-1.714a1 1 0 11.788 1.84L7.667 9.088l1.94.831a1 1 0 00.787 0l7-3a1 1 0 000-1.838l-7-3z"/>
            </svg>
          </div>
          <h2 className="text-3xl font-bold text-gray-900">Arroyo University</h2>
          <p className="mt-2 text-sm text-gray-600">
            Verificación de Email
          </p>
        </div>

        {/* Content */}
        {renderContent()}
      </div>
    </div>
  );
}
