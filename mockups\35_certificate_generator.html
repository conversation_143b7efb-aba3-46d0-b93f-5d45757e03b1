<!DOCTYPE html>
<html lang="es" class="h-full bg-gray-50">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Generador de Certificados - Arroyo University</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Inter', sans-serif; }
        .certificate-preview {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: 8px solid #d4af37;
        }
    </style>
</head>
<body class="h-full">
    <div class="min-h-full">
        <!-- Navigation -->
        <nav class="bg-white shadow">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between h-16">
                    <div class="flex items-center">
                        <button onclick="window.location.href='05_content_creator_dashboard.html'" 
                                class="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center hover:bg-blue-700 transition-colors">
                            <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M10.394 2.08a1 1 0 00-.788 0l-7 3a1 1 0 000 1.84L5.25 8.051a.999.999 0 01.356-.257l4-1.714a1 1 0 11.788 1.84L7.667 9.088l1.94.831a1 1 0 00.787 0l7-3a1 1 0 000-1.838l-7-3z"/>
                            </svg>
                        </button>
                        <h1 class="ml-3 text-xl font-semibold text-gray-900">Universidad Ejemplo</h1>
                        <nav class="ml-8 flex space-x-4">
                            <a href="05_content_creator_dashboard.html" class="text-gray-500 hover:text-gray-700 px-3 py-2 text-sm font-medium">Dashboard</a>
                            <a href="21_results_dashboard.html" class="text-gray-500 hover:text-gray-700 px-3 py-2 text-sm font-medium">Resultados</a>
                            <a href="#" class="bg-blue-100 text-blue-700 px-3 py-2 rounded-md text-sm font-medium">Certificados</a>
                        </nav>
                    </div>
                    
                    <div class="flex items-center space-x-4">
                        <div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                            <span class="text-sm font-medium text-gray-700">MG</span>
                        </div>
                    </div>
                </div>
            </div>
        </nav>

        <!-- Main Content -->
        <main class="max-w-7xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
            <!-- Header -->
            <div class="mb-8">
                <div class="flex items-center justify-between">
                    <div>
                        <h2 class="text-2xl font-bold text-gray-900">Generador de Certificados</h2>
                        <p class="mt-1 text-gray-600">Crea y personaliza certificados para estudiantes exitosos</p>
                    </div>
                    
                    <div class="flex items-center space-x-4">
                        <button onclick="previewCertificate()" 
                                class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
                            </svg>
                            Vista Previa
                        </button>
                        <button onclick="generateCertificates()" 
                                class="inline-flex items-center px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-blue-600 hover:bg-blue-700">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                            </svg>
                            Generar Certificados
                        </button>
                    </div>
                </div>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <!-- Configuration Panel -->
                <div class="space-y-6">
                    <!-- Certificate Template -->
                    <div class="bg-white shadow rounded-lg">
                        <div class="px-6 py-4 border-b border-gray-200">
                            <h3 class="text-lg font-medium text-gray-900">Plantilla de Certificado</h3>
                        </div>
                        <div class="p-6">
                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Tipo de Certificado</label>
                                    <select id="certificateType" onchange="updatePreview()" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        <option value="completion">Certificado de Finalización</option>
                                        <option value="achievement" selected>Certificado de Logro</option>
                                        <option value="proficiency">Certificado de Competencia</option>
                                    </select>
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Título del Certificado</label>
                                    <input type="text" id="certificateTitle" value="Certificado de Logro en Inglés B2" onchange="updatePreview()"
                                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Texto Principal</label>
                                    <textarea id="certificateText" rows="3" onchange="updatePreview()"
                                              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">Se certifica que {STUDENT_NAME} ha completado exitosamente el curso {COURSE_NAME} alcanzando el nivel {DIFFICULTY_LEVEL} con una puntuación de {SCORE}%.</textarea>
                                </div>
                                
                                <div class="grid grid-cols-2 gap-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Firmado por</label>
                                        <input type="text" id="signedBy" value="María González" onchange="updatePreview()"
                                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Título del Firmante</label>
                                        <input type="text" id="signerTitle" value="Directora Académica" onchange="updatePreview()"
                                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Design Options -->
                    <div class="bg-white shadow rounded-lg">
                        <div class="px-6 py-4 border-b border-gray-200">
                            <h3 class="text-lg font-medium text-gray-900">Opciones de Diseño</h3>
                        </div>
                        <div class="p-6">
                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Color de Fondo</label>
                                    <div class="flex space-x-3">
                                        <button onclick="changeBackground('gradient1')" class="w-12 h-8 rounded border-2 border-gray-300" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%)"></button>
                                        <button onclick="changeBackground('gradient2')" class="w-12 h-8 rounded border-2 border-gray-300" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%)"></button>
                                        <button onclick="changeBackground('gradient3')" class="w-12 h-8 rounded border-2 border-gray-300" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)"></button>
                                        <button onclick="changeBackground('white')" class="w-12 h-8 rounded border-2 border-gray-300 bg-white"></button>
                                    </div>
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Color del Borde</label>
                                    <div class="flex space-x-3">
                                        <button onclick="changeBorder('#d4af37')" class="w-8 h-8 rounded border-2" style="background-color: #d4af37"></button>
                                        <button onclick="changeBorder('#c0392b')" class="w-8 h-8 rounded border-2" style="background-color: #c0392b"></button>
                                        <button onclick="changeBorder('#2980b9')" class="w-8 h-8 rounded border-2" style="background-color: #2980b9"></button>
                                        <button onclick="changeBorder('#27ae60')" class="w-8 h-8 rounded border-2" style="background-color: #27ae60"></button>
                                    </div>
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Incluir Logo</label>
                                    <div class="flex items-center space-x-4">
                                        <label class="flex items-center">
                                            <input type="checkbox" id="includeLogo" checked onchange="updatePreview()" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                            <span class="ml-2 text-sm text-gray-700">Logo de la institución</span>
                                        </label>
                                    </div>
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Incluir QR de Verificación</label>
                                    <div class="flex items-center space-x-4">
                                        <label class="flex items-center">
                                            <input type="checkbox" id="includeQR" checked onchange="updatePreview()" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                            <span class="ml-2 text-sm text-gray-700">Código QR para verificación</span>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Student Selection -->
                    <div class="bg-white shadow rounded-lg">
                        <div class="px-6 py-4 border-b border-gray-200">
                            <h3 class="text-lg font-medium text-gray-900">Estudiantes Elegibles</h3>
                        </div>
                        <div class="p-6">
                            <div class="space-y-4">
                                <div class="flex items-center justify-between">
                                    <span class="text-sm font-medium text-gray-700">Curso: English B2 Assessment</span>
                                    <span class="text-sm text-gray-500">Puntuación mínima: 70%</span>
                                </div>
                                
                                <div class="space-y-2">
                                    <label class="flex items-center">
                                        <input type="checkbox" checked class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                        <span class="ml-3 text-sm text-gray-700">Carlos López - B2.1 (85%)</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" checked class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                        <span class="ml-3 text-sm text-gray-700">Ana Martínez - B2.0 (78%)</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" checked class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                        <span class="ml-3 text-sm text-gray-700">Juan Silva - B2.2 (92%)</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                        <span class="ml-3 text-sm text-gray-400">María Rodríguez - B1.3 (65%) - No elegible</span>
                                    </label>
                                </div>
                                
                                <div class="pt-4 border-t">
                                    <div class="flex items-center justify-between text-sm">
                                        <span class="text-gray-700">Total seleccionados:</span>
                                        <span class="font-medium text-blue-600">3 estudiantes</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Preview Panel -->
                <div class="bg-white shadow rounded-lg">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900">Vista Previa del Certificado</h3>
                    </div>
                    <div class="p-6">
                        <div id="certificatePreview" class="certificate-preview rounded-lg p-8 text-white text-center relative">
                            <!-- Logo -->
                            <div class="absolute top-4 left-4">
                                <div class="w-12 h-12 bg-white bg-opacity-20 rounded-lg flex items-center justify-center">
                                    <span class="text-lg font-bold">UE</span>
                                </div>
                            </div>
                            
                            <!-- QR Code -->
                            <div class="absolute top-4 right-4">
                                <div class="w-12 h-12 bg-white bg-opacity-20 rounded flex items-center justify-center">
                                    <svg class="w-8 h-8" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h3a1 1 0 011 1v3a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm2 2V5h1v1H5zM3 13a1 1 0 011-1h3a1 1 0 011 1v3a1 1 0 01-1 1H4a1 1 0 01-1-1v-3zm2 2v-1h1v1H5zM13 4a1 1 0 011-1h3a1 1 0 011 1v3a1 1 0 01-1 1h-3a1 1 0 01-1-1V4zm2 2V5h1v1h-1z" clip-rule="evenodd"/>
                                    </svg>
                                </div>
                            </div>
                            
                            <!-- Certificate Content -->
                            <div class="mt-8">
                                <h1 class="text-3xl font-bold mb-6" id="previewTitle">Certificado de Logro en Inglés B2</h1>
                                
                                <div class="text-lg mb-8" id="previewText">
                                    Se certifica que <strong>Carlos López</strong> ha completado exitosamente el curso <strong>English B2 Assessment</strong> alcanzando el nivel <strong>B2.1</strong> con una puntuación de <strong>85%</strong>.
                                </div>
                                
                                <div class="flex justify-between items-end mt-12">
                                    <div class="text-left">
                                        <div class="border-t border-white border-opacity-50 pt-2">
                                            <div class="font-semibold" id="previewSigner">María González</div>
                                            <div class="text-sm opacity-75" id="previewSignerTitle">Directora Académica</div>
                                        </div>
                                    </div>
                                    
                                    <div class="text-right">
                                        <div class="border-t border-white border-opacity-50 pt-2">
                                            <div class="font-semibold">Universidad Ejemplo</div>
                                            <div class="text-sm opacity-75" id="previewDate">15 de Noviembre, 2023</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mt-4 text-center">
                            <p class="text-sm text-gray-500">
                                Tamaño: A4 (210 × 297 mm) • Formato: PDF de alta calidad
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script>
        function updatePreview() {
            const title = document.getElementById('certificateTitle').value;
            const text = document.getElementById('certificateText').value;
            const signer = document.getElementById('signedBy').value;
            const signerTitle = document.getElementById('signerTitle').value;
            
            document.getElementById('previewTitle').textContent = title;
            document.getElementById('previewText').innerHTML = text
                .replace('{STUDENT_NAME}', '<strong>Carlos López</strong>')
                .replace('{COURSE_NAME}', '<strong>English B2 Assessment</strong>')
                .replace('{CEFR_LEVEL}', '<strong>B2.1</strong>')
                .replace('{SCORE}', '<strong>85</strong>');
            document.getElementById('previewSigner').textContent = signer;
            document.getElementById('previewSignerTitle').textContent = signerTitle;
        }

        function changeBackground(type) {
            const preview = document.getElementById('certificatePreview');
            switch(type) {
                case 'gradient1':
                    preview.style.background = 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';
                    break;
                case 'gradient2':
                    preview.style.background = 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)';
                    break;
                case 'gradient3':
                    preview.style.background = 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)';
                    break;
                case 'white':
                    preview.style.background = '#ffffff';
                    preview.style.color = '#000000';
                    break;
            }
        }

        function changeBorder(color) {
            const preview = document.getElementById('certificatePreview');
            preview.style.borderColor = color;
        }

        function previewCertificate() {
            alert('Abriendo vista previa completa del certificado...');
        }

        function generateCertificates() {
            if (confirm('¿Generar certificados para los 3 estudiantes seleccionados?')) {
                alert('Generando certificados...\n\n✓ Carlos López - B2.1\n✓ Ana Martínez - B2.0\n✓ Juan Silva - B2.2\n\nLos certificados se enviarán por email automáticamente.');
            }
        }

        // Initialize preview
        updatePreview();
    </script>
</body>
</html>
