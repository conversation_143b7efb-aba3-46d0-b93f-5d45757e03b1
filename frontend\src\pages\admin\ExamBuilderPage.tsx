import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  ArrowLef<PERSON>, 
  ArrowRight, 
  Plus, 
  Trash2, 
  <PERSON>tings, 
  Eye,
  Clock,
  HelpCircle,
  CheckCircle,
  Save
} from 'lucide-react';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Badge } from '@/components/ui/Badge';
import { Switch } from '@/components/ui/Switch';
import { ProgressBar } from '@/components/ui/ProgressBar';
import { Question } from '@/services/examService';
import toast from 'react-hot-toast';

interface ExamBuilder {
  basic: {
    title: string;
    description: string;
    courseId: string;
    instructions: string;
  };
  settings: {
    timeLimit: number;
    passingScore: number;
    attempts: number;
    shuffleQuestions: boolean;
    shuffleOptions: boolean;
    showResults: 'immediate' | 'after-submission' | 'manual';
    allowReview: boolean;
    scheduledStart?: string;
    scheduledEnd?: string;
  };
  questions: Question[];
}

const mockCourses = [
  { id: '1', title: 'DevOps Fundamentals' },
  { id: '2', title: 'Introducción a la IA' },
  { id: '3', title: 'Python para Principiantes' },
];

const mockQuestions: Question[] = [
  {
    id: '1',
    type: 'multiple-choice',
    title: 'Principios de DevOps',
    content: '¿Cuál de los siguientes es un principio fundamental de DevOps?',
    options: ['Automatización', 'Colaboración', 'Monitoreo continuo', 'Todas las anteriores'],
    correctAnswer: 'Todas las anteriores',
    points: 10,
    difficulty: 'medium',
    category: 'DevOps',
    tags: ['principios'],
    createdBy: 'instructor',
    createdAt: '2024-01-01',
    updatedAt: '2024-01-01',
  },
];

export default function ExamBuilderPage() {
  const navigate = useNavigate();
  const [currentStep, setCurrentStep] = useState(1);
  const [exam, setExam] = useState<ExamBuilder>({
    basic: {
      title: '',
      description: '',
      courseId: '',
      instructions: 'Lee cuidadosamente cada pregunta antes de responder.',
    },
    settings: {
      timeLimit: 60,
      passingScore: 70,
      attempts: 1,
      shuffleQuestions: false,
      shuffleOptions: false,
      showResults: 'immediate',
      allowReview: true,
    },
    questions: [],
  });

  const [availableQuestions] = useState<Question[]>(mockQuestions);
  const [selectedQuestions, setSelectedQuestions] = useState<string[]>([]);

  const steps = [
    { id: 1, title: 'Información Básica', icon: HelpCircle },
    { id: 2, title: 'Configuración', icon: Settings },
    { id: 3, title: 'Seleccionar Preguntas', icon: CheckCircle },
    { id: 4, title: 'Revisión', icon: Eye },
  ];

  const updateBasic = (field: keyof ExamBuilder['basic'], value: string) => {
    setExam(prev => ({
      ...prev,
      basic: { ...prev.basic, [field]: value }
    }));
  };

  const updateSettings = (field: keyof ExamBuilder['settings'], value: any) => {
    setExam(prev => ({
      ...prev,
      settings: { ...prev.settings, [field]: value }
    }));
  };

  const handleQuestionToggle = (questionId: string) => {
    setSelectedQuestions(prev => 
      prev.includes(questionId)
        ? prev.filter(id => id !== questionId)
        : [...prev, questionId]
    );
  };

  const handleNext = () => {
    if (currentStep === 3) {
      // Update exam with selected questions
      const questions = availableQuestions.filter(q => selectedQuestions.includes(q.id));
      setExam(prev => ({ ...prev, questions }));
    }
    
    if (currentStep < steps.length) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleSave = async (publish = false) => {
    try {
      // Validate exam
      if (!exam.basic.title || !exam.basic.courseId) {
        toast.error('Por favor, completa la información básica');
        return;
      }

      if (exam.questions.length === 0) {
        toast.error('Debes seleccionar al menos una pregunta');
        return;
      }

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      toast.success(publish ? 'Examen publicado exitosamente' : 'Examen guardado como borrador');
      navigate('/admin/courses');
    } catch (error) {
      toast.error('Error al guardar el examen');
    }
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className="space-y-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Título del Examen *
              </label>
              <Input
                placeholder="Ej: Examen Final de DevOps"
                value={exam.basic.title}
                onChange={(e) => updateBasic('title', e.target.value)}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Curso *
              </label>
              <select
                value={exam.basic.courseId}
                onChange={(e) => updateBasic('courseId', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">Seleccionar curso</option>
                {mockCourses.map(course => (
                  <option key={course.id} value={course.id}>
                    {course.title}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Descripción
              </label>
              <textarea
                rows={3}
                placeholder="Descripción del examen (opcional)"
                value={exam.basic.description}
                onChange={(e) => updateBasic('description', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Instrucciones para los Estudiantes
              </label>
              <textarea
                rows={4}
                value={exam.basic.instructions}
                onChange={(e) => updateBasic('instructions', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>
        );

      case 2:
        return (
          <div className="space-y-6">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Tiempo Límite (minutos)
                </label>
                <Input
                  type="number"
                  min="5"
                  max="300"
                  value={exam.settings.timeLimit}
                  onChange={(e) => updateSettings('timeLimit', parseInt(e.target.value))}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Puntuación para Aprobar (%)
                </label>
                <Input
                  type="number"
                  min="0"
                  max="100"
                  value={exam.settings.passingScore}
                  onChange={(e) => updateSettings('passingScore', parseInt(e.target.value))}
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Intentos Permitidos
              </label>
              <select
                value={exam.settings.attempts}
                onChange={(e) => updateSettings('attempts', parseInt(e.target.value))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value={1}>1 intento</option>
                <option value={2}>2 intentos</option>
                <option value={3}>3 intentos</option>
                <option value={-1}>Intentos ilimitados</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Mostrar Resultados
              </label>
              <select
                value={exam.settings.showResults}
                onChange={(e) => updateSettings('showResults', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="immediate">Inmediatamente</option>
                <option value="after-submission">Después del envío</option>
                <option value="manual">Manualmente por el instructor</option>
              </select>
            </div>

            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900">Opciones Avanzadas</h3>
              
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="text-sm font-medium text-gray-900">Mezclar Preguntas</h4>
                  <p className="text-sm text-gray-500">Las preguntas aparecen en orden aleatorio</p>
                </div>
                <Switch
                  checked={exam.settings.shuffleQuestions}
                  onCheckedChange={(checked) => updateSettings('shuffleQuestions', checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <h4 className="text-sm font-medium text-gray-900">Mezclar Opciones</h4>
                  <p className="text-sm text-gray-500">Las opciones de respuesta aparecen en orden aleatorio</p>
                </div>
                <Switch
                  checked={exam.settings.shuffleOptions}
                  onCheckedChange={(checked) => updateSettings('shuffleOptions', checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <h4 className="text-sm font-medium text-gray-900">Permitir Revisión</h4>
                  <p className="text-sm text-gray-500">Los estudiantes pueden revisar sus respuestas</p>
                </div>
                <Switch
                  checked={exam.settings.allowReview}
                  onCheckedChange={(checked) => updateSettings('allowReview', checked)}
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Fecha de Inicio (opcional)
                </label>
                <Input
                  type="datetime-local"
                  value={exam.settings.scheduledStart || ''}
                  onChange={(e) => updateSettings('scheduledStart', e.target.value)}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Fecha de Fin (opcional)
                </label>
                <Input
                  type="datetime-local"
                  value={exam.settings.scheduledEnd || ''}
                  onChange={(e) => updateSettings('scheduledEnd', e.target.value)}
                />
              </div>
            </div>
          </div>
        );

      case 3:
        return (
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-medium text-gray-900">
                Seleccionar Preguntas ({selectedQuestions.length} seleccionadas)
              </h3>
              <Button variant="outline" size="sm">
                <Plus className="w-4 h-4 mr-2" />
                Crear Nueva Pregunta
              </Button>
            </div>

            <div className="space-y-4">
              {availableQuestions.map((question) => (
                <div
                  key={question.id}
                  className={`border rounded-lg p-4 cursor-pointer transition-colors ${
                    selectedQuestions.includes(question.id)
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                  onClick={() => handleQuestionToggle(question.id)}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-2">
                        <input
                          type="checkbox"
                          checked={selectedQuestions.includes(question.id)}
                          onChange={() => handleQuestionToggle(question.id)}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                        <h4 className="font-medium text-gray-900">{question.title}</h4>
                        <Badge variant="outline">{question.type}</Badge>
                        <Badge variant={question.difficulty === 'easy' ? 'green' : question.difficulty === 'medium' ? 'yellow' : 'red'}>
                          {question.difficulty}
                        </Badge>
                      </div>
                      <p className="text-sm text-gray-600 mb-2">{question.content}</p>
                      <div className="flex items-center space-x-4 text-xs text-gray-500">
                        <span>{question.points} puntos</span>
                        <span>Categoría: {question.category}</span>
                        {question.tags.length > 0 && (
                          <span>Tags: {question.tags.join(', ')}</span>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {availableQuestions.length === 0 && (
              <div className="text-center py-12">
                <HelpCircle className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  No hay preguntas disponibles
                </h3>
                <p className="text-gray-600 mb-4">
                  Crea preguntas en el banco de preguntas para poder agregarlas al examen
                </p>
                <Button>
                  <Plus className="w-4 h-4 mr-2" />
                  Ir al Banco de Preguntas
                </Button>
              </div>
            )}
          </div>
        );

      case 4:
        const totalPoints = exam.questions.reduce((sum, q) => sum + q.points, 0);
        
        return (
          <div className="space-y-6">
            <h3 className="text-lg font-medium text-gray-900">Revisión del Examen</h3>
            
            <Card>
              <CardHeader>
                <CardTitle>Información General</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <span className="text-sm text-gray-500">Título:</span>
                    <p className="font-medium">{exam.basic.title}</p>
                  </div>
                  <div>
                    <span className="text-sm text-gray-500">Curso:</span>
                    <p className="font-medium">
                      {mockCourses.find(c => c.id === exam.basic.courseId)?.title || 'No seleccionado'}
                    </p>
                  </div>
                  <div>
                    <span className="text-sm text-gray-500">Tiempo límite:</span>
                    <p className="font-medium">{exam.settings.timeLimit} minutos</p>
                  </div>
                  <div>
                    <span className="text-sm text-gray-500">Puntuación para aprobar:</span>
                    <p className="font-medium">{exam.settings.passingScore}%</p>
                  </div>
                  <div>
                    <span className="text-sm text-gray-500">Intentos permitidos:</span>
                    <p className="font-medium">
                      {exam.settings.attempts === -1 ? 'Ilimitados' : exam.settings.attempts}
                    </p>
                  </div>
                  <div>
                    <span className="text-sm text-gray-500">Total de puntos:</span>
                    <p className="font-medium">{totalPoints}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Preguntas Seleccionadas ({exam.questions.length})</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {exam.questions.map((question, index) => (
                    <div key={question.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div>
                        <span className="text-sm font-medium">
                          {index + 1}. {question.title}
                        </span>
                        <div className="flex items-center space-x-2 mt-1">
                          <Badge variant="outline" className="text-xs">{question.type}</Badge>
                          <Badge variant={question.difficulty === 'easy' ? 'green' : question.difficulty === 'medium' ? 'yellow' : 'red'} className="text-xs">
                            {question.difficulty}
                          </Badge>
                          <span className="text-xs text-gray-500">{question.points} pts</span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <div className="flex space-x-3">
              <Button
                onClick={() => handleSave(false)}
                variant="outline"
                className="flex-1"
              >
                <Save className="w-4 h-4 mr-2" />
                Guardar como Borrador
              </Button>
              <Button
                onClick={() => handleSave(true)}
                className="flex-1"
              >
                <CheckCircle className="w-4 h-4 mr-2" />
                Publicar Examen
              </Button>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center space-x-4 mb-4">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => navigate(-1)}
          >
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Constructor de Exámenes</h1>
            <p className="text-gray-600">Crea un nuevo examen paso a paso</p>
          </div>
        </div>

        {/* Progress Steps */}
        <div className="flex items-center justify-between">
          {steps.map((step, index) => {
            const Icon = step.icon;
            const isActive = currentStep === step.id;
            const isCompleted = currentStep > step.id;
            
            return (
              <div key={step.id} className="flex items-center">
                <div className={`flex items-center justify-center w-10 h-10 rounded-full border-2 ${
                  isActive 
                    ? 'border-blue-500 bg-blue-500 text-white' 
                    : isCompleted
                      ? 'border-green-500 bg-green-500 text-white'
                      : 'border-gray-300 text-gray-400'
                }`}>
                  <Icon className="w-5 h-5" />
                </div>
                <div className="ml-3">
                  <p className={`text-sm font-medium ${
                    isActive ? 'text-blue-600' : isCompleted ? 'text-green-600' : 'text-gray-500'
                  }`}>
                    {step.title}
                  </p>
                </div>
                {index < steps.length - 1 && (
                  <div className={`w-16 h-0.5 mx-4 ${
                    isCompleted ? 'bg-green-500' : 'bg-gray-300'
                  }`} />
                )}
              </div>
            );
          })}
        </div>

        <div className="mt-4">
          <ProgressBar 
            value={currentStep} 
            max={steps.length} 
            showLabel={false}
          />
        </div>
      </div>

      {/* Content */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            {React.createElement(steps[currentStep - 1].icon, { className: "w-5 h-5 mr-2" })}
            {steps[currentStep - 1].title}
          </CardTitle>
        </CardHeader>
        <CardContent>
          {renderStepContent()}
        </CardContent>
      </Card>

      {/* Navigation */}
      <div className="flex justify-between mt-8">
        <Button
          variant="outline"
          onClick={handlePrevious}
          disabled={currentStep === 1}
        >
          <ArrowLeft className="w-4 h-4 mr-2" />
          Anterior
        </Button>

        {currentStep < steps.length ? (
          <Button onClick={handleNext}>
            Siguiente
            <ArrowRight className="w-4 h-4 ml-2" />
          </Button>
        ) : (
          <div className="space-x-3">
            <Button
              variant="outline"
              onClick={() => handleSave(false)}
            >
              <Save className="w-4 h-4 mr-2" />
              Guardar Borrador
            </Button>
            <Button onClick={() => handleSave(true)}>
              <CheckCircle className="w-4 h-4 mr-2" />
              Publicar
            </Button>
          </div>
        )}
      </div>
    </div>
  );
}
