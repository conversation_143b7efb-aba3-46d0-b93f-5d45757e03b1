"""
Forum models for course discussions
"""

from datetime import datetime
from typing import Optional, List
from uuid import UUID
from enum import Enum

from sqlmodel import SQLModel, Field

from .base import TimestampMixin, TenantMixin, MetadataMixin


class PostStatus(str, Enum):
    """Post status enumeration"""
    ACTIVE = "active"
    LOCKED = "locked"
    HIDDEN = "hidden"
    DELETED = "deleted"


class ModerationAction(str, Enum):
    """Moderation action enumeration"""
    APPROVE = "approve"
    REJECT = "reject"
    LOCK = "lock"
    UNLOCK = "unlock"
    PIN = "pin"
    UNPIN = "unpin"
    HIGHLIGHT = "highlight"
    UNHIGHLIGHT = "unhighlight"


class ForumCategoryBase(SQLModel):
    """Base forum category model"""
    name: str = Field(max_length=255)
    description: Optional[str] = None
    order_index: int = Field(default=0)
    is_active: bool = Field(default=True)
    color: Optional[str] = Field(default=None, max_length=7)  # Hex color


class ForumCategory(ForumCategoryBase, TenantMixin, TimestampMixin, table=True):
    """Forum category table model"""
    __tablename__ = "forum_categories"
    
    category_id: UUID = Field(primary_key=True)
    course_id: UUID = Field(foreign_key="courses.course_id", index=True)
    created_by: UUID = Field(foreign_key="users.user_id", index=True)


class ForumCategoryCreate(ForumCategoryBase):
    """Forum category creation model"""
    course_id: UUID


class ForumCategoryUpdate(SQLModel):
    """Forum category update model"""
    name: Optional[str] = Field(default=None, max_length=255)
    description: Optional[str] = None
    order_index: Optional[int] = None
    is_active: Optional[bool] = None
    color: Optional[str] = Field(default=None, max_length=7)


class ForumCategoryResponse(ForumCategoryBase):
    """Forum category response model"""
    category_id: UUID
    course_id: UUID
    created_by: UUID
    created_at: datetime
    updated_at: datetime
    post_count: Optional[int] = None
    reply_count: Optional[int] = None
    last_activity: Optional[datetime] = None


class ForumPostBase(SQLModel):
    """Base forum post model"""
    title: str = Field(max_length=255)
    content: str
    is_pinned: bool = Field(default=False)
    is_locked: bool = Field(default=False)
    is_highlighted: bool = Field(default=False)
    status: PostStatus = Field(default=PostStatus.ACTIVE)


class ForumPost(ForumPostBase, TenantMixin, TimestampMixin, MetadataMixin, table=True):
    """Forum post table model"""
    __tablename__ = "forum_posts"
    
    post_id: UUID = Field(primary_key=True)
    category_id: UUID = Field(foreign_key="forum_categories.category_id", index=True)
    course_id: UUID = Field(foreign_key="courses.course_id", index=True)
    author_id: UUID = Field(foreign_key="users.user_id", index=True)
    tags: Optional[List[str]] = Field(default_factory=list, sa_column_kwargs={"type_": "JSONB"})
    view_count: int = Field(default=0)
    like_count: int = Field(default=0)
    reply_count: int = Field(default=0)
    last_reply_at: Optional[datetime] = None
    last_reply_by: Optional[UUID] = Field(foreign_key="users.user_id", default=None)


class ForumPostCreate(ForumPostBase):
    """Forum post creation model"""
    category_id: UUID
    tags: Optional[List[str]] = Field(default_factory=list)


class ForumPostUpdate(SQLModel):
    """Forum post update model"""
    title: Optional[str] = Field(default=None, max_length=255)
    content: Optional[str] = None
    is_pinned: Optional[bool] = None
    is_locked: Optional[bool] = None
    is_highlighted: Optional[bool] = None
    status: Optional[PostStatus] = None
    tags: Optional[List[str]] = None


class ForumPostResponse(ForumPostBase):
    """Forum post response model"""
    post_id: UUID
    category_id: UUID
    course_id: UUID
    author_id: UUID
    tags: List[str]
    view_count: int
    like_count: int
    reply_count: int
    last_reply_at: Optional[datetime] = None
    last_reply_by: Optional[UUID] = None
    created_at: datetime
    updated_at: datetime
    author_name: Optional[str] = None
    author_avatar: Optional[str] = None
    author_is_expert: Optional[bool] = None
    category_name: Optional[str] = None
    last_reply_author: Optional[str] = None
    user_has_liked: Optional[bool] = None


class ForumReplyBase(SQLModel):
    """Base forum reply model"""
    content: str
    is_solution: bool = Field(default=False)
    status: PostStatus = Field(default=PostStatus.ACTIVE)


class ForumReply(ForumReplyBase, TenantMixin, TimestampMixin, table=True):
    """Forum reply table model"""
    __tablename__ = "forum_replies"
    
    reply_id: UUID = Field(primary_key=True)
    post_id: UUID = Field(foreign_key="forum_posts.post_id", index=True)
    author_id: UUID = Field(foreign_key="users.user_id", index=True)
    parent_reply_id: Optional[UUID] = Field(foreign_key="forum_replies.reply_id", default=None)
    like_count: int = Field(default=0)


class ForumReplyCreate(ForumReplyBase):
    """Forum reply creation model"""
    post_id: UUID
    parent_reply_id: Optional[UUID] = None


class ForumReplyUpdate(SQLModel):
    """Forum reply update model"""
    content: Optional[str] = None
    is_solution: Optional[bool] = None
    status: Optional[PostStatus] = None


class ForumReplyResponse(ForumReplyBase):
    """Forum reply response model"""
    reply_id: UUID
    post_id: UUID
    author_id: UUID
    parent_reply_id: Optional[UUID] = None
    like_count: int
    created_at: datetime
    updated_at: datetime
    author_name: Optional[str] = None
    author_avatar: Optional[str] = None
    author_is_expert: Optional[bool] = None
    user_has_liked: Optional[bool] = None
    replies: Optional[List["ForumReplyResponse"]] = None


class ForumLike(SQLModel, table=True):
    """Forum like model"""
    __tablename__ = "forum_likes"
    
    like_id: UUID = Field(primary_key=True)
    user_id: UUID = Field(foreign_key="users.user_id", index=True)
    tenant_id: UUID = Field(foreign_key="tenants.tenant_id", index=True)
    post_id: Optional[UUID] = Field(foreign_key="forum_posts.post_id", default=None, index=True)
    reply_id: Optional[UUID] = Field(foreign_key="forum_replies.reply_id", default=None, index=True)
    created_at: datetime = Field(default_factory=datetime.utcnow)


class ForumLikeCreate(SQLModel):
    """Forum like creation model"""
    post_id: Optional[UUID] = None
    reply_id: Optional[UUID] = None


class ForumModeration(SQLModel, table=True):
    """Forum moderation model"""
    __tablename__ = "forum_moderation"
    
    moderation_id: UUID = Field(primary_key=True)
    post_id: Optional[UUID] = Field(foreign_key="forum_posts.post_id", default=None, index=True)
    reply_id: Optional[UUID] = Field(foreign_key="forum_replies.reply_id", default=None, index=True)
    moderator_id: UUID = Field(foreign_key="users.user_id", index=True)
    tenant_id: UUID = Field(foreign_key="tenants.tenant_id", index=True)
    action: ModerationAction
    reason: Optional[str] = Field(default=None, max_length=500)
    notes: Optional[str] = None
    created_at: datetime = Field(default_factory=datetime.utcnow)


class ForumModerationCreate(SQLModel):
    """Forum moderation creation model"""
    post_id: Optional[UUID] = None
    reply_id: Optional[UUID] = None
    action: ModerationAction
    reason: Optional[str] = Field(default=None, max_length=500)
    notes: Optional[str] = None


class ForumReport(SQLModel, table=True):
    """Forum report model"""
    __tablename__ = "forum_reports"
    
    report_id: UUID = Field(primary_key=True)
    post_id: Optional[UUID] = Field(foreign_key="forum_posts.post_id", default=None, index=True)
    reply_id: Optional[UUID] = Field(foreign_key="forum_replies.reply_id", default=None, index=True)
    reported_by: UUID = Field(foreign_key="users.user_id", index=True)
    tenant_id: UUID = Field(foreign_key="tenants.tenant_id", index=True)
    reason: str = Field(max_length=100)
    description: Optional[str] = Field(default=None, max_length=1000)
    status: str = Field(default="pending", max_length=20)
    resolved_by: Optional[UUID] = Field(foreign_key="users.user_id", default=None)
    resolved_at: Optional[datetime] = None
    created_at: datetime = Field(default_factory=datetime.utcnow)


class ForumReportCreate(SQLModel):
    """Forum report creation model"""
    post_id: Optional[UUID] = None
    reply_id: Optional[UUID] = None
    reason: str = Field(max_length=100)
    description: Optional[str] = Field(default=None, max_length=1000)


class ForumSearch(SQLModel):
    """Forum search model"""
    query: Optional[str] = None
    category_id: Optional[UUID] = None
    author_id: Optional[UUID] = None
    tags: Optional[List[str]] = None
    is_pinned: Optional[bool] = None
    is_highlighted: Optional[bool] = None
    date_from: Optional[datetime] = None
    date_to: Optional[datetime] = None


class ForumStatistics(SQLModel):
    """Forum statistics model"""
    course_id: UUID
    total_posts: int
    total_replies: int
    total_likes: int
    active_users: int
    posts_this_week: int
    replies_this_week: int
    top_contributors: List[dict]
    popular_tags: List[dict]
    category_stats: List[dict]
