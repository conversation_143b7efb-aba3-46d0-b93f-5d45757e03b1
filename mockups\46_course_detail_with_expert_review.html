<!DOCTYPE html>
<html lang="es" class="h-full bg-gray-50">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DevOps Fundamentals - Arroyo University</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Inter', sans-serif; }
    </style>
</head>
<body class="h-full">
    <div class="flex h-full">
        <!-- Sidebar -->
        <div class="hidden md:flex md:w-64 md:flex-col">
            <div class="flex flex-col flex-grow pt-5 bg-white border-r border-gray-200 overflow-y-auto">
                <!-- Logo -->
                <div class="flex items-center flex-shrink-0 px-4">
                    <div class="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                        <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M10.394 2.08a1 1 0 00-.788 0l-7 3a1 1 0 000 1.84L5.25 8.051a.999.999 0 01.356-.257l4-1.714a1 1 0 11.788 1.84L7.667 9.088l1.94.831a1 1 0 00.787 0l7-3a1 1 0 000-1.838l-7-3z"/>
                        </svg>
                    </div>
                    <h1 class="ml-3 text-lg font-semibold text-gray-900">Universidad Ejemplo</h1>
                </div>

                <!-- Navigation -->
                <nav class="mt-8 flex-1 px-2 space-y-1">
                    <a href="43_user_home_dashboard.html" class="text-gray-600 hover:bg-gray-50 hover:text-gray-900 group flex items-center px-2 py-2 text-sm font-medium rounded-md">
                        <svg class="text-gray-400 mr-3 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"/>
                        </svg>
                        Home
                    </a>

                    <a href="39_course_marketplace.html" class="text-gray-600 hover:bg-gray-50 hover:text-gray-900 group flex items-center px-2 py-2 text-sm font-medium rounded-md">
                        <svg class="text-gray-400 mr-3 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"/>
                        </svg>
                        Marketplace
                    </a>

                    <a href="#" class="bg-blue-100 text-blue-700 group flex items-center px-2 py-2 text-sm font-medium rounded-md">
                        <svg class="text-blue-500 mr-3 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"/>
                        </svg>
                        Mis Cursos
                    </a>

                    <!-- Profile Section -->
                    <div class="pt-4 mt-4 border-t border-gray-200">
                        <a href="#" class="text-gray-600 hover:bg-gray-50 hover:text-gray-900 group flex items-center px-2 py-2 text-sm font-medium rounded-md">
                            <svg class="text-gray-400 mr-3 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                            </svg>
                            Perfil
                        </a>
                    </div>
                </nav>

                <!-- User Info -->
                <div class="flex-shrink-0 flex border-t border-gray-200 p-4">
                    <div class="flex items-center">
                        <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                            <span class="text-sm font-medium text-white">AJ</span>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm font-medium text-gray-700">Alejandro</p>
                            <p class="text-xs text-gray-500">Usuario</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="flex flex-col flex-1 overflow-hidden">
            <!-- Top Header -->
            <header class="bg-white shadow-sm border-b border-gray-200">
                <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div class="flex justify-between items-center py-4">
                        <div>
                            <nav class="flex" aria-label="Breadcrumb">
                                <ol class="flex items-center space-x-4">
                                    <li>
                                        <div class="flex items-center">
                                            <a href="#" class="text-gray-400 hover:text-gray-500">
                                                <svg class="flex-shrink-0 h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                                                    <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"/>
                                                </svg>
                                            </a>
                                        </div>
                                    </li>
                                    <li>
                                        <div class="flex items-center">
                                            <svg class="flex-shrink-0 h-5 w-5 text-gray-300" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
                                            </svg>
                                            <a href="39_course_marketplace.html" class="ml-4 text-sm font-medium text-gray-500 hover:text-gray-700">Marketplace</a>
                                        </div>
                                    </li>
                                    <li>
                                        <div class="flex items-center">
                                            <svg class="flex-shrink-0 h-5 w-5 text-gray-300" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
                                            </svg>
                                            <span class="ml-4 text-sm font-medium text-gray-900">DevOps Fundamentals</span>
                                        </div>
                                    </li>
                                </ol>
                            </nav>
                        </div>
                    </div>
                </div>
            </header>

            <!-- Main Content Area -->
            <main class="flex-1 overflow-y-auto">
                <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
                    <!-- Course Header -->
                    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-8 mb-8">
                        <div class="flex items-start justify-between">
                            <div class="flex-1">
                                <h1 class="text-3xl font-bold text-gray-900 mb-4">DevOps Fundamentals</h1>
                                <p class="text-lg text-gray-600 mb-6">Aprende los fundamentos de DevOps, CI/CD y automatización de infraestructura para acelerar el desarrollo de software.</p>
                                
                                <div class="flex items-center space-x-6 mb-6">
                                    <div class="flex items-center text-sm text-gray-500">
                                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                        </svg>
                                        40 horas
                                    </div>
                                    <div class="flex items-center text-sm text-gray-500">
                                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"/>
                                        </svg>
                                        156 estudiantes
                                    </div>
                                    <div class="flex items-center text-sm text-gray-500">
                                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
                                        </svg>
                                        8 módulos
                                    </div>
                                    <div class="flex items-center text-sm text-gray-500">
                                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"/>
                                        </svg>
                                        Intermedio
                                    </div>
                                </div>
                            </div>
                            <div class="flex flex-col items-end space-y-4">
                                <button class="bg-blue-600 text-white px-6 py-3 rounded-md hover:bg-blue-700 font-medium">
                                    Inscribirse al Curso
                                </button>
                                <div class="flex items-center space-x-2">
                                    <button class="text-gray-400 hover:text-red-500">
                                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"/>
                                        </svg>
                                    </button>
                                    <button class="text-gray-400 hover:text-blue-500">
                                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z"/>
                                        </svg>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Expert Review Section -->
                    <div class="bg-gradient-to-r from-yellow-50 to-amber-50 border border-yellow-200 rounded-lg p-6 mb-8">
                        <div class="flex items-start space-x-4">
                            <div class="flex-shrink-0">
                                <div class="w-16 h-16 bg-yellow-500 rounded-full flex items-center justify-center">
                                    <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M9.664 1.319a.75.75 0 01.672 0 41.059 41.059 0 018.198 5.424.75.75 0 01-.254 1.285 31.372 31.372 0 00-7.86 ********** 0 01-.84 0 31.508 31.508 0 00-2.08-1.287V9.394c0-.244.116-.463.302-.592a35.504 35.504 0 713.305-*********** 0 00-.714-1.319 37 37 0 00-3.446 2.12A2.216 2.216 0 006 9.393v.38a31.293 31.293 0 00-4.28-1.746.75.75 0 01-.254-1.285 41.059 41.059 0 018.198-5.424zM6 11.459a29.848 29.848 0 00-2.455-1.158 41.029 41.029 0 00-.39 *********** 0 00.419.74c.528.256 1.046.53 1.554.82-.21-.899-.455-1.746-.721-2.517zM21.564 10.1a.75.75 0 00-.42-.739A47.593 47.593 0 0014 7.78v1.535a29.729 29.729 0 003.27 1.49c.348.043.696.093 1.042.15.775.129 1.477.295 2.093.49a.75.75 0 00.159-1.345z" clip-rule="evenodd"/>
                                    </svg>
                                </div>
                            </div>
                            <div class="flex-1">
                                <div class="flex items-center space-x-3 mb-3">
                                    <h3 class="text-xl font-bold text-gray-900">Calificación de Experto</h3>
                                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-yellow-100 text-yellow-800">
                                        <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M9.664 1.319a.75.75 0 01.672 0 41.059 41.059 0 018.198 5.424.75.75 0 01-.254 1.285 31.372 31.372 0 00-7.86 ********** 0 01-.84 0 31.508 31.508 0 00-2.08-1.287V9.394c0-.244.116-.463.302-.592a35.504 35.504 0 713.305-*********** 0 00-.714-1.319 37 37 0 00-3.446 2.12A2.216 2.216 0 006 9.393v.38a31.293 31.293 0 00-4.28-1.746.75.75 0 01-.254-1.285 41.059 41.059 0 018.198-5.424zM6 11.459a29.848 29.848 0 00-2.455-1.158 41.029 41.029 0 00-.39 *********** 0 00.419.74c.528.256 1.046.53 1.554.82-.21-.899-.455-1.746-.721-2.517zM21.564 10.1a.75.75 0 00-.42-.739A47.593 47.593 0 0014 7.78v1.535a29.729 29.729 0 003.27 1.49c.348.043.696.093 1.042.15.775.129 1.477.295 2.093.49a.75.75 0 00.159-1.345z" clip-rule="evenodd"/>
                                        </svg>
                                        Experta en DevOps
                                    </span>
                                </div>
                                
                                <div class="flex items-center space-x-4 mb-4">
                                    <div class="flex items-center space-x-1">
                                        <!-- 5 stars - Excellent rating -->
                                        <svg class="w-6 h-6 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                                        </svg>
                                        <svg class="w-6 h-6 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                                        </svg>
                                        <svg class="w-6 h-6 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                                        </svg>
                                        <svg class="w-6 h-6 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                                        </svg>
                                        <svg class="w-6 h-6 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                                        </svg>
                                    </div>
                                    <span class="text-2xl font-bold text-yellow-600">Excelente</span>
                                    <span class="text-sm text-gray-600">por Dr. María González</span>
                                </div>
                                
                                <div class="text-gray-700">
                                    <p class="mb-3"><strong>"Un curso excepcional que cubre todos los aspectos fundamentales de DevOps"</strong></p>
                                    <p class="mb-3">Como profesional con más de 10 años en DevOps, puedo afirmar que este curso presenta una excelente progresión desde conceptos básicos hasta implementaciones prácticas. Los módulos están bien estructurados y los ejercicios hands-on son especialmente valiosos.</p>
                                    <p class="mb-3"><strong>Fortalezas destacadas:</strong></p>
                                    <ul class="list-disc list-inside space-y-1 mb-3 text-sm">
                                        <li>Cobertura completa de CI/CD con ejemplos reales</li>
                                        <li>Excelente introducción a Docker y Kubernetes</li>
                                        <li>Casos de estudio de empresas como Netflix y Amazon</li>
                                        <li>Laboratorios prácticos bien diseñados</li>
                                    </ul>
                                    <p class="text-sm"><strong>Recomendado para:</strong> Desarrolladores, administradores de sistemas y cualquier profesional que quiera entender DevOps desde cero.</p>
                                </div>
                                
                                <div class="flex items-center justify-between mt-4 pt-4 border-t border-yellow-200">
                                    <span class="text-sm text-gray-500">Revisado el 15 de noviembre, 2023</span>
                                    <div class="flex items-center space-x-4">
                                        <button class="flex items-center text-sm text-gray-500 hover:text-gray-700">
                                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"/>
                                            </svg>
                                            23 útiles
                                        </button>
                                        <span class="text-sm text-gray-400">|</span>
                                        <span class="text-sm text-gray-500">Verificado por Admin Tenant</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Course Content -->
                    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                        <!-- Main Content -->
                        <div class="lg:col-span-2">
                            <!-- Course Description -->
                            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
                                <h2 class="text-xl font-bold text-gray-900 mb-4">Descripción del Curso</h2>
                                <div class="prose prose-gray max-w-none">
                                    <p class="mb-4">Este curso integral de DevOps te llevará desde los conceptos fundamentales hasta la implementación práctica de pipelines de CI/CD en entornos de producción. Aprenderás las herramientas y metodologías que utilizan las empresas líderes en tecnología.</p>

                                    <h3 class="text-lg font-semibold mb-2">¿Qué aprenderás?</h3>
                                    <ul class="list-disc list-inside space-y-2 mb-4">
                                        <li>Fundamentos de DevOps y cultura de colaboración</li>
                                        <li>Implementación de pipelines CI/CD con Jenkins y GitHub Actions</li>
                                        <li>Containerización con Docker y orquestación con Kubernetes</li>
                                        <li>Infrastructure as Code con Terraform</li>
                                        <li>Monitoreo y logging con Prometheus y ELK Stack</li>
                                        <li>Estrategias de deployment y rollback</li>
                                        <li>Seguridad en DevOps (DevSecOps)</li>
                                        <li>Casos de estudio reales de Netflix, Amazon y Google</li>
                                    </ul>

                                    <h3 class="text-lg font-semibold mb-2">Prerrequisitos</h3>
                                    <ul class="list-disc list-inside space-y-1 mb-4">
                                        <li>Conocimientos básicos de Linux/Unix</li>
                                        <li>Experiencia básica con línea de comandos</li>
                                        <li>Conceptos básicos de desarrollo de software</li>
                                        <li>Familiaridad con Git (recomendado)</li>
                                    </ul>
                                </div>
                            </div>

                            <!-- Course Modules -->
                            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
                                <h2 class="text-xl font-bold text-gray-900 mb-4">Módulos del Curso</h2>
                                <div class="space-y-4">
                                    <div class="border border-gray-200 rounded-lg p-4">
                                        <div class="flex items-center justify-between">
                                            <h3 class="font-semibold text-gray-900">Módulo 1: Introducción a DevOps</h3>
                                            <span class="text-sm text-gray-500">2 horas</span>
                                        </div>
                                        <p class="text-gray-600 text-sm mt-1">Cultura DevOps, principios fundamentales y beneficios organizacionales</p>
                                    </div>

                                    <div class="border border-gray-200 rounded-lg p-4">
                                        <div class="flex items-center justify-between">
                                            <h3 class="font-semibold text-gray-900">Módulo 2: Control de Versiones Avanzado</h3>
                                            <span class="text-sm text-gray-500">3 horas</span>
                                        </div>
                                        <p class="text-gray-600 text-sm mt-1">Git workflows, branching strategies y colaboración en equipo</p>
                                    </div>

                                    <div class="border border-gray-200 rounded-lg p-4">
                                        <div class="flex items-center justify-between">
                                            <h3 class="font-semibold text-gray-900">Módulo 3: Continuous Integration</h3>
                                            <span class="text-sm text-gray-500">6 horas</span>
                                        </div>
                                        <p class="text-gray-600 text-sm mt-1">Jenkins, GitHub Actions, testing automatizado y quality gates</p>
                                    </div>

                                    <div class="border border-gray-200 rounded-lg p-4">
                                        <div class="flex items-center justify-between">
                                            <h3 class="font-semibold text-gray-900">Módulo 4: Containerización con Docker</h3>
                                            <span class="text-sm text-gray-500">8 horas</span>
                                        </div>
                                        <p class="text-gray-600 text-sm mt-1">Docker fundamentals, Dockerfile, registries y best practices</p>
                                    </div>

                                    <div class="border border-gray-200 rounded-lg p-4">
                                        <div class="flex items-center justify-between">
                                            <h3 class="font-semibold text-gray-900">Módulo 5: Orquestación con Kubernetes</h3>
                                            <span class="text-sm text-gray-500">10 horas</span>
                                        </div>
                                        <p class="text-gray-600 text-sm mt-1">K8s architecture, deployments, services y scaling</p>
                                    </div>

                                    <div class="border border-gray-200 rounded-lg p-4">
                                        <div class="flex items-center justify-between">
                                            <h3 class="font-semibold text-gray-900">Módulo 6: Infrastructure as Code</h3>
                                            <span class="text-sm text-gray-500">6 horas</span>
                                        </div>
                                        <p class="text-gray-600 text-sm mt-1">Terraform, CloudFormation y gestión de infraestructura</p>
                                    </div>

                                    <div class="border border-gray-200 rounded-lg p-4">
                                        <div class="flex items-center justify-between">
                                            <h3 class="font-semibold text-gray-900">Módulo 7: Monitoreo y Observabilidad</h3>
                                            <span class="text-sm text-gray-500">3 horas</span>
                                        </div>
                                        <p class="text-gray-600 text-sm mt-1">Prometheus, Grafana, ELK Stack y alerting</p>
                                    </div>

                                    <div class="border border-gray-200 rounded-lg p-4">
                                        <div class="flex items-center justify-between">
                                            <h3 class="font-semibold text-gray-900">Módulo 8: Proyecto Final</h3>
                                            <span class="text-sm text-gray-500">2 horas</span>
                                        </div>
                                        <p class="text-gray-600 text-sm mt-1">Implementación completa de pipeline DevOps end-to-end</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Sidebar -->
                        <div class="lg:col-span-1">
                            <!-- Course Actions -->
                            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
                                <h3 class="text-lg font-semibold text-gray-900 mb-4">Acciones del Curso</h3>
                                <div class="space-y-3">
                                    <button onclick="window.location.href='45_course_forum.html'"
                                            class="w-full bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 font-medium flex items-center justify-center">
                                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"/>
                                        </svg>
                                        Ir al Foro
                                    </button>
                                    <button class="w-full border border-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-50 font-medium">
                                        Ver Contenido
                                    </button>
                                    <button class="w-full border border-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-50 font-medium">
                                        Descargar Recursos
                                    </button>
                                </div>
                            </div>

                            <!-- Course Stats -->
                            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
                                <h3 class="text-lg font-semibold text-gray-900 mb-4">Estadísticas</h3>
                                <div class="space-y-3">
                                    <div class="flex justify-between">
                                        <span class="text-gray-600">Estudiantes activos:</span>
                                        <span class="font-medium">156</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-gray-600">Tasa de finalización:</span>
                                        <span class="font-medium">87%</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-gray-600">Calificación promedio:</span>
                                        <span class="font-medium">4.8/5</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-gray-600">Tiempo promedio:</span>
                                        <span class="font-medium">38 horas</span>
                                    </div>
                                </div>
                            </div>

                            <!-- Instructor -->
                            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                                <h3 class="text-lg font-semibold text-gray-900 mb-4">Instructor</h3>
                                <div class="flex items-start space-x-3">
                                    <div class="w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center">
                                        <span class="text-white font-medium">JM</span>
                                    </div>
                                    <div>
                                        <h4 class="font-medium text-gray-900">Juan Martínez</h4>
                                        <p class="text-sm text-gray-600">Senior DevOps Engineer</p>
                                        <p class="text-sm text-gray-500 mt-1">8+ años de experiencia en DevOps y Cloud Computing. Ex-Amazon, actualmente en Microsoft Azure.</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>
</body>
</html>
