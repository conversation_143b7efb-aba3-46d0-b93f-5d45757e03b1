"""
Database configuration and session management
"""

from sqlmodel import SQLModel, create_engine, Session
from sqlalchemy.pool import <PERSON>atic<PERSON>ool

from .config import settings, DatabaseConfig


# Create database engine
engine = create_engine(
    DatabaseConfig.get_database_url(),
    **DatabaseConfig.get_engine_config(),
    echo=settings.DEBUG
)


def create_db_and_tables():
    """Create database tables"""
    SQLModel.metadata.create_all(engine)


def get_session() -> Session:
    """Get database session"""
    with Session(engine) as session:
        yield session


# Dependency for FastAPI
def get_db():
    """Database dependency for FastAPI"""
    return next(get_session())


class DatabaseManager:
    """Database manager for advanced operations"""
    
    @staticmethod
    def create_tables():
        """Create all database tables"""
        SQLModel.metadata.create_all(engine)
    
    @staticmethod
    def drop_tables():
        """Drop all database tables"""
        SQLModel.metadata.drop_all(engine)
    
    @staticmethod
    def get_engine():
        """Get database engine"""
        return engine
    
    @staticmethod
    def execute_raw_sql(sql: str, params: dict = None):
        """Execute raw SQL"""
        with Session(engine) as session:
            result = session.exec(sql, params or {})
            session.commit()
            return result
    
    @staticmethod
    def check_connection() -> bool:
        """Check database connection"""
        try:
            with Session(engine) as session:
                session.exec("SELECT 1")
                return True
        except Exception:
            return False
