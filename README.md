# Arroyo University - Educational Assessment Platform

A comprehensive multi-tenant educational assessment platform built with modern microservices architecture, featuring AI-powered content generation, real-time collaboration, and advanced analytics.

## 🏗️ Architecture Overview

The platform follows a microservices architecture with the following components:

- **Frontend SPA**: React 18 with Vite, Tailwind CSS, PWA capabilities
- **API Gateway**: Nginx-based routing, rate limiting, SSL termination
- **Core API**: FastAPI with SQLModel, handles CRUD, RBAC, multi-tenant enforcement, and integrated AI services
- **AI Integration**: OpenAI GPT-4, Azure Speech Services for question generation, scoring, and moderation
- **Notification Service**: Handles emails, webhooks, real-time notifications
- **PostgreSQL Database**: Primary database with multi-tenant support and RLS
- **Redis**: Message queue and caching
- **MinIO**: Object storage for multimedia files (local development)
- **Monitoring Stack**: Prometheus, Grafana, Loki for observability

## 🚀 Quick Start

### Prerequisites

- Docker and Docker Compose
- Node.js 18+ (for frontend development)
- Python 3.11+ (for backend development)
- Git

### Environment Setup

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd ArroyoUniversity
   ```

2. **Configure environment variables**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

3. **Start the development environment**
   ```bash
   docker-compose up -d
   ```

4. **Verify services are running**
   ```bash
   docker-compose ps
   ```

### Service URLs

- **Frontend**: http://localhost:3000
- **API Gateway**: http://localhost:80
- **Core API + AI**: http://localhost:8000
- **Notification Service**: http://localhost:8002
- **Grafana**: http://localhost:3001 (admin/admin123)
- **Prometheus**: http://localhost:9090
- **MinIO Console**: http://localhost:9001 (minioadmin/minioadmin123)

## 📁 Project Structure

```
ArroyoUniversity/
├── docker-compose.yml          # Main orchestration file
├── .env                        # Root environment configuration
├── README.md                   # This file
│
├── api-gateway/               # Nginx-based API Gateway
│   ├── Dockerfile
│   ├── .env
│   ├── nginx.conf
│   └── ssl/                   # SSL certificates
│
├── core-api/                  # Main backend API + AI services
│   ├── Dockerfile
│   ├── .env
│   ├── requirements.txt
│   └── app/                   # Application code with AI integration
│       ├── models/ai.py       # AI models and schemas
│       ├── services/ai_service.py  # AI service implementation
│       └── routers/ai_router.py    # AI API endpoints
│
├── notification-service/      # Notification handling service
│   ├── Dockerfile
│   ├── .env
│   ├── requirements.txt
│   └── app/                   # Application code (to be created)
│
├── frontend/                  # React SPA
│   ├── Dockerfile
│   ├── .env
│   ├── package.json
│   └── src/                   # Source code (to be created)
│
├── database/                  # Database configuration
│   └── init/
│       └── 01-init.sql        # Initial database schema
│
├── monitoring/                # Monitoring configuration
│   ├── prometheus/
│   │   ├── prometheus.yml
│   │   └── alert_rules.yml
│   ├── grafana/
│   │   └── provisioning/      # Grafana dashboards (to be created)
│   └── loki/
│       └── local-config.yaml
│
└── documentation/             # Project documentation
    └── mockups/               # UI mockups
```

## 🔧 Development

### Backend Services

Each backend service (core-api, notification-service) follows the same structure:

```
service/
├── app/
│   ├── main.py              # FastAPI application entry point
│   ├── config.py            # Configuration management
│   ├── database.py          # Database connection
│   ├── models/              # SQLModel models
│   ├── routers/             # API route handlers
│   ├── services/            # Business logic
│   ├── utils/               # Utility functions
│   └── tests/               # Unit tests
├── Dockerfile
├── .env
└── requirements.txt
```

### Frontend

The frontend is a React SPA with:

- **Vite** for build tooling
- **Tailwind CSS** for styling
- **React Query** for state management
- **React Router** for navigation
- **PWA** capabilities for offline support

### Database

- **PostgreSQL 15** with extensions for multi-tenant support
- **Row Level Security (RLS)** for tenant isolation
- **UUID** primary keys for all entities
- **JSONB** for flexible metadata storage

## 🔐 Security Features

- **Multi-tenant architecture** with row-level security
- **JWT-based authentication** with refresh tokens
- **Role-based access control (RBAC)**
- **Rate limiting** at API gateway level
- **Content security policies**
- **Audit logging** for critical operations

## 📊 Monitoring & Observability

- **Prometheus** for metrics collection
- **Grafana** for visualization and dashboards
- **Loki** for centralized logging
- **Health checks** for all services
- **Custom business metrics** tracking

## 🧪 Testing

Each service includes:

- **Unit tests** with pytest
- **Integration tests** for API endpoints
- **Load testing** configuration
- **Security testing** with bandit

Run tests for a specific service:
```bash
cd core-api
python -m pytest tests/
```

## 🚀 Deployment

### Development
```bash
docker-compose up -d
```

### Production
The platform is designed for Kubernetes deployment with:

- **Helm charts** for application deployment
- **Terraform** for infrastructure as code
- **CI/CD pipelines** with GitHub Actions
- **Blue-green deployments** for zero downtime

## 📝 API Documentation

- **Core API**: http://localhost:8000/docs
- **AI Service**: http://localhost:8001/docs
- **Notification Service**: http://localhost:8002/docs

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:

- Check the documentation in `/documentation`
- Review the mockups in `/mockups`
- Open an issue on GitHub
- Contact the development team

## 🔄 Next Steps

After setting up the project structure:

1. **Implement Core API** - Start with authentication and user management
2. **Build Frontend Components** - Create reusable UI components
3. **Develop AI Service** - Implement question generation and scoring
4. **Set up CI/CD** - Configure automated testing and deployment
5. **Add Monitoring** - Set up dashboards and alerting
6. **Write Tests** - Comprehensive test coverage
7. **Documentation** - API documentation and user guides
