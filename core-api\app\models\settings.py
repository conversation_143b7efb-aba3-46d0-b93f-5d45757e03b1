"""
System settings models
"""

from datetime import datetime
from typing import Optional, Dict, Any
from uuid import UUID
from enum import Enum

from sqlmodel import SQLModel, Field

from .base import TimestampMixin, TenantMixin


class SettingType(str, Enum):
    """Setting type enumeration"""
    STRING = "string"
    INTEGER = "integer"
    FLOAT = "float"
    BOOLEAN = "boolean"
    JSON = "json"
    ENCRYPTED = "encrypted"


class SettingCategory(str, Enum):
    """Setting category enumeration"""
    GENERAL = "general"
    EMAIL = "email"
    SECURITY = "security"
    NOTIFICATIONS = "notifications"
    APPEARANCE = "appearance"
    FEATURES = "features"
    INTEGRATIONS = "integrations"
    ADVANCED = "advanced"


class SystemSettingBase(SQLModel):
    """Base system setting model"""
    key: str = Field(max_length=255, unique=True)
    value: Optional[str] = None
    setting_type: SettingType = Field(default=SettingType.STRING)
    category: SettingCategory = Field(default=SettingCategory.GENERAL)
    description: Optional[str] = Field(default=None, max_length=500)
    is_public: bool = Field(default=False)  # Can be read by non-admin users
    is_required: bool = Field(default=False)
    validation_rules: Optional[Dict[str, Any]] = Field(default_factory=dict, sa_column_kwargs={"type_": "JSONB"})


class SystemSetting(SystemSettingBase, TenantMixin, TimestampMixin, table=True):
    """System setting table model"""
    __tablename__ = "system_settings"
    
    setting_id: UUID = Field(primary_key=True)
    default_value: Optional[str] = None
    updated_by: Optional[UUID] = Field(foreign_key="users.user_id", default=None)


class SystemSettingCreate(SystemSettingBase):
    """System setting creation model"""
    default_value: Optional[str] = None


class SystemSettingUpdate(SQLModel):
    """System setting update model"""
    value: Optional[str] = None
    description: Optional[str] = None
    is_public: Optional[bool] = None
    is_required: Optional[bool] = None
    validation_rules: Optional[Dict[str, Any]] = None


class SystemSettingResponse(SystemSettingBase):
    """System setting response model"""
    setting_id: UUID
    default_value: Optional[str] = None
    updated_by: Optional[UUID] = None
    created_at: datetime
    updated_at: datetime
    updated_by_name: Optional[str] = None


class SettingsBulkUpdate(SQLModel):
    """Settings bulk update model"""
    settings: Dict[str, Any]  # key -> value mapping


class SettingsExport(SQLModel):
    """Settings export model"""
    categories: Optional[list[SettingCategory]] = None
    include_encrypted: bool = Field(default=False)
    format: str = Field(default="json", max_length=10)  # json, yaml, env


class SettingsImport(SQLModel):
    """Settings import model"""
    settings: Dict[str, Any]
    overwrite_existing: bool = Field(default=False)
    validate_only: bool = Field(default=False)


class SettingsValidationResult(SQLModel):
    """Settings validation result model"""
    is_valid: bool
    errors: list[str]
    warnings: list[str]
    validated_settings: Dict[str, Any]


# Predefined system settings with their metadata
DEFAULT_SETTINGS = [
    # General Settings
    {
        "key": "site_name",
        "default_value": "Arroyo University",
        "setting_type": SettingType.STRING,
        "category": SettingCategory.GENERAL,
        "description": "Name of the educational platform",
        "is_public": True,
        "is_required": True,
        "validation_rules": {"min_length": 1, "max_length": 255}
    },
    {
        "key": "site_description",
        "default_value": "Advanced Learning and Assessment Platform",
        "setting_type": SettingType.STRING,
        "category": SettingCategory.GENERAL,
        "description": "Description of the platform",
        "is_public": True,
        "validation_rules": {"max_length": 1000}
    },
    {
        "key": "default_language",
        "default_value": "en",
        "setting_type": SettingType.STRING,
        "category": SettingCategory.GENERAL,
        "description": "Default language for the platform",
        "is_public": True,
        "validation_rules": {"choices": ["en", "es", "fr", "de", "pt"]}
    },
    {
        "key": "default_timezone",
        "default_value": "UTC",
        "setting_type": SettingType.STRING,
        "category": SettingCategory.GENERAL,
        "description": "Default timezone for the platform",
        "is_public": True,
        "validation_rules": {"timezone": True}
    },
    {
        "key": "maintenance_mode",
        "default_value": "false",
        "setting_type": SettingType.BOOLEAN,
        "category": SettingCategory.GENERAL,
        "description": "Enable maintenance mode",
        "is_public": True
    },
    {
        "key": "registration_enabled",
        "default_value": "false",
        "setting_type": SettingType.BOOLEAN,
        "category": SettingCategory.GENERAL,
        "description": "Allow user self-registration",
        "is_public": True
    },
    
    # Email Settings
    {
        "key": "smtp_host",
        "default_value": "",
        "setting_type": SettingType.STRING,
        "category": SettingCategory.EMAIL,
        "description": "SMTP server hostname",
        "validation_rules": {"hostname": True}
    },
    {
        "key": "smtp_port",
        "default_value": "587",
        "setting_type": SettingType.INTEGER,
        "category": SettingCategory.EMAIL,
        "description": "SMTP server port",
        "validation_rules": {"min": 1, "max": 65535}
    },
    {
        "key": "smtp_username",
        "default_value": "",
        "setting_type": SettingType.STRING,
        "category": SettingCategory.EMAIL,
        "description": "SMTP username"
    },
    {
        "key": "smtp_password",
        "default_value": "",
        "setting_type": SettingType.ENCRYPTED,
        "category": SettingCategory.EMAIL,
        "description": "SMTP password"
    },
    {
        "key": "from_email",
        "default_value": "<EMAIL>",
        "setting_type": SettingType.STRING,
        "category": SettingCategory.EMAIL,
        "description": "Default from email address",
        "validation_rules": {"email": True}
    },
    {
        "key": "from_name",
        "default_value": "Arroyo University",
        "setting_type": SettingType.STRING,
        "category": SettingCategory.EMAIL,
        "description": "Default from name"
    },
    {
        "key": "email_verification_required",
        "default_value": "true",
        "setting_type": SettingType.BOOLEAN,
        "category": SettingCategory.EMAIL,
        "description": "Require email verification for new users"
    },
    
    # Security Settings
    {
        "key": "password_min_length",
        "default_value": "8",
        "setting_type": SettingType.INTEGER,
        "category": SettingCategory.SECURITY,
        "description": "Minimum password length",
        "validation_rules": {"min": 6, "max": 128}
    },
    {
        "key": "password_require_uppercase",
        "default_value": "true",
        "setting_type": SettingType.BOOLEAN,
        "category": SettingCategory.SECURITY,
        "description": "Require uppercase letters in passwords"
    },
    {
        "key": "password_require_numbers",
        "default_value": "true",
        "setting_type": SettingType.BOOLEAN,
        "category": SettingCategory.SECURITY,
        "description": "Require numbers in passwords"
    },
    {
        "key": "password_require_symbols",
        "default_value": "false",
        "setting_type": SettingType.BOOLEAN,
        "category": SettingCategory.SECURITY,
        "description": "Require symbols in passwords"
    },
    {
        "key": "session_timeout_hours",
        "default_value": "24",
        "setting_type": SettingType.INTEGER,
        "category": SettingCategory.SECURITY,
        "description": "Session timeout in hours",
        "validation_rules": {"min": 1, "max": 168}
    },
    {
        "key": "max_login_attempts",
        "default_value": "5",
        "setting_type": SettingType.INTEGER,
        "category": SettingCategory.SECURITY,
        "description": "Maximum login attempts before lockout",
        "validation_rules": {"min": 3, "max": 10}
    },
    {
        "key": "two_factor_enabled",
        "default_value": "false",
        "setting_type": SettingType.BOOLEAN,
        "category": SettingCategory.SECURITY,
        "description": "Enable two-factor authentication"
    },
    
    # Feature Settings
    {
        "key": "ai_question_generation",
        "default_value": "true",
        "setting_type": SettingType.BOOLEAN,
        "category": SettingCategory.FEATURES,
        "description": "Enable AI question generation",
        "is_public": True
    },
    {
        "key": "forum_enabled",
        "default_value": "true",
        "setting_type": SettingType.BOOLEAN,
        "category": SettingCategory.FEATURES,
        "description": "Enable course forums",
        "is_public": True
    },
    {
        "key": "groups_enabled",
        "default_value": "true",
        "setting_type": SettingType.BOOLEAN,
        "category": SettingCategory.FEATURES,
        "description": "Enable study groups",
        "is_public": True
    },
    {
        "key": "certificates_enabled",
        "default_value": "false",
        "setting_type": SettingType.BOOLEAN,
        "category": SettingCategory.FEATURES,
        "description": "Enable course certificates",
        "is_public": True
    },
    {
        "key": "analytics_enabled",
        "default_value": "true",
        "setting_type": SettingType.BOOLEAN,
        "category": SettingCategory.FEATURES,
        "description": "Enable advanced analytics",
        "is_public": True
    },
]
