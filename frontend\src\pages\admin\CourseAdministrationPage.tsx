import React, { useState } from 'react';
import { Plus, Search, Filter, Edit, Trash2, Users, ThumbsUp, ThumbsDown, Flag } from 'lucide-react';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Badge } from '@/components/ui/Badge';
import { DataTable, Column } from '@/components/ui/DataTable';
import { Dropdown, DropdownItem, DropdownSeparator } from '@/components/ui/Dropdown';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/Avatar';
import { generateInitials } from '@/lib/utils';
import toast from 'react-hot-toast';

interface Course {
  id: string;
  title: string;
  category: string;
  creator: {
    id: string;
    name: string;
    avatar?: string;
  };
  collaborators: Array<{
    id: string;
    name: string;
    role: 'creator' | 'collaborator' | 'grader';
  }>;
  studentCount: number;
  rating: number;
  likes: number;
  dislikes: number;
  reports: number;
  status: 'draft' | 'published' | 'archived';
  createdAt: string;
  lastModified: string;
}

const mockCourses: Course[] = [
  {
    id: '1',
    title: 'DevOps Fundamentals',
    category: 'Tecnología',
    creator: { id: '1', name: 'Prof. García' },
    collaborators: [
      { id: '1', name: 'Prof. García', role: 'creator' },
      { id: '2', name: 'Dr. López', role: 'collaborator' },
      { id: '3', name: 'Ing. Silva', role: 'grader' }
    ],
    studentCount: 156,
    rating: 4.8,
    likes: 142,
    dislikes: 8,
    reports: 1,
    status: 'published',
    createdAt: '2024-01-01',
    lastModified: '2024-01-15',
  },
  {
    id: '2',
    title: 'Introducción a la IA',
    category: 'Tecnología',
    creator: { id: '2', name: 'Dra. Martínez' },
    collaborators: [
      { id: '2', name: 'Dra. Martínez', role: 'creator' },
      { id: '4', name: 'Prof. Chen', role: 'grader' }
    ],
    studentCount: 243,
    rating: 4.6,
    likes: 198,
    dislikes: 12,
    reports: 0,
    status: 'published',
    createdAt: '2024-01-05',
    lastModified: '2024-01-20',
  },
  {
    id: '3',
    title: 'Matemáticas Avanzadas',
    category: 'Matemáticas',
    creator: { id: '3', name: 'Prof. Ruiz' },
    collaborators: [
      { id: '3', name: 'Prof. Ruiz', role: 'creator' }
    ],
    studentCount: 89,
    rating: 4.2,
    likes: 67,
    dislikes: 15,
    reports: 3,
    status: 'draft',
    createdAt: '2024-01-10',
    lastModified: '2024-01-22',
  },
];

export default function CourseAdministrationPage() {
  const [courses, setCourses] = useState<Course[]>(mockCourses);
  const [selectedCourses, setSelectedCourses] = useState<string[]>([]);

  const handleEditCourse = (courseId: string) => {
    toast.info(`Editando curso: ${courseId}`);
  };

  const handleDeleteCourse = (courseId: string) => {
    setCourses(courses.filter(c => c.id !== courseId));
    toast.success('Curso eliminado exitosamente');
  };

  const handleBulkAction = (action: string) => {
    if (selectedCourses.length === 0) {
      toast.error('Selecciona al menos un curso');
      return;
    }

    switch (action) {
      case 'publish':
        setCourses(courses.map(c => 
          selectedCourses.includes(c.id) ? { ...c, status: 'published' as const } : c
        ));
        toast.success(`${selectedCourses.length} cursos publicados`);
        break;
      case 'archive':
        setCourses(courses.map(c => 
          selectedCourses.includes(c.id) ? { ...c, status: 'archived' as const } : c
        ));
        toast.success(`${selectedCourses.length} cursos archivados`);
        break;
      case 'delete':
        setCourses(courses.filter(c => !selectedCourses.includes(c.id)));
        toast.success(`${selectedCourses.length} cursos eliminados`);
        break;
    }
    setSelectedCourses([]);
  };

  const getStatusBadge = (status: Course['status']) => {
    const variants = {
      draft: 'yellow',
      published: 'green',
      archived: 'gray',
    } as const;

    const labels = {
      draft: 'Borrador',
      published: 'Publicado',
      archived: 'Archivado',
    };

    return (
      <Badge variant={variants[status]}>
        {labels[status]}
      </Badge>
    );
  };

  const getPopularityScore = (course: Course) => {
    const total = course.likes + course.dislikes;
    if (total === 0) return 0;
    return Math.round((course.likes / total) * 100);
  };

  const columns: Column<Course>[] = [
    {
      key: 'title',
      title: 'Curso',
      sortable: true,
      render: (_, course) => (
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
            <span className="text-blue-600 font-medium text-sm">
              {course.title.charAt(0)}
            </span>
          </div>
          <div>
            <div className="font-medium text-gray-900">{course.title}</div>
            <div className="text-sm text-gray-500">{course.category}</div>
          </div>
        </div>
      ),
    },
    {
      key: 'creator',
      title: 'Creador',
      render: (_, course) => (
        <div className="flex items-center space-x-2">
          <Avatar className="w-8 h-8">
            <AvatarImage src={course.creator.avatar} />
            <AvatarFallback className="bg-gray-500 text-white text-xs">
              {generateInitials(course.creator.name)}
            </AvatarFallback>
          </Avatar>
          <span className="text-sm">{course.creator.name}</span>
        </div>
      ),
    },
    {
      key: 'collaborators',
      title: 'Colaboradores',
      render: (_, course) => (
        <div className="flex items-center space-x-1">
          {course.collaborators.slice(0, 3).map((collab, index) => (
            <Avatar key={collab.id} className="w-6 h-6">
              <AvatarFallback className="bg-blue-500 text-white text-xs">
                {generateInitials(collab.name)}
              </AvatarFallback>
            </Avatar>
          ))}
          {course.collaborators.length > 3 && (
            <span className="text-xs text-gray-500 ml-1">
              +{course.collaborators.length - 3}
            </span>
          )}
        </div>
      ),
    },
    {
      key: 'studentCount',
      title: 'Estudiantes',
      sortable: true,
      align: 'center',
      render: (value) => (
        <div className="flex items-center justify-center">
          <Users className="w-4 h-4 mr-1 text-gray-400" />
          {value}
        </div>
      ),
    },
    {
      key: 'rating',
      title: 'Rating',
      sortable: true,
      align: 'center',
      render: (value) => (
        <div className="text-center">
          <div className="font-medium">{value}</div>
          <div className="text-xs text-gray-500">⭐</div>
        </div>
      ),
    },
    {
      key: 'popularity',
      title: 'Popularidad',
      sortable: true,
      align: 'center',
      render: (_, course) => {
        const score = getPopularityScore(course);
        return (
          <div className="text-center">
            <div className="font-medium">{score}%</div>
            <div className="flex items-center justify-center space-x-1 text-xs">
              <span className="flex items-center text-green-600">
                <ThumbsUp className="w-3 h-3 mr-1" />
                {course.likes}
              </span>
              <span className="flex items-center text-red-600">
                <ThumbsDown className="w-3 h-3 mr-1" />
                {course.dislikes}
              </span>
            </div>
          </div>
        );
      },
    },
    {
      key: 'reports',
      title: 'Reportes',
      sortable: true,
      align: 'center',
      render: (value) => (
        <div className="flex items-center justify-center">
          <Flag className={`w-4 h-4 mr-1 ${value > 0 ? 'text-red-500' : 'text-gray-400'}`} />
          {value}
        </div>
      ),
    },
    {
      key: 'status',
      title: 'Estado',
      sortable: true,
      render: (_, course) => getStatusBadge(course.status),
    },
    {
      key: 'actions',
      title: 'Acciones',
      align: 'right',
      render: (_, course) => (
        <Dropdown
          trigger={
            <Button variant="ghost" size="sm">
              <Edit className="w-4 h-4" />
            </Button>
          }
          align="right"
        >
          <DropdownItem onClick={() => handleEditCourse(course.id)}>
            <Edit className="w-4 h-4" />
            Editar
          </DropdownItem>
          <DropdownItem onClick={() => toast.info('Ver estadísticas')}>
            <Users className="w-4 h-4" />
            Ver Estadísticas
          </DropdownItem>
          <DropdownSeparator />
          <DropdownItem 
            onClick={() => handleDeleteCourse(course.id)}
            className="text-red-600"
          >
            <Trash2 className="w-4 h-4" />
            Eliminar
          </DropdownItem>
        </Dropdown>
      ),
    },
  ];

  const stats = {
    total: courses.length,
    published: courses.filter(c => c.status === 'published').length,
    draft: courses.filter(c => c.status === 'draft').length,
    totalStudents: courses.reduce((sum, c) => sum + c.studentCount, 0),
  };

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Administración de Cursos</h1>
            <p className="text-gray-600 mt-1">
              Gestiona todos los cursos de la plataforma
            </p>
          </div>
          <Button onClick={() => window.location.href = '/admin/courses/create'}>
            <Plus className="w-4 h-4 mr-2" />
            Crear Curso
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0 p-3 rounded-lg bg-blue-100">
                <Users className="h-6 w-6 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Total Cursos</p>
                <p className="text-2xl font-semibold text-gray-900">{stats.total}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0 p-3 rounded-lg bg-green-100">
                <Users className="h-6 w-6 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Publicados</p>
                <p className="text-2xl font-semibold text-gray-900">{stats.published}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0 p-3 rounded-lg bg-yellow-100">
                <Users className="h-6 w-6 text-yellow-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Borradores</p>
                <p className="text-2xl font-semibold text-gray-900">{stats.draft}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0 p-3 rounded-lg bg-purple-100">
                <Users className="h-6 w-6 text-purple-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Total Estudiantes</p>
                <p className="text-2xl font-semibold text-gray-900">{stats.totalStudents}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Bulk Actions */}
      {selectedCourses.length > 0 && (
        <Card className="mb-6 border-blue-200 bg-blue-50">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <span className="text-blue-800 font-medium">
                {selectedCourses.length} curso{selectedCourses.length > 1 ? 's' : ''} seleccionado{selectedCourses.length > 1 ? 's' : ''}
              </span>
              <div className="flex space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleBulkAction('publish')}
                >
                  Publicar
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleBulkAction('archive')}
                >
                  Archivar
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleBulkAction('delete')}
                  className="text-red-600 border-red-300 hover:bg-red-50"
                >
                  Eliminar
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Courses Table */}
      <Card>
        <CardHeader>
          <CardTitle>Lista de Cursos</CardTitle>
        </CardHeader>
        <CardContent>
          <DataTable
            data={courses}
            columns={columns}
            searchable
            searchPlaceholder="Buscar cursos..."
            exportable
            onExport={() => toast.info('Exportando cursos...')}
          />
        </CardContent>
      </Card>
    </div>
  );
}
