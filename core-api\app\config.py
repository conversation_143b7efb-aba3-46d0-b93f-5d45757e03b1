"""
Arroyo University Core API Configuration
Centralized configuration management using Pydantic Settings
"""

from pydantic_settings import BaseSettings
from pydantic import Field, validator
from typing import List, Optional
import os


class Settings(BaseSettings):
    """Application settings"""
    
    # Application
    APP_NAME: str = Field(default="Arroyo University Core API")
    APP_VERSION: str = Field(default="1.0.0")
    DEBUG: bool = Field(default=True)
    ENVIRONMENT: str = Field(default="development")
    
    # Database
    DATABASE_URL: str = Field(...)
    DATABASE_POOL_SIZE: int = Field(default=20)
    DATABASE_MAX_OVERFLOW: int = Field(default=30)
    DATABASE_POOL_TIMEOUT: int = Field(default=30)
    DATABASE_POOL_RECYCLE: int = Field(default=3600)
    
    # Redis
    REDIS_URL: str = Field(...)
    REDIS_POOL_SIZE: int = Field(default=10)
    REDIS_SOCKET_TIMEOUT: int = Field(default=5)
    REDIS_SOCKET_CONNECT_TIMEOUT: int = Field(default=5)
    
    # Security
    JWT_SECRET_KEY: str = Field(...)
    JWT_ALGORITHM: str = Field(default="HS256")
    JWT_ACCESS_TOKEN_EXPIRE_MINUTES: int = Field(default=15)
    JWT_REFRESH_TOKEN_EXPIRE_DAYS: int = Field(default=30)
    ENCRYPTION_KEY: str = Field(...)
    
    # Password Security
    PASSWORD_MIN_LENGTH: int = Field(default=8)
    PASSWORD_REQUIRE_UPPERCASE: bool = Field(default=True)
    PASSWORD_REQUIRE_LOWERCASE: bool = Field(default=True)
    PASSWORD_REQUIRE_NUMBERS: bool = Field(default=True)
    PASSWORD_REQUIRE_SPECIAL: bool = Field(default=True)
    BCRYPT_ROUNDS: int = Field(default=12)
    
    # Multi-tenant
    DEFAULT_TENANT_PLAN: str = Field(default="basic")
    MAX_USERS_PER_TENANT: int = Field(default=1000)
    MAX_COURSES_PER_TENANT: int = Field(default=100)
    MAX_STORAGE_PER_TENANT_GB: int = Field(default=10)
    
    # File Upload
    MAX_FILE_SIZE_MB: int = Field(default=200)
    ALLOWED_FILE_TYPES: str = Field(default="pdf,doc,docx,mp3,wav,mp4,avi,jpg,jpeg,png,gif")
    UPLOAD_PATH: str = Field(default="/app/uploads")
    MINIO_ENDPOINT: str = Field(default="minio:9000")
    MINIO_ACCESS_KEY: str = Field(default="minioadmin")
    MINIO_SECRET_KEY: str = Field(default="minioadmin123")
    MINIO_BUCKET_NAME: str = Field(default="arroyo-files")
    MINIO_SECURE: bool = Field(default=False)
    
    # External Services
    AI_SERVICE_URL: str = Field(default="http://ai-service:8000")
    NOTIFICATION_SERVICE_URL: str = Field(default="http://notification-service:8000")
    
    # Email
    SMTP_HOST: str = Field(default="smtp.gmail.com")
    SMTP_PORT: int = Field(default=587)
    SMTP_USER: str = Field(default="")
    SMTP_PASSWORD: str = Field(default="")
    SMTP_TLS: bool = Field(default=True)
    SMTP_SSL: bool = Field(default=False)
    FROM_EMAIL: str = Field(default="<EMAIL>")
    FROM_NAME: str = Field(default="Arroyo University")
    
    # Rate Limiting
    RATE_LIMIT_PER_MINUTE: int = Field(default=60)
    RATE_LIMIT_BURST: int = Field(default=10)
    
    # Logging
    LOG_LEVEL: str = Field(default="INFO")
    LOG_FORMAT: str = Field(default="json")
    LOG_FILE: str = Field(default="/app/logs/core-api.log")
    
    # CORS
    CORS_ORIGINS: List[str] = Field(default=["http://localhost:3000", "https://localhost:3000"])
    CORS_ALLOW_CREDENTIALS: bool = Field(default=True)
    CORS_ALLOW_METHODS: List[str] = Field(default=["GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH"])
    CORS_ALLOW_HEADERS: List[str] = Field(default=["Origin", "X-Requested-With", "Content-Type", "Accept", "Authorization"])
    
    # Monitoring
    ENABLE_METRICS: bool = Field(default=True)
    METRICS_PORT: int = Field(default=8001)
    
    # Feature Flags
    ENABLE_REGISTRATION: bool = Field(default=True)
    ENABLE_SSO: bool = Field(default=False)
    ENABLE_MFA: bool = Field(default=True)
    ENABLE_AUDIT_LOG: bool = Field(default=True)
    
    # Pagination
    DEFAULT_PAGE_SIZE: int = Field(default=20)
    MAX_PAGE_SIZE: int = Field(default=100)
    
    # Cache
    CACHE_TTL_SECONDS: int = Field(default=300)
    CACHE_MAX_SIZE: int = Field(default=1000)
    
    @validator('CORS_ORIGINS', pre=True)
    def parse_cors_origins(cls, v):
        if isinstance(v, str):
            return [origin.strip() for origin in v.split(',')]
        return v
    
    @validator('CORS_ALLOW_METHODS', pre=True)
    def parse_cors_methods(cls, v):
        if isinstance(v, str):
            return [method.strip() for method in v.split(',')]
        return v
    
    @validator('CORS_ALLOW_HEADERS', pre=True)
    def parse_cors_headers(cls, v):
        if isinstance(v, str):
            return [header.strip() for header in v.split(',')]
        return v
    
    @validator('ALLOWED_FILE_TYPES', pre=True)
    def parse_allowed_file_types(cls, v):
        if isinstance(v, str):
            return [file_type.strip() for file_type in v.split(',')]
        return v
    
    class Config:
        env_file = ".env"
        case_sensitive = True


# Create settings instance
settings = Settings()
